#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用浏览器cookie调用正确的Temu API接口
"""

import json
import time
import logging
import requests
import os
from datetime import datetime

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"correct_api_with_browser_cookies_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"正确API调用日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def extract_browser_cookies():
    """从浏览器获取cookie"""
    try:
        import undetected_chromedriver as uc
        
        logging.info("🔧 启动浏览器获取cookie...")
        
        # 创建用户数据目录
        user_data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "chrome_user_data")
        
        # 配置Chrome选项
        options = uc.ChromeOptions()
        options.add_argument(f'--user-data-dir={user_data_dir}')
        options.add_argument('--profile-directory=Default')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--headless')  # 无头模式，更快
        
        # 创建浏览器
        driver = uc.Chrome(options=options, version_main=137)
        
        # 访问Temu主页确保cookie加载
        driver.get("https://www.temu.com")
        time.sleep(3)
        
        # 获取所有cookie
        cookies = driver.get_cookies()
        logging.info(f"📥 获取到 {len(cookies)} 个cookie")
        
        # 转换为requests格式
        cookie_dict = {}
        for cookie in cookies:
            cookie_dict[cookie['name']] = cookie['value']
        
        # 获取User-Agent
        user_agent = driver.execute_script("return navigator.userAgent;")
        
        driver.quit()
        
        logging.info("✅ 成功获取浏览器cookie和User-Agent")
        return cookie_dict, user_agent
        
    except Exception as e:
        logging.error(f"❌ 获取浏览器cookie失败: {str(e)}")
        return None, None

def create_temu_api_session(cookies, user_agent):
    """创建带有浏览器cookie的Temu API会话"""
    try:
        session = requests.Session()
        
        # 设置cookie
        for name, value in cookies.items():
            session.cookies.set(name, value, domain='.temu.com')
        
        # 设置headers，模拟真实浏览器请求
        session.headers.update({
            'User-Agent': user_agent,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://www.temu.com/',
            'Origin': 'https://www.temu.com',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Content-Type': 'application/json'
        })
        
        logging.info("✅ Temu API会话创建成功")
        return session
        
    except Exception as e:
        logging.error(f"❌ 创建Temu API会话失败: {str(e)}")
        return None

def call_temu_mall_api_with_browser_cookies(session, mall_id):
    """使用浏览器cookie调用正确的Temu商店API"""
    try:
        logging.info(f"🎯 使用浏览器cookie调用Temu商店API: {mall_id}")
        
        # 使用正确的API端点
        api_url = "https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList"
        
        # 使用正确的payload格式
        payload = {
            "mallId": mall_id,
            "mainGoodsIds": ["1"],
            "source_page_sn": "10013",
            "mall_id": mall_id,
            "main_goods_ids": ["1"],
            "filter_items": "",
            "page_number": 1,
            "page_size": 8,
            "list_id": "r7oe7gyw0vd5xo2z2qja2",
            "scene_code": "mall_rule",
            "page_sn": 10040,
            "page_el_sn": 201265,
            "source": 10018,
            "source_page_id": "10040_1750392257867_ulwao19zgq"
        }
        
        logging.info(f"📡 调用API: {api_url}")
        logging.info(f"📦 请求payload: {json.dumps(payload, ensure_ascii=False)}")
        
        # 发送POST请求
        response = session.post(api_url, json=payload, timeout=30)
        
        logging.info(f"📊 响应状态码: {response.status_code}")
        logging.info(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                logging.info(f"✅ API调用成功！数据长度: {len(str(data))} 字符")
                
                # 保存成功的响应
                filename = f"temu_api_success_{mall_id}_{datetime.now().strftime('%H%M%S')}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                logging.info(f"📁 响应数据已保存: {filename}")
                
                # 分析响应数据结构
                analyze_temu_response(data)
                
                return True, data, filename
                
            except json.JSONDecodeError:
                logging.warning(f"⚠️ 响应不是有效的JSON格式")
                logging.info(f"📄 响应内容: {response.text[:500]}...")
                return False, response.text, None
                
        elif response.status_code == 429:
            logging.warning(f"⚠️ API被限流 (429)")
            logging.info(f"📄 响应内容: {response.text[:200]}...")
            return False, response.text, None
            
        elif response.status_code == 403:
            logging.warning(f"⚠️ API被禁止访问 (403)")
            logging.info(f"📄 响应内容: {response.text[:200]}...")
            return False, response.text, None
            
        else:
            logging.warning(f"⚠️ API返回状态码: {response.status_code}")
            logging.info(f"📄 响应内容: {response.text[:500]}...")
            return False, response.text, None
        
    except requests.exceptions.Timeout:
        logging.warning(f"⚠️ API请求超时")
        return False, "Timeout", None
    except requests.exceptions.RequestException as e:
        logging.warning(f"⚠️ API请求失败: {str(e)}")
        return False, str(e), None
    except Exception as e:
        logging.error(f"❌ 调用Temu API失败: {str(e)}")
        return False, str(e), None

def analyze_temu_response(data):
    """分析Temu API响应数据结构"""
    try:
        logging.info("🔍 分析Temu API响应数据结构...")
        
        if isinstance(data, dict):
            # 检查顶级字段
            top_level_keys = list(data.keys())
            logging.info(f"📋 顶级字段: {top_level_keys}")
            
            # 查找商店信息
            if 'result' in data:
                result = data['result']
                if isinstance(result, dict):
                    result_keys = list(result.keys())
                    logging.info(f"📋 result字段: {result_keys}")
                    
                    # 查找商店名称
                    if 'mallInfo' in result:
                        mall_info = result['mallInfo']
                        if isinstance(mall_info, dict) and 'mallName' in mall_info:
                            logging.info(f"🏪 商店名称: {mall_info['mallName']}")
                    
                    # 查找商品列表
                    if 'goodsList' in result:
                        goods_list = result['goodsList']
                        if isinstance(goods_list, list):
                            logging.info(f"📦 商品数量: {len(goods_list)}")
                            if goods_list:
                                first_product = goods_list[0]
                                if isinstance(first_product, dict):
                                    product_keys = list(first_product.keys())
                                    logging.info(f"📋 商品字段: {product_keys[:10]}...")  # 只显示前10个字段
            
            # 检查错误信息
            if 'errorCode' in data:
                logging.warning(f"⚠️ API返回错误码: {data['errorCode']}")
            if 'errorMsg' in data:
                logging.warning(f"⚠️ API返回错误信息: {data['errorMsg']}")
        
    except Exception as e:
        logging.warning(f"⚠️ 分析响应数据时出错: {str(e)}")

def main():
    """主函数"""
    logging.info("="*60)
    logging.info("开始使用浏览器cookie调用正确的Temu API接口")
    logging.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info("="*60)
    
    try:
        # 获取浏览器cookie
        cookies, user_agent = extract_browser_cookies()
        if not cookies:
            logging.error("❌ 无法获取浏览器cookie")
            return False
        
        logging.info(f"🍪 获取到 {len(cookies)} 个cookie")
        logging.info(f"🌐 User-Agent: {user_agent[:100]}...")
        
        # 显示关键cookie
        key_cookies = ['AccessToken', 'user_uin', '_bee', 'api_uid', 'verifyAuthToken']
        found_key_cookies = [name for name in key_cookies if name in cookies]
        logging.info(f"🔑 找到关键cookie: {found_key_cookies}")
        
        # 创建API会话
        session = create_temu_api_session(cookies, user_agent)
        if not session:
            logging.error("❌ 无法创建API会话")
            return False
        
        # 测试目标商店API
        target_mall_id = "634418212233370"
        success, response_data, filename = call_temu_mall_api_with_browser_cookies(session, target_mall_id)
        
        # 汇总结果
        print("\n" + "="*60)
        print("🎉 正确Temu API测试结果")
        print("="*60)
        
        if success:
            print("✅ API调用成功！")
            print(f"📁 数据文件: {filename}")
            print(f"📊 数据长度: {len(str(response_data))} 字符")
            
            # 尝试提取关键信息
            if isinstance(response_data, dict) and 'result' in response_data:
                result = response_data['result']
                if 'mallInfo' in result and 'mallName' in result['mallInfo']:
                    print(f"🏪 商店名称: {result['mallInfo']['mallName']}")
                if 'goodsList' in result:
                    print(f"📦 商品数量: {len(result['goodsList'])}")
        else:
            print("❌ API调用失败")
            print(f"📄 错误信息: {str(response_data)[:200]}...")
        
        print("="*60)
        
        return success
        
    except Exception as e:
        logging.error(f"程序执行过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 使用浏览器cookie的正确Temu API调用测试成功！")
        else:
            print("\n⚠️ 使用浏览器cookie的正确Temu API调用测试失败")
    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
