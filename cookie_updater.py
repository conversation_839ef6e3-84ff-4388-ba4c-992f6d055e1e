import requests
import json
import os
import logging
import time
from datetime import datetime
import traceback

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_file = os.path.join(log_dir, f"cookie_updater_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )

    logging.info(f"日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 从cookie.json文件加载cookie
def load_cookies_from_file():
    """从cookie.json文件加载cookie信息"""
    try:
        cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        logging.info(f"尝试从 {cookie_file_path} 加载cookie")
        
        if not os.path.exists(cookie_file_path):
            logging.warning(f"警告: cookie文件不存在: {cookie_file_path}")
            return None
            
        with open(cookie_file_path, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
            
        logging.info(f"成功加载cookie，包含 {len(cookies_data)} 个条目")
        return cookies_data
    except Exception as e:
        logging.error(f"加载cookie文件时出错: {str(e)}")
        return None

# 保存cookie到文件
def save_cookies_to_file(cookies_data):
    """保存cookie到cookie.json文件"""
    try:
        cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        logging.info(f"保存cookie到 {cookie_file_path}")
        
        with open(cookie_file_path, 'w', encoding='utf-8') as f:
            json.dump(cookies_data, f, ensure_ascii=False, indent=2)
            
        logging.info(f"成功保存cookie，包含 {len(cookies_data)} 个条目")
        return True
    except Exception as e:
        logging.error(f"保存cookie文件时出错: {str(e)}")
        return False

# 将字符串cookie转换为浏览器cookie格式
def convert_cookie_string_to_json(cookie_string):
    """将字符串格式的cookie转换为JSON格式"""
    cookies = []
    
    # 分割cookie字符串
    cookie_items = cookie_string.split('; ')
    
    for item in cookie_items:
        if '=' in item:
            name, value = item.split('=', 1)
            cookie = {
                "domain": ".temu.com",  # 默认域名
                "expirationDate": None,
                "hostOnly": False,
                "httpOnly": False,
                "name": name,
                "path": "/",
                "sameSite": None,
                "secure": True,
                "session": False,
                "storeId": None,
                "value": value
            }
            cookies.append(cookie)
    
    return cookies

# 通过API登录获取新cookie
def update_cookies_via_api():
    """通过您提供的登录接口更新cookie"""
    logging.info("开始通过登录接口更新cookie")

    url = "https://seller.kuajingmaihuo.com/bg/quiet/api/mms/login"

    # 使用您提供的完整cookie字符串
    cookie_str = "api_uid=CmjlgWfaQOkSjQBRU/2IAg==; _nano_fp=XpmYX5Xyn5molpdqnC_FmUeycabCkkUsSkR8VkU2; _bee=PhyEdE3F56YBtPsKSw5T5IqRIWrrpamh; _f77=b402e972-423e-494b-88d1-bc02aef62807; _a42=478e04b1-f5ef-4a3b-837c-1bbbe5b1e274; rckk=PhyEdE3F56YBtPsKSw5T5IqRIWrrpamh; ru1k=b402e972-423e-494b-88d1-bc02aef62807; ru2k=478e04b1-f5ef-4a3b-837c-1bbbe5b1e274; SUB_PASS_ID=eyJ0IjoiOUNTdXJVc2lSUkxYcTdpQUZQWEMvZTl2VEtOUko5NTAyZEtpRkw1Z3pzVDRucFEyaExSS0F2ZDNzemduYk9HTiIsInYiOjEsInMiOjEwMDAwLCJ1IjoyMzg0NzI4NjQ1MjQ1N30="

    # 使用您提供的完整headers
    headers = {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
        "anti-content": "0aqAfqsyscPssgE2Z4Y7N6Xi-pTTaNeyyhO-0crMiFVgDw-qjCjGrohR4pUrHCrYczi8B3QtxThJksvg3XFF-tLTP6C2Dh9Fv1TGysTi20w536uTq6LnSqQNZ0rYUSZrRnaccCPCWXr_oe3AGNX3lMAtZwcRGC91gwQadZyG6y02Vb9ldvWVxBySrbBFRCWkVxMyTjI0sL5LiTKWg2ZdqlaUoLCVh6yTq2WIdgETuAKgcx4oR0Rj_XCJZ0RN8XCJisY5RzREwVDGXNuV0cKFwLg27TPWE3lygQa218-w8gvWdeWVTJy6py2e3b9REpo6dvZy7uV45JErNgXzHwQaxfLapb0HiB_WT3zZYFQqyc9i1kG8wdChWOSDaHrF1MX-prPOM-eyR_ddNjtQEIwy4d-MpzuXvnSnplVBBpZ1WrGcEzukioIta_WYNir2ErJAa_ZjjQ-iSXhHjoClGZCgeKGcAGticjo1Dq_rr7-YgDYVtf1gOO37EaUIVVxNJ5kkTKYD1WpvmJC9ARHeStNH3t1OqxnUO1uP_70m-LZyNOYU-2ihG6V9tX1IOamFzlIzmFgF5-p-jFJf-Y-xDwfJvft8za1lgwGrx93XziahI-eNvbUy6-EUNU3MnTvdnvPhFjcsuHv5tuEphegGc9ViCE-aa1JBl6loSqqXmK6yyjm7r4VlwLtuT7DPxAHZZc6z2IO-pGU8vVMXjzsqG6XRtL9JIX-Kcj_qYBjHkW-dBhgiwqc_ipdpBLucCyQvnUbsRToCNDUhSE5e2OZ",
        "cache-control": "max-age=0",
        "content-type": "application/json",
        "cookie": cookie_str,
        "origin": "https://seller.kuajingmaihuo.com",
        "priority": "u=1, i",
        "referer": "https://seller.kuajingmaihuo.com/login",
        "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }

    # 使用您提供的登录数据
    data = {
        "loginName": "13802913944",
        "encryptPassword": "K8Y/tIckJgh0fJsR5rAPR7GU8qTKwCXZylHH8nOpt1dlIa8KRGDVpvM0urkSSDyfZiOMf6FowvidYaecXFdu5I0qF2wssiYKSj1I+7smiBVFvrdxiF1kp1ZYKccL9CI4CQHiesuL909Q6LiPqGGka13oySZbMUcermeR3dmutIo=",
        "keyVersion": "1"
    }
    
    try:
        # 发送登录请求
        logging.info("发送登录请求...")
        logging.info(f"请求URL: {url}")
        logging.info(f"请求数据: {json.dumps(data, ensure_ascii=False)}")

        response = requests.post(url, json=data, headers=headers, timeout=30)

        logging.info(f"登录请求状态码: {response.status_code}")
        logging.info(f"响应头: {dict(response.headers)}")

        if response.status_code == 200:
            try:
                result = response.json()
                logging.info(f"登录响应: {json.dumps(result, ensure_ascii=False)}")

                # 检查登录是否成功
                if result.get("success") or result.get("code") == 0:
                    logging.info("登录成功！")

                    # 获取响应中的cookie
                    response_cookies = response.cookies
                    if response_cookies:
                        logging.info(f"从响应中获取到 {len(response_cookies)} 个cookie")

                        # 将cookie转换为所需格式并保存
                        formatted_cookies = []
                        for cookie in response_cookies:
                            cookie_dict = {
                                "domain": cookie.domain if cookie.domain else ".kuajingmaihuo.com",
                                "expirationDate": None,
                                "hostOnly": not cookie.domain.startswith('.') if cookie.domain else True,
                                "httpOnly": cookie.has_nonstandard_attr('HttpOnly') if hasattr(cookie, 'has_nonstandard_attr') else False,
                                "name": cookie.name,
                                "path": cookie.path if cookie.path else "/",
                                "sameSite": None,
                                "secure": cookie.secure,
                                "session": False,
                                "storeId": None,
                                "value": cookie.value
                            }
                            formatted_cookies.append(cookie_dict)

                        if formatted_cookies:
                            save_cookies_to_file(formatted_cookies)
                            logging.info("成功保存新的cookie")
                            return True
                    else:
                        logging.warning("响应中没有cookie，但登录成功")
                        return True
                else:
                    error_msg = result.get('errorMessage') or result.get('message') or '未知错误'
                    logging.error(f"登录失败: {error_msg}")
                    return False
            except Exception as e:
                logging.error(f"解析响应JSON时出错: {str(e)}")
                logging.info(f"响应内容: {response.text[:500]}...")
                return False
        else:
            logging.error(f"登录请求失败: {response.status_code}")
            logging.info(f"响应内容: {response.text[:500]}...")
            return False


    except Exception as e:
        logging.error(f"更新cookie时发生异常: {str(e)}")
        traceback.print_exc()
        return False

# 从Temu网站手动获取cookie
def update_cookies_from_temu():
    """直接访问Temu网站获取cookie"""
    logging.info("开始直接从Temu获取cookie")
    
    url = "https://www.temu.com/"
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "max-age=0",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1"
    }
    
    try:
        # 创建session来保存cookie
        session = requests.Session()
        
        # 加载现有cookie
        current_cookies = load_cookies_from_file()
        if current_cookies:
            for cookie in current_cookies:
                session.cookies.set(
                    name=cookie.get('name'),
                    value=cookie.get('value'),
                    domain=cookie.get('domain', '.temu.com'),
                    path=cookie.get('path', '/')
                )
        
        # 发送请求
        logging.info("访问Temu网站...")
        response = session.get(url, headers=headers)
        
        logging.info(f"访问状态码: {response.status_code}")
        
        if response.status_code == 200:
            logging.info("成功访问Temu网站")
            
            # 获取session中的cookie
            cookies = session.cookies.get_dict()
            logging.info(f"获取到 {len(cookies)} 个cookie")
            
            # 将cookie转换为所需格式
            formatted_cookies = []
            for name, value in cookies.items():
                cookie = {
                    "domain": ".temu.com",
                    "expirationDate": None,
                    "hostOnly": False,
                    "httpOnly": False,
                    "name": name,
                    "path": "/",
                    "sameSite": None,
                    "secure": True,
                    "session": False,
                    "storeId": None,
                    "value": value
                }
                formatted_cookies.append(cookie)
            
            # 保存cookie到文件
            if formatted_cookies:
                save_cookies_to_file(formatted_cookies)
                logging.info("cookie更新成功！")
                return True
            else:
                logging.error("未获取到有效cookie")
                return False
        else:
            logging.error(f"访问Temu网站失败: {response.status_code}")
            return False
            
    except Exception as e:
        logging.error(f"更新cookie时发生异常: {str(e)}")
        return False

def check_cookie_validity():
    """检查cookie是否有效"""
    logging.info("检查cookie有效性...")

    # 尝试使用当前cookie访问一个测试接口
    test_url = "https://seller.kuajingmaihuo.com/login"

    try:
        current_cookies = load_cookies_from_file()
        if not current_cookies:
            logging.warning("没有找到cookie文件")
            return False

        # 将cookie转换为字符串格式
        cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in current_cookies if cookie.get('name') and cookie.get('value')])

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Cookie": cookie_str
        }

        response = requests.get(test_url, headers=headers, timeout=10)
        logging.info(f"Cookie测试状态码: {response.status_code}")

        # 如果返回200且不是登录页面，说明cookie有效
        if response.status_code == 200:
            # 简单检查是否包含登录相关内容
            if "login" in response.text.lower() and "password" in response.text.lower():
                logging.info("Cookie已过期，需要重新登录")
                return False
            else:
                logging.info("Cookie仍然有效")
                return True
        else:
            logging.warning(f"Cookie测试失败: {response.status_code}")
            return False

    except Exception as e:
        logging.error(f"检查cookie有效性时出错: {str(e)}")
        return False

def main():
    """主函数"""
    # 初始化日志
    setup_logging()

    logging.info("="*50)
    logging.info("开始Cookie更新程序")
    logging.info("="*50)

    # 首先检查当前cookie是否有效
    if check_cookie_validity():
        logging.info("当前cookie仍然有效，无需更新")
        return True

    # Cookie无效，尝试更新
    logging.info("Cookie已过期，开始更新...")

    # 通过登录接口更新cookie
    if update_cookies_via_api():
        logging.info("成功通过登录接口更新cookie")

        # 再次验证新cookie是否有效
        if check_cookie_validity():
            logging.info("新cookie验证成功")
            return True
        else:
            logging.warning("新cookie验证失败")
            return False
    else:
        logging.error("通过登录接口更新cookie失败")
        return False

    logging.info("="*50)
    logging.info("Cookie更新过程完成")
    logging.info("="*50)

if __name__ == "__main__":
    main() 