#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie系统测试脚本
测试cookie自动更新功能是否正常工作
"""

import os
import sys
import json
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入主程序的函数
from a import (
    setup_logging,
    load_cookies_from_file,
    get_temu_headers,
    update_cookies_if_needed
)

def test_cookie_loading():
    """测试cookie加载功能"""
    print("\n=== 测试Cookie加载功能 ===")
    
    cookies = load_cookies_from_file()
    if cookies:
        print(f"✓ 成功加载cookie，包含 {len(cookies.split(';'))} 个条目")
        print(f"Cookie预览: {cookies[:100]}...")
        return True
    else:
        print("✗ Cookie加载失败")
        return False

def test_headers_generation():
    """测试请求头生成功能"""
    print("\n=== 测试请求头生成功能 ===")
    
    headers = get_temu_headers()
    if headers:
        print("✓ 成功生成请求头")
        print(f"请求头包含字段: {list(headers.keys())}")
        return True
    else:
        print("✗ 请求头生成失败")
        return False

def test_cookie_updater():
    """测试cookie更新功能"""
    print("\n=== 测试Cookie更新功能 ===")
    
    result = update_cookies_if_needed()
    if result:
        print("✓ Cookie更新程序运行成功")
        return True
    else:
        print("✗ Cookie更新程序运行失败")
        return False

def test_cookie_file_structure():
    """测试cookie文件结构"""
    print("\n=== 测试Cookie文件结构 ===")
    
    cookie_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
    
    if not os.path.exists(cookie_file):
        print("✗ Cookie文件不存在")
        return False
    
    try:
        with open(cookie_file, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
        
        if not isinstance(cookies_data, list):
            print("✗ Cookie文件格式错误：应该是数组格式")
            return False
        
        required_fields = ['name', 'value', 'domain']
        valid_cookies = 0
        
        for cookie in cookies_data:
            if all(field in cookie for field in required_fields):
                valid_cookies += 1
        
        print(f"✓ Cookie文件结构正确")
        print(f"总cookie数: {len(cookies_data)}")
        print(f"有效cookie数: {valid_cookies}")
        
        return valid_cookies > 0
        
    except Exception as e:
        print(f"✗ 读取cookie文件时出错: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("="*60)
    print("Cookie系统测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # 设置日志
    setup_logging()
    
    # 运行所有测试
    tests = [
        ("Cookie文件结构", test_cookie_file_structure),
        ("Cookie加载", test_cookie_loading),
        ("请求头生成", test_headers_generation),
        ("Cookie更新程序", test_cookie_updater),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试异常: {test_name} - {str(e)}")
    
    print("\n" + "="*60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Cookie系统工作正常")
        return True
    else:
        print("⚠️  部分测试失败，请检查系统配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
