2025-06-20 10:08:44,524 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250620_100844.log
2025-06-20 10:08:44,526 [INFO] 真实用户行为模拟日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_realistic_20250620_100844.log
2025-06-20 10:08:44,526 [INFO] ============================================================
2025-06-20 10:08:44,526 [INFO] 开始运行Temu数据抓取 - 真实用户行为完全模拟
2025-06-20 10:08:46,614 [INFO] 时间: 2025-06-20 10:08:46
2025-06-20 10:08:46,615 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_realistic_20250620_100844.log
2025-06-20 10:08:46,615 [INFO] ============================================================
2025-06-20 10:08:46,615 [INFO] 模拟流程：
2025-06-20 10:08:46,615 [INFO] 1. 访问商店页面（如真实用户）
2025-06-20 10:08:46,615 [INFO] 2. 模拟浏览和操作
2025-06-20 10:08:46,615 [INFO] 3. 提取页面参数
2025-06-20 10:08:46,615 [INFO] 4. 调用API接口
2025-06-20 10:08:46,615 [INFO] 5. 处理和保存数据
2025-06-20 10:08:46,615 [INFO] 
获取商店ID列表...
2025-06-20 10:08:46,615 [INFO] 请求商店ID列表: http://172.25.165.28:8055/items/shop_data
2025-06-20 10:08:46,679 [INFO] 获取商店ID状态码: 200
2025-06-20 10:08:46,680 [INFO] 获取到的商店数据: {
  "data": [
    {
      "mall_id": "634418212233370"
    },
    {
      "mall_id": "634418221321199"
    },
    {
      "mall_id": "634418221704901"
    },
    {
      "mall_id": "634418213167233"
    }
  ]
}...
2025-06-20 10:08:46,680 [INFO] 获取到的全部mall_id数量: 4
2025-06-20 10:08:46,680 [INFO] 去重后的mall_id数量: 4
2025-06-20 10:08:46,680 [INFO] 去除了 0 个重复的mall_id
2025-06-20 10:08:46,680 [INFO] 最终使用的商店ID列表: ['634418212233370', '634418221321199', '634418213167233', '634418221704901']
2025-06-20 10:08:46,681 [INFO] 真实用户行为测试，处理 3 个商店
2025-06-20 10:08:46,681 [INFO] 
==================================================
2025-06-20 10:08:46,681 [INFO] 正在处理商店 1/3: mall_id 634418212233370
2025-06-20 10:08:46,681 [INFO] ==================================================
2025-06-20 10:08:46,681 [INFO] 开始真实用户行为模拟: mall_id=634418212233370
2025-06-20 10:08:46,681 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-06-20 10:08:46,690 [INFO] 成功加载cookie，包含 20 个条目
2025-06-20 10:08:46,690 [INFO] 访问商店页面: https://www.temu.com/mall.html?mall_id=634418212233370
2025-06-20 10:08:47,289 [INFO] 商店页面访问状态码: 200
2025-06-20 10:08:47,289 [INFO] ✅ 成功访问商店页面
2025-06-20 10:08:47,295 [INFO] 模拟浏览页面 10.8 秒
2025-06-20 10:08:58,111 [INFO] 真实用户延迟 4.25 秒 - 模拟用户页面操作
2025-06-20 10:09:02,367 [INFO] 发送API请求: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList
2025-06-20 10:09:02,682 [INFO] API响应状态码: 429
2025-06-20 10:09:02,682 [WARNING] ⚠️ 仍然遇到429错误
2025-06-20 10:09:02,683 [INFO] 即使真实用户行为模拟也被限制，建议等待更长时间
2025-06-20 10:09:02,684 [INFO] 跳过mall_id为634418212233370的商店
2025-06-20 10:09:02,685 [INFO] 
==================================================
2025-06-20 10:09:02,685 [INFO] 正在处理商店 2/3: mall_id 634418221321199
2025-06-20 10:09:02,685 [INFO] ==================================================
2025-06-20 10:09:02,685 [INFO] 开始真实用户行为模拟: mall_id=634418221321199
2025-06-20 10:09:02,685 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-06-20 10:09:02,686 [INFO] 成功加载cookie，包含 20 个条目
2025-06-20 10:09:02,686 [INFO] 访问商店页面: https://www.temu.com/mall.html?mall_id=634418221321199
2025-06-20 10:09:03,206 [INFO] 商店页面访问状态码: 200
2025-06-20 10:09:03,208 [INFO] ✅ 成功访问商店页面
2025-06-20 10:09:03,208 [INFO] 模拟浏览页面 11.6 秒
2025-06-20 10:09:14,813 [INFO] 真实用户延迟 5.79 秒 - 模拟用户页面操作
2025-06-20 10:09:20,603 [INFO] 发送API请求: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList
2025-06-20 10:09:20,866 [INFO] API响应状态码: 429
2025-06-20 10:09:20,866 [WARNING] ⚠️ 仍然遇到429错误
2025-06-20 10:09:20,866 [INFO] 即使真实用户行为模拟也被限制，建议等待更长时间
2025-06-20 10:09:20,867 [INFO] 跳过mall_id为634418221321199的商店
2025-06-20 10:09:20,867 [INFO] 
==================================================
2025-06-20 10:09:20,867 [INFO] 正在处理商店 3/3: mall_id 634418213167233
2025-06-20 10:09:20,867 [INFO] ==================================================
2025-06-20 10:09:20,868 [INFO] 开始真实用户行为模拟: mall_id=634418213167233
2025-06-20 10:09:20,868 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-06-20 10:09:20,868 [INFO] 成功加载cookie，包含 20 个条目
2025-06-20 10:09:20,868 [INFO] 访问商店页面: https://www.temu.com/mall.html?mall_id=634418213167233
2025-06-20 10:09:21,397 [INFO] 商店页面访问状态码: 200
2025-06-20 10:09:21,397 [INFO] ✅ 成功访问商店页面
2025-06-20 10:09:21,397 [INFO] 模拟浏览页面 8.2 秒
2025-06-20 10:09:29,601 [INFO] 真实用户延迟 6.78 秒 - 模拟用户页面操作
2025-06-20 10:09:36,387 [INFO] 发送API请求: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList
2025-06-20 10:09:36,649 [INFO] API响应状态码: 429
2025-06-20 10:09:36,649 [WARNING] ⚠️ 仍然遇到429错误
2025-06-20 10:09:36,649 [INFO] 即使真实用户行为模拟也被限制，建议等待更长时间
2025-06-20 10:09:36,650 [INFO] 跳过mall_id为634418213167233的商店
2025-06-20 10:09:36,650 [INFO] 
============================================================
2025-06-20 10:09:36,650 [INFO] 真实用户行为模拟汇总
2025-06-20 10:09:36,651 [INFO] 处理时间: 2025-06-20 10:09:36
2025-06-20 10:09:36,651 [INFO] 处理商店总数: 3
2025-06-20 10:09:36,651 [INFO] 成功保存商店数: 0
2025-06-20 10:09:36,651 [INFO] ============================================================
