{"augment.advanced": {"mcpServers": [{"name": "MySQL", "command": "npx", "args": ["mcprunner", "MYSQL_HOST=*************", "MYSQL_PORT=3306", "MYSQL_USER=dtstemushop", "MYSQL_PASS=BKeFSsXNry7tMkfj", "MYSQL_DB=dtstemushop", "ALLOW_INSERT_OPERATION=true", "ALLOW_UPDATE_OPERATION=true", "ALLOW_DELETE_OPERATION=true", "--", "npx", "-y", "@benborla29/mcp-server-mysql"]}, {"name": "directus", "command": "npx", "args": ["@directus/content-mcp@latest"], "env": {"DIRECTUS_URL": "http://*************:8055", "DIRECTUS_TOKEN": "OppexW5M7FRYT3VQHT3EQx8x3Ly6k2ZM"}}, {"name": "mcp-feedback-enhanced", "command": "uvx", "args": ["mcp-feedback-enhanced@latest"]}, {"name": "sequential-thinking", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, {"name": "Playwright", "command": "npx", "args": ["@playwright/mcp@latest"]}, {"name": "BrowserToolsMCP", "command": "npx", "args": ["-y", "@browsertools/mcp-server"], "env": {"BROWSERTOOLS_HEADLESS": "true", "BROWSERTOOLS_TIMEOUT": "30000"}}]}}