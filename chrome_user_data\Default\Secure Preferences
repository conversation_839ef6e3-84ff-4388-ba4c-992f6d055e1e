{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "查找适用于Google Chrome的精彩应用、游戏、扩展程序和主题背景。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "应用商店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\137.0.7151.119\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\137.0.7151.119\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ncennffkjdiamlpmcbajkmaiiiddgioo": {"account_extension_type": 0, "ack_external": true, "active_bit": false, "active_permissions": {"api": ["contextMenus", "cookies", "downloads", "nativeMessaging", "notifications", "storage", "tabs", "webRequest", "scripting"], "explicit_host": ["<all_urls>", "http://*/*", "https://*/*"], "manifest_permissions": [], "scriptable_host": ["ftp://*/*", "http://*/*", "https://*/*"]}, "allowlist": 1, "commands": {}, "content_settings": [], "creation_flags": 9, "cws-info": {"is-live": true, "is-present": true, "last-updated-time-millis": "*************", "no-privacy-practice": false, "unpublished-long-ago": false, "violation-type": 0}, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": true, "granted_permissions": {"api": ["contextMenus", "cookies", "downloads", "nativeMessaging", "notifications", "storage", "tabs", "webRequest", "scripting"], "explicit_host": ["<all_urls>", "http://*/*", "https://*/*"], "manifest_permissions": [], "scriptable_host": ["ftp://*/*", "http://*/*", "https://*/*"]}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13394868033796426", "location": 3, "manifest": {"action": {"default_icon": "assets/icon19_normal.png", "default_popup": "popup.html", "default_title": "迅雷Chrome支持"}, "background": {"service_worker": "service-worker-loader.js", "type": "module"}, "content_scripts": [{"all_frames": true, "css": ["assets/content.css", "assets/Jsq-8833d7eb.css", "assets/content-673cd3f7.css"], "js": ["assets/content.js-loader-8297b8d7.js"], "matches": ["http://*/*", "https://*/*", "ftp://*/*"], "run_at": "document_start"}], "current_locale": "zh_CN", "default_locale": "zh_CN", "description": "迅雷下载支持", "host_permissions": ["<all_urls>", "http://*/*", "https://*/*"], "icons": {"128": "assets/install_logo.png", "16": "assets/menu_logo.png", "48": "assets/extension_logo.png"}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDEa5DG04lhgzzm3gRSXPPOZOv6ZXnzQrBv+rjUE/dL5br9Duh1kbwGQJCO4QMDvD1usf6FoXDsuvZwYzH6lg1pLI7m/wmQC3NQURHQ7J5zAy7VY0F7qSVqclcpRKY2k00vcqxok6lota3Z1QxUVUwWc9VUfr4gRUeQa4KlEsXzGwIDAQAB", "manifest_version": 3, "name": "迅雷下载支持", "optional_permissions": [], "options_page": "options.html", "permissions": ["contextMenus", "cookies", "tabs", "webRequest", "downloads", "nativeMessaging", "storage", "scripting", "notifications"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "3.52.10", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["assets/*"], "use_dynamic_url": false}, {"matches": ["ftp://*/*", "http://*/*", "https://*/*"], "resources": ["assets/runtime-dom.esm-bundler-3c6fceb0.js", "assets/util-86a8139d.js", "assets/index-9000aff5.js", "assets/Jsq-b86ddcf2.js", "assets/stat-481b0a88.js", "assets/tool-13238bfa.js", "assets/content.js-610e9598.js"], "use_dynamic_url": false}]}, "path": "ncennffkjdiamlpmcbajkmaiiiddgioo\\3.52.10_1", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "3.52.10"}, "serviceworkerevents": ["contextMenus.onClicked", "downloads.onCreated", "notifications.onClicked", "runtime.onInstalled", "tabs.onActivated", "tabs.onCreated", "tabs.onRemoved", "tabs.onUpdated", "webRequest.onBeforeSendHeaders/s2", "webRequest.onHeadersReceived/s1", "webRequest.onHeadersReceived/s3"], "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "nmmhkkegccagdldgiimedpiccmgmieda": {"account_extension_type": 0, "ack_external": true, "active_bit": false, "active_permissions": {"api": ["identity", "webview"], "explicit_host": ["https://payments.google.com/*", "https://sandbox.google.com/*", "https://www.google.com/*", "https://www.googleapis.com/*"], "manifest_permissions": [], "scriptable_host": []}, "allowlist": 1, "commands": {}, "content_settings": [], "creation_flags": 137, "cws-info": {"is-live": true, "is-present": true, "last-updated-time-millis": "*************", "no-privacy-practice": false, "unpublished-long-ago": false, "violation-type": 0}, "disable_reasons": [], "events": ["app.runtime.onLaunched", "runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": true, "granted_permissions": {"api": ["identity", "webview"], "explicit_host": ["https://payments.google.com/*", "https://sandbox.google.com/*", "https://www.google.com/*", "https://www.googleapis.com/*"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "13394789999026435", "location": 10, "manifest": {"app": {"background": {"scripts": ["craw_background.js"]}}, "current_locale": "zh_CN", "default_locale": "en", "description": "Chrome 网上应用店付款系统", "display_in_launcher": false, "display_in_new_tab_page": false, "icons": {"128": "images/icon_128.png", "16": "images/icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrKfMnLqViEyokd1wk57FxJtW2XXpGXzIHBzv9vQI/01UsuP0IV5/lj0wx7zJ/xcibUgDeIxobvv9XD+zO1MdjMWuqJFcKuSS4Suqkje6u+pMrTSGOSHq1bmBVh0kpToN8YoJs/P/yrRd7FEtAXTaFTGxQL4C385MeXSjaQfiRiQIDAQAB", "manifest_version": 2, "minimum_chrome_version": "29", "name": "Chrome 网上应用店付款系统", "oauth2": {"auto_approve": true, "client_id": "203784468217.apps.googleusercontent.com", "scopes": ["https://www.googleapis.com/auth/sierra", "https://www.googleapis.com/auth/sierrasandbox", "https://www.googleapis.com/auth/chromewebstore", "https://www.googleapis.com/auth/chromewebstore.readonly"]}, "permissions": ["identity", "webview", "https://www.google.com/", "https://www.googleapis.com/*", "https://payments.google.com/payments/v4/js/integrator.js", "https://sandbox.google.com/payments/v4/js/integrator.js"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.0.0.6"}, "path": "nmmhkkegccagdldgiimedpiccmgmieda\\1.0.0.6_0", "preferences": {}, "regular_only_preferences": {}, "running": true, "was_installed_by_default": true, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "875043B12FFCC04B1AB3F583C4951A383CB5EAA725F58EFA21044FFC2F50A55A"}, "extensions": {"ui": {"developer_mode": "4D8A1A1EF7842CDED2FB8622F98608B139D9C7EADBC8A7D847460339BDB17CB6"}}, "homepage": "255F39E2648CFADC17F7FFAF532FDA4F2C0DA17672500672499220DA1B7F7231", "homepage_is_newtabpage": "454294251394BDEAA51E8F0F3A4B70E482EBDB315834E97ACD686088FCFA0E92", "session": {"restore_on_startup": "D29820F002FD757E577915C9EF34BAC973F5EED565010FCA8B9F9D3CA272426E", "startup_urls": "062F1D48442958F317DA817F26E30193CFF12AE22FE5D766A59316A0E5ABA452"}}, "browser": {"show_home_button": "D6F0E4E0515C1B5028D74D11BCD848C7A7D12BDF892A1C0D949ABC105AC722A5"}, "default_search_provider_data": {"template_url_data": "ABFC66B80399E1226B4B6937C69DB4BFEB7805565490A2098411263DA74BBDDB"}, "enterprise_signin": {"policy_recovery_token": "1C5BE2A6D5BC368C8A86DA49E9337DA0E05108B52AC125446BEF8B9D03A092EE"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "8176AB35A9942DF5D6D0790964176C049AD474583A830B254AA0FD4E0B4434C1", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "1CA617FA239D7A506D153678F5471DFB8C85F8926B89038870485B006FDB19C4", "ncennffkjdiamlpmcbajkmaiiiddgioo": "998EC4B706515293B3B7EE427B737BB78F5C172580E70D18328815FF10988383", "nmmhkkegccagdldgiimedpiccmgmieda": "748C7553B99CAE0086C3C009BEF3F8EB8BE91DA7418E20E9E3ADE2C465E3E7B6"}, "ui": {"developer_mode": "A0470206E83DFB2D5FFE9B44F5C3D535F86AEDBA39BCC856D7699032F56E1138"}}, "google": {"services": {"account_id": "48723D73EDAC6947E842152440F2C623DD79FF81A763825D06373373496AAE3C", "last_signed_in_username": "A279BFCD324E335A9153D068EC5871AC0022448968089580368DFB3366610869", "last_username": "C06202FA8BE837F06709975547143F1686636D0BAFD16E611CF835E2C47EC25A"}}, "homepage": "B1A0C3EC7A47701CF23669125D91C5BB5994472996DCE3A737E028B682634146", "homepage_is_newtabpage": "38BB641455DB00E89B3CF50BBAA20805A884DDFA0D35E205FFAF42390F7F81A0", "media": {"cdm": {"origin_data": "645A61593058F486C5C5F37F33A7914C55DC8FE811FB0A4C7F7A48220FB15B00"}, "storage_id_salt": "7A396CFDBE7590DFA3ECA15901D53F4A1090AC5B9F3B24E13ACC57480F06B3CF"}, "module_blocklist_cache_md5_digest": "6869D5E96BCE91A2F09FC8C7FB6F66FF71D182E006FB7C22DCB93084511ED74A", "pinned_tabs": "0E3615798F55EE14F6E2F03E5AAC7B482A944E8C784A79411B46681F7B17789D", "prefs": {"preference_reset_time": "0FB79FB50AFE358E87CC7C87E65C3F9CE3941F7E2F20D248842E9D9558E6A689"}, "safebrowsing": {"incidents_sent": "A814D7347737C599AEC718855E5D06A6702AAEF54AF32D91DA1C8405375CFB85"}, "search_provider_overrides": "641A50211461FC399BDB791EAD6DCCC14A1D8147D05490751B8955C49A8C060B", "session": {"restore_on_startup": "3FE361746EEA127018A78C24AF687A962DFC3196DE4560DE17098465AAF8F4D1", "startup_urls": "23D3A57550268BE706B65458ADF79D112CE7603F3361D935524825C35ECAF7E3"}}, "super_mac": "9EA5D15DBD4244A7FE06C4B9D01EA79BE446C56B53D3715F609F4F1893F7E114"}}