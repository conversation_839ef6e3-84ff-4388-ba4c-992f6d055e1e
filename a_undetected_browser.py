#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反检测浏览器自动化版本
使用undetected-chromedriver绕过Cloudflare和反爬虫检测
"""

import json
import time
import logging
import random
import os
from datetime import datetime
import re

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"temu_crawler_undetected_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logging.info(f"反检测浏览器自动化日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def setup_undetected_chrome():
    """设置反检测Chrome浏览器"""
    try:
        import undetected_chromedriver as uc
        
        logging.info("🔧 开始设置反检测Chrome浏览器...")
        
        # 配置Chrome选项
        options = uc.ChromeOptions()
        
        # 基本设置
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        # 反检测增强设置
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--no-first-run')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-infobars')
        
        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36')
        
        # 禁用图片加载以提高速度
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0
        }
        options.add_experimental_option("prefs", prefs)
        
        # 创建反检测Chrome驱动
        driver = uc.Chrome(options=options, version_main=137)
        
        # 执行额外的反检测脚本
        driver.execute_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            
            window.chrome = {
                runtime: {},
            };
            
            Object.defineProperty(navigator, 'permissions', {
                get: () => ({
                    query: () => Promise.resolve({ state: 'granted' }),
                }),
            });
        """)
        
        logging.info("✅ 反检测Chrome浏览器初始化成功")
        return driver
        
    except ImportError:
        logging.error("❌ undetected-chromedriver未安装，请运行: pip install undetected-chromedriver")
        return None
    except Exception as e:
        logging.error(f"❌ 反检测Chrome浏览器初始化失败: {str(e)}")
        return None

def load_cookies_to_undetected_browser(driver):
    """将cookie加载到反检测浏览器中"""
    try:
        cookie_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        
        # 先访问域名以设置cookie
        logging.info("🌐 访问Temu主页以设置域名...")
        driver.get("https://www.temu.com")
        time.sleep(3)
        
        if os.path.exists(cookie_file):
            with open(cookie_file, 'r', encoding='utf-8') as f:
                cookies = json.load(f)
            
            logging.info(f"📥 开始加载 {len(cookies)} 个cookie...")
            
            for cookie in cookies:
                try:
                    # 只添加必要的cookie字段
                    cookie_dict = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie.get('domain', '.temu.com'),
                        'path': cookie.get('path', '/'),
                        'secure': cookie.get('secure', True)
                    }
                    driver.add_cookie(cookie_dict)
                except Exception as e:
                    logging.warning(f"添加cookie失败: {cookie['name']} - {str(e)}")
            
            logging.info(f"✅ 成功加载cookie到反检测浏览器")
            return True
        else:
            logging.warning("⚠️ Cookie文件不存在")
            return False
    except Exception as e:
        logging.error(f"❌ 加载cookie到反检测浏览器失败: {str(e)}")
        return False

def human_like_behavior(driver):
    """模拟人类行为"""
    try:
        # 随机移动鼠标
        driver.execute_script("""
            function simulateMouseMove() {
                const event = new MouseEvent('mousemove', {
                    clientX: Math.random() * window.innerWidth,
                    clientY: Math.random() * window.innerHeight,
                    bubbles: true
                });
                document.dispatchEvent(event);
            }
            
            // 模拟多次鼠标移动
            for(let i = 0; i < 3; i++) {
                setTimeout(simulateMouseMove, i * 500);
            }
        """)
        
        # 随机滚动页面
        for i in range(3):
            scroll_position = random.randint(200, 600) * (i + 1)
            driver.execute_script(f"window.scrollTo(0, {scroll_position});")
            time.sleep(random.uniform(1, 3))
        
        # 模拟键盘事件
        driver.execute_script("""
            const event = new KeyboardEvent('keydown', {
                key: 'Tab',
                bubbles: true
            });
            document.dispatchEvent(event);
        """)
        
        logging.info("🎭 完成人类行为模拟")
        
    except Exception as e:
        logging.warning(f"人类行为模拟时出错: {str(e)}")

def visit_mall_page_undetected(driver, mall_id):
    """使用反检测浏览器访问商店页面"""
    mall_url = f"https://www.temu.com/mall.html?mall_id={mall_id}"
    
    try:
        logging.info(f"🌐 反检测浏览器访问商店页面: {mall_url}")
        
        # 访问页面
        driver.get(mall_url)
        
        # 等待页面初始加载
        time.sleep(5)
        
        # 检查页面标题和URL
        page_title = driver.title
        current_url = driver.current_url
        
        logging.info(f"📄 页面标题: {page_title}")
        logging.info(f"🔗 当前URL: {current_url}")
        
        # 检查是否被重定向到错误页面
        if "no_access" in current_url or "没有互联网连接" in page_title:
            logging.warning("⚠️ 页面被重定向到无访问权限页面")
            
            # 等待更长时间让Cloudflare挑战完成
            logging.info("⏳ 等待Cloudflare挑战完成...")
            time.sleep(15)
            
            # 刷新页面
            logging.info("🔄 刷新页面...")
            driver.refresh()
            time.sleep(10)
            
            # 再次检查
            page_title = driver.title
            current_url = driver.current_url
            logging.info(f"📄 刷新后页面标题: {page_title}")
            logging.info(f"🔗 刷新后当前URL: {current_url}")
        
        # 模拟人类行为
        human_like_behavior(driver)
        
        # 等待更多内容加载
        time.sleep(8)
        
        # 获取最终页面内容
        page_source = driver.page_source
        logging.info(f"📊 页面内容长度: {len(page_source)} 字符")
        
        # 保存页面内容
        debug_file = f"undetected_page_{mall_id}.html"
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(page_source)
        logging.info(f"📄 页面内容已保存到: {debug_file}")
        
        # 检查页面是否包含商店数据
        success_indicators = [
            "mall_name",
            "goods_list", 
            "mallInfoWithGoodsList",
            "商店",
            "产品",
            "price"
        ]
        
        found_indicators = [indicator for indicator in success_indicators if indicator in page_source]
        
        if found_indicators:
            logging.info(f"✅ 页面包含商店数据，找到指标: {found_indicators}")
            return True, page_source
        elif "no_access" in current_url or "没有互联网连接" in page_title:
            logging.warning("❌ 页面仍然被重定向到无访问权限页面")
            return False, page_source
        else:
            logging.warning("⚠️ 页面内容不确定，可能仍在加载")
            return False, page_source
        
    except Exception as e:
        logging.error(f"❌ 反检测浏览器访问页面失败: {str(e)}")
        return False, ""

def extract_data_from_undetected_page(page_source, mall_id):
    """从反检测浏览器获取的页面中提取数据"""
    logging.info("🔍 从反检测浏览器页面提取数据...")
    
    extracted_data = {
        'mall_id': mall_id,
        'mall_name': None,
        'products': [],
        'success': False,
        'raw_data': {}
    }
    
    try:
        # 方法1：提取JavaScript中的JSON数据
        json_patterns = [
            r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
            r'window\.__APOLLO_STATE__\s*=\s*({.*?});',
            r'window\.__NEXT_DATA__\s*=\s*({.*?});',
            r'window\.pageData\s*=\s*({.*?});',
            r'window\.mallData\s*=\s*({.*?});',
            r'__INITIAL_PROPS__\s*=\s*({.*?});'
        ]
        
        for i, pattern in enumerate(json_patterns, 1):
            matches = re.finditer(pattern, page_source, re.DOTALL)
            for match in matches:
                try:
                    json_str = match.group(1)
                    data = json.loads(json_str)
                    logging.info(f"✅ 成功提取JSON数据块 {i}，大小: {len(json_str)} 字符")
                    extracted_data['raw_data'][f'pattern_{i}'] = data
                    
                    # 递归查找商店信息
                    def find_mall_info(obj, path=""):
                        if isinstance(obj, dict):
                            for key, value in obj.items():
                                if key in ['mall_name', 'storeName', 'name', 'title'] and isinstance(value, str) and len(value) > 0:
                                    if not extracted_data['mall_name']:
                                        extracted_data['mall_name'] = value
                                        logging.info(f"找到商店名称: {value}")
                                
                                # 查找产品信息
                                if key in ['goods_list', 'products', 'items'] and isinstance(value, list):
                                    for item in value[:10]:  # 限制前10个
                                        if isinstance(item, dict):
                                            product_info = {}
                                            for prod_key in ['title', 'name', 'goods_name']:
                                                if prod_key in item and isinstance(item[prod_key], str):
                                                    product_info['title'] = item[prod_key]
                                                    break
                                            for price_key in ['price', 'min_price', 'current_price']:
                                                if price_key in item:
                                                    product_info['price'] = str(item[price_key])
                                                    break
                                            if product_info:
                                                product_info['mall_id'] = mall_id
                                                extracted_data['products'].append(product_info)
                                
                                if isinstance(value, (dict, list)):
                                    find_mall_info(value, f"{path}.{key}")
                        elif isinstance(obj, list):
                            for item in obj:
                                if isinstance(item, (dict, list)):
                                    find_mall_info(item, path)
                    
                    find_mall_info(data)
                    
                    if extracted_data['mall_name'] or extracted_data['products']:
                        extracted_data['success'] = True
                        
                except json.JSONDecodeError:
                    continue
                except Exception as e:
                    logging.warning(f"解析JSON数据时出错: {str(e)}")
                    continue
        
        # 方法2：正则表达式直接提取
        if not extracted_data['success']:
            logging.info("尝试使用正则表达式直接提取...")
            
            # 提取商店名称
            name_patterns = [
                r'"mall_name":"([^"]+)"',
                r'"storeName":"([^"]+)"',
                r'<title>([^<]+)</title>',
                r'<h1[^>]*>([^<]+)</h1>'
            ]
            
            for pattern in name_patterns:
                match = re.search(pattern, page_source)
                if match and not extracted_data['mall_name']:
                    extracted_data['mall_name'] = match.group(1)
                    extracted_data['success'] = True
                    logging.info(f"正则提取商店名称: {extracted_data['mall_name']}")
                    break
        
        logging.info(f"📊 数据提取完成:")
        logging.info(f"  - 商店名称: {extracted_data['mall_name'] or '未找到'}")
        logging.info(f"  - 产品数量: {len(extracted_data['products'])}")
        logging.info(f"  - 原始数据块: {len(extracted_data['raw_data'])}")
        logging.info(f"  - 提取成功: {extracted_data['success']}")
        
        return extracted_data
        
    except Exception as e:
        logging.error(f"提取数据时发生异常: {str(e)}")
        return extracted_data

def main():
    """反检测浏览器自动化主函数"""
    logging.info("="*60)
    logging.info("开始运行Temu数据抓取 - 反检测浏览器自动化")
    logging.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"日志文件: {log_file}")
    logging.info("="*60)
    
    driver = None
    try:
        # 设置反检测浏览器
        driver = setup_undetected_chrome()
        if not driver:
            logging.error("❌ 反检测浏览器设置失败")
            return False
        
        # 加载cookie
        if not load_cookies_to_undetected_browser(driver):
            logging.warning("⚠️ Cookie加载失败，继续尝试")
        
        # 测试商店
        test_mall_id = "634418212233370"
        logging.info(f"\n🎯 测试商店: {test_mall_id}")
        
        # 访问商店页面
        success, page_source = visit_mall_page_undetected(driver, test_mall_id)
        
        if success:
            # 提取数据
            extracted_data = extract_data_from_undetected_page(page_source, test_mall_id)
            
            if extracted_data['success']:
                logging.info("🎉 反检测浏览器自动化成功！")
                logging.info(f"商店名称: {extracted_data['mall_name']}")
                
                if extracted_data['products']:
                    logging.info(f"提取到 {len(extracted_data['products'])} 个产品:")
                    for i, product in enumerate(extracted_data['products'][:3], 1):
                        logging.info(f"  产品{i}: {product.get('title', 'N/A')} - {product.get('price', 'N/A')}")
                
                # 保存提取的数据
                output_file = f"undetected_extracted_data_{test_mall_id}.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(extracted_data, f, ensure_ascii=False, indent=2)
                logging.info(f"📁 数据已保存到: {output_file}")
                
                return True
            else:
                logging.warning("⚠️ 未能从页面提取到数据")
                return False
        else:
            logging.warning("⚠️ 页面访问失败或被阻止")
            return False
        
    except Exception as e:
        logging.error(f"程序执行过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if driver:
            try:
                # 保持浏览器打开一段时间以便观察
                logging.info("⏳ 保持浏览器打开10秒以便观察...")
                time.sleep(10)
                driver.quit()
                logging.info("🔒 反检测浏览器已关闭")
            except:
                pass

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 反检测浏览器自动化测试成功！")
            print("成功绕过Cloudflare保护并提取数据")
        else:
            print("\n⚠️ 反检测浏览器自动化测试失败")
            print("请检查错误日志")
    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
