2025-06-20 12:16:22,557 [INFO] 修复版API调用日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\fixed_api_correct_headers_20250620_121622.log
2025-06-20 12:16:22,558 [INFO] ============================================================
2025-06-20 12:16:22,558 [INFO] 开始使用正确headers调用Temu API
2025-06-20 12:16:22,558 [INFO] 时间: 2025-06-20 12:16:22
2025-06-20 12:16:22,558 [INFO] ============================================================
2025-06-20 12:16:22,822 [INFO] 🔧 启动浏览器获取cookie和headers...
2025-06-20 12:16:27,026 [INFO] patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-06-20 12:16:38,211 [INFO] 📥 获取到 15 个cookie
2025-06-20 12:16:38,217 [INFO] ✅ 从cookie获取verifyAuthToken: pBBujQO62qk1lY_YjNmB...
2025-06-20 12:16:38,227 [INFO] ✅ 成功获取浏览器数据
2025-06-20 12:16:38,227 [INFO] 🍪 获取到 15 个cookie
2025-06-20 12:16:38,228 [INFO] 🌐 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Sa...
2025-06-20 12:16:38,228 [INFO] 🔑 找到关键cookie: ['_bee', 'api_uid', 'verifyAuthToken']
2025-06-20 12:16:38,228 [INFO] ✅ 添加verifyauthtoken header
2025-06-20 12:16:38,228 [INFO] ✅ 正确的API会话创建成功
2025-06-20 12:16:38,228 [INFO] 📋 Headers数量: 15
2025-06-20 12:16:38,228 [INFO] 🎯 使用正确headers调用Temu API: 634418212233370
2025-06-20 12:16:38,229 [INFO] 📡 调用API: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList
2025-06-20 12:16:38,229 [INFO] 📦 请求payload: {"mallId": "634418212233370", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418212233370", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 60, "list_id": "r7oe7gyw0vd5xo2z2qja1", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018}
2025-06-20 12:16:38,229 [WARNING] ⚠️ 缺少header: anti-content
2025-06-20 12:16:38,229 [INFO] 🔑 verifyauthtoken: pBBujQO62qk1lY_YjNmBmw582062ca0af13fff3
2025-06-20 12:16:38,229 [WARNING] ⚠️ 缺少header: x-phan-data
2025-06-20 12:16:38,229 [INFO] 🔑 Cookie: __cf_bm=weP7o1SLt_Yp00sSo4IW59IeRwoKVYxn6L_WplzmNH...
2025-06-20 12:16:39,130 [INFO] 📊 响应状态码: 200
2025-06-20 12:16:39,130 [INFO] 📊 响应头: {'Date': 'Fri, 20 Jun 2025 04:16:41 GMT', 'Content-Type': 'application/json', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'vary': 'accept-encoding, Origin', 'Content-Encoding': 'gzip', 'x-gateway-request-id': '1750393000694-247ad78109dabda8d07cd9d61cd1d95d-20', 'access-control-allow-origin': 'https://www.temu.com', 'access-control-allow-headers': 'Origin, X-Requested-With, Content-Type, X_Requested_With, Accept, X-HTTP-Method-Override, Cookie, AccessToken, PASSID, VerifyAuthToken, Anti-Content', 'access-control-allow-methods': 'GET, POST, OPTIONS, DELETE, PUT', 'access-control-allow-credentials': 'true', 'strict-transport-security': 'max-age=31536000', 'content-security-policy-report-only': "default-src 'none';script-src 'report-sample';report-uri /api/sec-csp/110000007/sec-gif", 'yak-timeinfo': '1750393000694|221', 'alt-svc': 'h3=":443"; ma=86400', 'cip': '************', 'cf-cache-status': 'DYNAMIC', 'Server': 'cloudflare', 'CF-RAY': '952873bd8a4fc861-HKG'}
2025-06-20 12:16:39,133 [INFO] ✅ API调用成功！数据长度: 390038 字符
2025-06-20 12:16:39,145 [INFO] 📁 响应数据已保存: fixed_api_success_634418212233370_121639.json
2025-06-20 12:16:39,146 [INFO] 🏪 商店名称: Personalizedforyou
2025-06-20 12:16:39,146 [INFO] 📦 商品数量: 60
2025-06-20 12:16:39,154 [INFO] ensuring close
