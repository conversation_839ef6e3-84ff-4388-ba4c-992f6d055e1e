#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确cookie加载的浏览器自动化版本
确保所有cookie都正确加载到浏览器中
"""

import json
import time
import logging
import random
import os
from datetime import datetime
# import re  # 暂时不需要

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"temu_crawler_precise_cookie_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logging.info(f"精确cookie加载日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def setup_undetected_chrome_with_precise_cookies():
    """设置反检测Chrome浏览器并精确加载cookie"""
    try:
        import undetected_chromedriver as uc
        
        logging.info("🔧 开始设置反检测Chrome浏览器（精确cookie版本）...")
        
        # 配置Chrome选项
        options = uc.ChromeOptions()
        
        # 基本设置
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        # 反检测增强设置
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--no-first-run')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-infobars')
        
        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
        
        # 创建反检测Chrome驱动
        driver = uc.Chrome(options=options, version_main=137)
        
        # 执行额外的反检测脚本
        driver.execute_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            
            window.chrome = {
                runtime: {},
            };
            
            Object.defineProperty(navigator, 'permissions', {
                get: () => ({
                    query: () => Promise.resolve({ state: 'granted' }),
                }),
            });
        """)
        
        logging.info("✅ 反检测Chrome浏览器初始化成功")
        return driver
        
    except ImportError:
        logging.error("❌ undetected-chromedriver未安装，请运行: pip install undetected-chromedriver")
        return None
    except Exception as e:
        logging.error(f"❌ 反检测Chrome浏览器初始化失败: {str(e)}")
        return None

def load_cookies_precisely(driver):
    """精确加载cookie到浏览器中"""
    try:
        cookie_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        
        if not os.path.exists(cookie_file):
            logging.error("❌ Cookie文件不存在")
            return False
        
        # 先访问域名以设置cookie上下文
        logging.info("🌐 访问Temu主页以设置cookie上下文...")
        driver.get("https://www.temu.com")
        time.sleep(3)
        
        # 加载cookie文件
        with open(cookie_file, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
        
        logging.info(f"📥 开始精确加载 {len(cookies_data)} 个cookie...")
        
        # 清除现有cookie
        driver.delete_all_cookies()
        logging.info("🧹 已清除浏览器中的所有现有cookie")
        
        # 逐个添加cookie
        successful_cookies = 0
        failed_cookies = 0
        
        for i, cookie in enumerate(cookies_data, 1):
            try:
                # 构造cookie字典，只包含必要字段
                cookie_dict = {
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie.get('domain', '.temu.com'),
                    'path': cookie.get('path', '/'),
                }
                
                # 只有当secure为True时才添加secure字段
                if cookie.get('secure', False):
                    cookie_dict['secure'] = True
                
                # 只有当httpOnly为True时才添加httpOnly字段
                if cookie.get('httpOnly', False):
                    cookie_dict['httpOnly'] = True
                
                # 添加cookie
                driver.add_cookie(cookie_dict)
                successful_cookies += 1
                
                logging.info(f"✅ 成功添加cookie {i}/{len(cookies_data)}: {cookie['name']}")
                
            except Exception as e:
                failed_cookies += 1
                logging.warning(f"⚠️ 添加cookie失败 {i}/{len(cookies_data)}: {cookie['name']} - {str(e)}")
        
        logging.info(f"📊 Cookie加载完成: 成功 {successful_cookies}, 失败 {failed_cookies}")
        
        # 验证cookie是否正确加载
        browser_cookies = driver.get_cookies()
        logging.info(f"🔍 浏览器中现有cookie数量: {len(browser_cookies)}")
        
        # 检查关键cookie是否存在
        key_cookies = ['AccessToken', 'user_uin', '_bee', 'api_uid', 'verifyAuthToken']
        found_key_cookies = []
        
        for browser_cookie in browser_cookies:
            if browser_cookie['name'] in key_cookies:
                found_key_cookies.append(browser_cookie['name'])
        
        logging.info(f"🔑 找到关键cookie: {found_key_cookies}")
        
        if len(found_key_cookies) >= 3:  # 至少要有3个关键cookie
            logging.info("✅ 关键cookie加载成功，应该能够正常访问")
            return True
        else:
            logging.warning("⚠️ 关键cookie加载不完整，可能影响访问")
            return False
            
    except Exception as e:
        logging.error(f"❌ 精确加载cookie失败: {str(e)}")
        return False

def visit_mall_page_with_precise_cookies(driver, mall_id):
    """使用精确cookie访问商店页面"""
    mall_url = f"https://www.temu.com/mall.html?mall_id={mall_id}"
    
    try:
        logging.info(f"🌐 使用精确cookie访问商店页面: {mall_url}")
        
        # 刷新页面以确保cookie生效
        logging.info("🔄 刷新页面以确保cookie生效...")
        driver.refresh()
        time.sleep(3)
        
        # 访问商店页面
        driver.get(mall_url)
        
        # 等待页面初始加载
        time.sleep(8)
        
        # 检查页面标题和URL
        page_title = driver.title
        current_url = driver.current_url
        
        logging.info(f"📄 页面标题: {page_title}")
        logging.info(f"🔗 当前URL: {current_url}")
        
        # 检查是否被重定向到错误页面
        if "no_access" in current_url or "没有互联网连接" in page_title:
            logging.warning("⚠️ 仍然被重定向到无访问权限页面")
            
            # 尝试再次刷新
            logging.info("🔄 尝试再次刷新页面...")
            driver.refresh()
            time.sleep(10)
            
            # 再次检查
            page_title = driver.title
            current_url = driver.current_url
            logging.info(f"📄 刷新后页面标题: {page_title}")
            logging.info(f"🔗 刷新后当前URL: {current_url}")
        
        # 检查浏览器中的cookie状态
        current_cookies = driver.get_cookies()
        logging.info(f"🍪 当前浏览器cookie数量: {len(current_cookies)}")
        
        # 显示一些关键cookie的值
        key_cookies = ['AccessToken', 'user_uin', '_bee']
        for cookie in current_cookies:
            if cookie['name'] in key_cookies:
                logging.info(f"🔑 关键cookie {cookie['name']}: {cookie['value'][:20]}...")
        
        # 模拟人类行为
        logging.info("🎭 模拟人类浏览行为...")
        
        # 随机滚动页面
        for i in range(3):
            scroll_position = random.randint(200, 600) * (i + 1)
            driver.execute_script(f"window.scrollTo(0, {scroll_position});")
            time.sleep(random.uniform(2, 4))
        
        # 尝试执行JavaScript来触发数据加载
        logging.info("🔧 尝试执行JavaScript触发数据加载...")

        try:
            # 尝试重新加载页面数据
            driver.execute_script("""
                // 尝试触发页面重新加载
                if (window.location && window.location.reload) {
                    setTimeout(() => {
                        window.location.reload(true);
                    }, 2000);
                }

                // 尝试触发数据加载事件
                if (window.dispatchEvent) {
                    const event = new Event('load');
                    window.dispatchEvent(event);
                }

                // 尝试触发DOMContentLoaded事件
                if (document.dispatchEvent) {
                    const event = new Event('DOMContentLoaded');
                    document.dispatchEvent(event);
                }
            """)

            # 等待JavaScript执行
            time.sleep(5)

        except Exception as e:
            logging.warning(f"JavaScript执行失败: {str(e)}")

        # 等待更长时间让内容加载
        logging.info("⏳ 等待30秒让页面完全加载...")
        time.sleep(30)

        # 再次检查页面状态
        current_title = driver.title
        current_url = driver.current_url
        logging.info(f"📄 最终页面标题: {current_title}")
        logging.info(f"🔗 最终URL: {current_url}")

        # 获取最终页面内容
        page_source = driver.page_source
        logging.info(f"📊 页面内容长度: {len(page_source)} 字符")
        
        # 保存页面内容
        debug_file = f"precise_cookie_page_{mall_id}.html"
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(page_source)
        logging.info(f"📄 页面内容已保存到: {debug_file}")
        
        # 检查页面是否包含商店数据
        success_indicators = [
            "mall_name",
            "goods_list", 
            "mallInfoWithGoodsList",
            "商店",
            "产品",
            "price",
            "商品"
        ]
        
        found_indicators = [indicator for indicator in success_indicators if indicator in page_source]
        
        if found_indicators:
            logging.info(f"✅ 页面包含商店数据，找到指标: {found_indicators}")
            return True, page_source
        elif "no_access" in current_url or "没有互联网连接" in page_title:
            logging.warning("❌ 页面仍然被重定向到无访问权限页面")
            return False, page_source
        else:
            logging.warning("⚠️ 页面内容不确定，可能仍在加载")
            return False, page_source
        
    except Exception as e:
        logging.error(f"❌ 精确cookie访问页面失败: {str(e)}")
        return False, ""

def main():
    """精确cookie加载主函数"""
    logging.info("="*60)
    logging.info("开始运行Temu数据抓取 - 精确Cookie加载版本")
    logging.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"日志文件: {log_file}")
    logging.info("="*60)
    
    driver = None
    try:
        # 设置反检测浏览器
        driver = setup_undetected_chrome_with_precise_cookies()
        if not driver:
            logging.error("❌ 反检测浏览器设置失败")
            return False
        
        # 精确加载cookie
        if not load_cookies_precisely(driver):
            logging.error("❌ 精确cookie加载失败")
            return False
        
        # 测试商店
        test_mall_id = "634418212233370"
        logging.info(f"\n🎯 测试商店: {test_mall_id}")
        
        # 访问商店页面
        success, page_source = visit_mall_page_with_precise_cookies(driver, test_mall_id)
        
        if success:
            logging.info("🎉 精确cookie加载成功！页面访问正常")
            return True
        else:
            logging.warning("⚠️ 页面访问失败或被阻止")
            return False
        
    except Exception as e:
        logging.error(f"程序执行过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if driver:
            try:
                # 保持浏览器打开一段时间以便观察
                logging.info("⏳ 保持浏览器打开15秒以便观察...")
                time.sleep(15)
                driver.quit()
                logging.info("🔒 浏览器已关闭")
            except:
                pass

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 精确cookie加载测试成功！")
            print("成功使用您的cookie访问页面")
        else:
            print("\n⚠️ 精确cookie加载测试失败")
            print("请检查错误日志")
    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
