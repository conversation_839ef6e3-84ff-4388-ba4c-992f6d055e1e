2025-06-20 12:30:53,070 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250620_123053.log
2025-06-20 12:30:53,071 [INFO] ==================================================
2025-06-20 12:30:53,071 [INFO] 开始运行Temu数据抓取
2025-06-20 12:30:53,107 [INFO] 时间: 2025-06-20 12:30:53
2025-06-20 12:30:53,108 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250620_123053.log
2025-06-20 12:30:53,108 [INFO] ==================================================
2025-06-20 12:30:53,108 [INFO] 
检查Cookie状态...
2025-06-20 12:30:53,108 [INFO] 🔄 开始自动化刷新cookie...
2025-06-20 12:30:57,365 [INFO] patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-06-20 12:30:58,163 [INFO] 🌐 访问卖家中心...
2025-06-20 12:31:03,566 [WARNING] ⚠️ 需要手动登录，请在浏览器中完成登录...
2025-06-20 12:31:13,569 [INFO] ⏳ 等待登录中... (10/300秒)
2025-06-20 12:31:23,574 [INFO] ⏳ 等待登录中... (20/300秒)
2025-06-20 12:31:23,873 [INFO] ✅ 登录成功，获取新cookie...
2025-06-20 12:31:27,373 [INFO] 📥 获取到 15 个新cookie
2025-06-20 12:31:27,376 [INFO] ✅ Cookie刷新成功
2025-06-20 12:31:27,377 [INFO] Cookie检查/更新完成
2025-06-20 12:31:27,377 [INFO] 
获取商店ID列表...
2025-06-20 12:31:27,377 [INFO] 请求商店ID列表: http://172.25.165.28:8055/items/shop_data
2025-06-20 12:31:27,447 [INFO] 获取商店ID状态码: 200
2025-06-20 12:31:27,448 [INFO] 获取到的商店数据: {
  "data": [
    {
      "mall_id": "634418212233370"
    },
    {
      "mall_id": "634418221321199"
    },
    {
      "mall_id": "634418221704901"
    },
    {
      "mall_id": "634418213167233"
    }
  ]
}...
2025-06-20 12:31:27,448 [INFO] 获取到的全部mall_id数量: 4
2025-06-20 12:31:27,448 [INFO] 去重后的mall_id数量: 4
2025-06-20 12:31:27,448 [INFO] 去除了 0 个重复的mall_id
2025-06-20 12:31:27,448 [INFO] 最终使用的商店ID列表: ['634418213167233', '634418212233370', '634418221704901', '634418221321199']
2025-06-20 12:31:27,449 [INFO] 
开始处理第 1 批商店，共 1 个
2025-06-20 12:31:27,449 [INFO] 
==================================================
2025-06-20 12:31:27,449 [INFO] 正在处理商店 1/4: mall_id 634418213167233
2025-06-20 12:31:27,449 [INFO] ==================================================
2025-06-20 12:31:27,449 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418213167233
2025-06-20 12:31:27,449 [INFO] 随机延迟 16.89 秒
2025-06-20 12:31:44,344 [INFO] 🔧 启动自动化cookie获取...
2025-06-20 12:31:48,607 [INFO] patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-06-20 12:31:49,383 [INFO] setting properties for headless
2025-06-20 12:31:52,816 [INFO] 📥 获取到 16 个cookie
2025-06-20 12:31:52,819 [INFO] ✅ 成功获取cookie，包含 16 个条目
2025-06-20 12:31:52,820 [INFO] 🔑 获取到verifyAuthToken: pBBujQO62qk1lY_YjNmB...
2025-06-20 12:31:52,820 [INFO] ✅ 添加verifyAuthToken到headers
2025-06-20 12:31:52,820 [INFO] 生成增强Temu请求头: 15 个字段
2025-06-20 12:31:53,372 [INFO] Temu API状态码: 403
2025-06-20 12:31:53,372 [ERROR] 获取mall_id为634418213167233的Temu数据时出错: 403
2025-06-20 12:31:53,372 [ERROR] 返回内容: {"success":false,"error_code":40001,"errorCode":40001,"verify_auth_token":"pBBujQO62qk1lY_YjNmBmw582062ca0af13fff3"}...
2025-06-20 12:31:53,372 [WARNING] 可能是cookie过期，尝试更新cookie...
2025-06-20 12:31:53,373 [INFO] 🔄 开始自动化刷新cookie...
2025-06-20 12:31:57,416 [INFO] patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-06-20 12:31:58,193 [INFO] 🌐 访问卖家中心...
2025-06-20 12:32:03,598 [WARNING] ⚠️ 需要手动登录，请在浏览器中完成登录...
2025-06-20 12:32:13,602 [INFO] ⏳ 等待登录中... (10/300秒)
2025-06-20 12:32:14,842 [INFO] ✅ 登录成功，获取新cookie...
2025-06-20 12:32:18,416 [INFO] 📥 获取到 15 个新cookie
2025-06-20 12:32:18,422 [INFO] ✅ Cookie刷新成功
2025-06-20 12:32:18,423 [INFO] Cookie更新成功，重试请求...
2025-06-20 12:32:18,423 [INFO] 🔧 启动自动化cookie获取...
2025-06-20 12:32:18,972 [INFO] setting properties for headless
2025-06-20 12:32:22,458 [INFO] 📥 获取到 16 个cookie
2025-06-20 12:32:22,463 [INFO] ✅ 成功获取cookie，包含 16 个条目
2025-06-20 12:32:22,463 [INFO] 🔑 获取到verifyAuthToken: pBBujQO62qk1lY_YjNmB...
2025-06-20 12:32:22,463 [INFO] ✅ 添加verifyAuthToken到headers
2025-06-20 12:32:22,464 [INFO] 生成增强Temu请求头: 15 个字段
2025-06-20 12:32:23,004 [INFO] 重试后的Temu API状态码: 403
2025-06-20 12:32:23,004 [ERROR] 重试后仍然失败: 403
2025-06-20 12:32:23,006 [INFO] 由于API错误，跳过mall_id为634418213167233的商店
2025-06-20 12:32:23,006 [INFO] 
批次间休息 274.20 秒，降低请求频率...
