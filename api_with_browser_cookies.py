#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用浏览器cookie调用API接口
"""

import json
import time
import logging
import requests
import os
from datetime import datetime

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"api_with_browser_cookies_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"API调用日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def extract_browser_cookies():
    """从浏览器获取cookie"""
    try:
        import undetected_chromedriver as uc
        
        logging.info("🔧 启动浏览器获取cookie...")
        
        # 创建用户数据目录
        user_data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "chrome_user_data")
        
        # 配置Chrome选项
        options = uc.ChromeOptions()
        options.add_argument(f'--user-data-dir={user_data_dir}')
        options.add_argument('--profile-directory=Default')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--headless')  # 无头模式，更快
        
        # 创建浏览器
        driver = uc.Chrome(options=options, version_main=137)
        
        # 访问Temu主页确保cookie加载
        driver.get("https://www.temu.com")
        time.sleep(3)
        
        # 获取所有cookie
        cookies = driver.get_cookies()
        logging.info(f"📥 获取到 {len(cookies)} 个cookie")
        
        # 转换为requests格式
        cookie_dict = {}
        for cookie in cookies:
            cookie_dict[cookie['name']] = cookie['value']
        
        # 获取User-Agent
        user_agent = driver.execute_script("return navigator.userAgent;")
        
        driver.quit()
        
        logging.info("✅ 成功获取浏览器cookie和User-Agent")
        return cookie_dict, user_agent
        
    except Exception as e:
        logging.error(f"❌ 获取浏览器cookie失败: {str(e)}")
        return None, None

def create_api_session(cookies, user_agent):
    """创建带有浏览器cookie的API会话"""
    try:
        session = requests.Session()
        
        # 设置cookie
        for name, value in cookies.items():
            session.cookies.set(name, value, domain='.temu.com')
        
        # 设置headers，模拟真实浏览器
        session.headers.update({
            'User-Agent': user_agent,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://www.temu.com/',
            'Origin': 'https://www.temu.com',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })
        
        logging.info("✅ API会话创建成功")
        return session
        
    except Exception as e:
        logging.error(f"❌ 创建API会话失败: {str(e)}")
        return None

def test_mall_api_with_browser_cookies(session, mall_id):
    """使用浏览器cookie测试商店API"""
    try:
        logging.info(f"🎯 使用浏览器cookie测试商店API: {mall_id}")
        
        # 测试多个可能的API端点
        api_endpoints = [
            f"https://www.temu.com/api/poppy/v1/mall/info?mall_id={mall_id}",
            f"https://www.temu.com/api/poppy/v1/mall/goods?mall_id={mall_id}&page=1&size=20",
            f"https://www.temu.com/api/poppy/v1/mall/detail?mall_id={mall_id}",
            f"https://www.temu.com/api/poppy/v1/mall/profile?mall_id={mall_id}",
            f"https://www.temu.com/api/oak/v1/mall/info?mall_id={mall_id}",
            f"https://www.temu.com/api/oak/v1/mall/goods?mall_id={mall_id}&page=1&size=20"
        ]
        
        successful_calls = []
        
        for i, api_url in enumerate(api_endpoints, 1):
            try:
                logging.info(f"📡 测试API端点 {i}/{len(api_endpoints)}: {api_url}")
                
                response = session.get(api_url, timeout=10)
                
                logging.info(f"📊 响应状态码: {response.status_code}")
                logging.info(f"📊 响应头: {dict(response.headers)}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logging.info(f"✅ API调用成功！数据长度: {len(str(data))} 字符")
                        
                        # 保存成功的响应
                        filename = f"api_success_{mall_id}_{i}_{datetime.now().strftime('%H%M%S')}.json"
                        with open(filename, 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        
                        successful_calls.append({
                            'endpoint': api_url,
                            'status_code': response.status_code,
                            'data_length': len(str(data)),
                            'filename': filename,
                            'data_preview': str(data)[:500] + "..." if len(str(data)) > 500 else str(data)
                        })
                        
                        logging.info(f"📁 响应数据已保存: {filename}")
                        
                    except json.JSONDecodeError:
                        logging.warning(f"⚠️ 响应不是有效的JSON格式")
                        logging.info(f"📄 响应内容: {response.text[:500]}...")
                        
                elif response.status_code == 429:
                    logging.warning(f"⚠️ API端点 {i} 被限流 (429)")
                    
                elif response.status_code == 403:
                    logging.warning(f"⚠️ API端点 {i} 被禁止访问 (403)")
                    
                else:
                    logging.warning(f"⚠️ API端点 {i} 返回状态码: {response.status_code}")
                    logging.info(f"📄 响应内容: {response.text[:200]}...")
                
                # 在请求之间添加延迟
                time.sleep(2)
                
            except requests.exceptions.Timeout:
                logging.warning(f"⚠️ API端点 {i} 请求超时")
            except requests.exceptions.RequestException as e:
                logging.warning(f"⚠️ API端点 {i} 请求失败: {str(e)}")
        
        return successful_calls
        
    except Exception as e:
        logging.error(f"❌ 测试商店API失败: {str(e)}")
        return []

def test_additional_apis(session, mall_id):
    """测试其他相关API"""
    try:
        logging.info("🔍 测试其他相关API...")
        
        additional_apis = [
            f"https://www.temu.com/api/poppy/v1/search/mall?keyword={mall_id}",
            f"https://www.temu.com/api/poppy/v1/store/info?store_id={mall_id}",
            f"https://www.temu.com/api/poppy/v1/merchant/info?merchant_id={mall_id}",
            "https://www.temu.com/api/poppy/v1/user/profile",
            "https://www.temu.com/api/poppy/v1/cart/list",
            "https://www.temu.com/api/poppy/v1/wishlist/list"
        ]
        
        successful_calls = []
        
        for i, api_url in enumerate(additional_apis, 1):
            try:
                logging.info(f"📡 测试额外API {i}/{len(additional_apis)}: {api_url}")
                
                response = session.get(api_url, timeout=10)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logging.info(f"✅ 额外API调用成功！")
                        
                        filename = f"additional_api_success_{i}_{datetime.now().strftime('%H%M%S')}.json"
                        with open(filename, 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)
                        
                        successful_calls.append({
                            'endpoint': api_url,
                            'status_code': response.status_code,
                            'filename': filename
                        })
                        
                    except json.JSONDecodeError:
                        logging.warning(f"⚠️ 额外API响应不是JSON格式")
                else:
                    logging.info(f"📊 额外API状态码: {response.status_code}")
                
                time.sleep(1)
                
            except Exception as e:
                logging.warning(f"⚠️ 额外API {i} 失败: {str(e)}")
        
        return successful_calls
        
    except Exception as e:
        logging.error(f"❌ 测试额外API失败: {str(e)}")
        return []

def main():
    """主函数"""
    logging.info("="*60)
    logging.info("开始使用浏览器cookie调用API接口")
    logging.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info("="*60)
    
    try:
        # 获取浏览器cookie
        cookies, user_agent = extract_browser_cookies()
        if not cookies:
            logging.error("❌ 无法获取浏览器cookie")
            return False
        
        logging.info(f"🍪 获取到 {len(cookies)} 个cookie")
        logging.info(f"🌐 User-Agent: {user_agent[:100]}...")
        
        # 创建API会话
        session = create_api_session(cookies, user_agent)
        if not session:
            logging.error("❌ 无法创建API会话")
            return False
        
        # 测试目标商店API
        target_mall_id = "634418212233370"
        successful_calls = test_mall_api_with_browser_cookies(session, target_mall_id)
        
        # 测试其他API
        additional_calls = test_additional_apis(session, target_mall_id)
        
        # 汇总结果
        total_successful = len(successful_calls) + len(additional_calls)
        
        print("\n" + "="*60)
        print("🎉 API测试结果汇总")
        print("="*60)
        print(f"✅ 成功的商店API调用: {len(successful_calls)}")
        print(f"✅ 成功的其他API调用: {len(additional_calls)}")
        print(f"🎯 总成功调用数: {total_successful}")
        
        if successful_calls:
            print("\n📊 成功的商店API:")
            for call in successful_calls:
                print(f"  - {call['endpoint']}")
                print(f"    状态码: {call['status_code']}, 文件: {call['filename']}")
        
        if additional_calls:
            print("\n📊 成功的其他API:")
            for call in additional_calls:
                print(f"  - {call['endpoint']}")
                print(f"    状态码: {call['status_code']}, 文件: {call['filename']}")
        
        print("="*60)
        
        return total_successful > 0
        
    except Exception as e:
        logging.error(f"程序执行过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 使用浏览器cookie的API调用测试成功！")
        else:
            print("\n⚠️ 使用浏览器cookie的API调用测试失败")
    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
