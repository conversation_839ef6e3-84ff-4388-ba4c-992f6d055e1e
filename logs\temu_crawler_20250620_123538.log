2025-06-20 12:35:38,670 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250620_123538.log
2025-06-20 12:35:38,670 [INFO] ==================================================
2025-06-20 12:35:38,671 [INFO] 开始运行Temu数据抓取
2025-06-20 12:35:38,705 [INFO] 时间: 2025-06-20 12:35:38
2025-06-20 12:35:38,705 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250620_123538.log
2025-06-20 12:35:38,705 [INFO] ==================================================
2025-06-20 12:35:38,705 [INFO] 
检查Cookie状态...
2025-06-20 12:35:38,705 [WARNING] ⚠️ Cookie可能已过期，需要手动刷新...
2025-06-20 12:35:55,301 [INFO] Cookie检查/更新完成
2025-06-20 12:35:55,302 [INFO] 
获取商店ID列表...
2025-06-20 12:35:55,302 [INFO] 请求商店ID列表: http://172.25.165.28:8055/items/shop_data
2025-06-20 12:35:55,352 [INFO] 获取商店ID状态码: 200
2025-06-20 12:35:55,353 [INFO] 获取到的商店数据: {
  "data": [
    {
      "mall_id": "634418212233370"
    },
    {
      "mall_id": "634418221321199"
    },
    {
      "mall_id": "634418221704901"
    },
    {
      "mall_id": "634418213167233"
    }
  ]
}...
2025-06-20 12:35:55,353 [INFO] 获取到的全部mall_id数量: 4
2025-06-20 12:35:55,353 [INFO] 去重后的mall_id数量: 4
2025-06-20 12:35:55,354 [INFO] 去除了 0 个重复的mall_id
2025-06-20 12:35:55,354 [INFO] 最终使用的商店ID列表: ['634418212233370', '634418221321199', '634418213167233', '634418221704901']
2025-06-20 12:35:55,354 [INFO] 
开始处理第 1 批商店，共 1 个
2025-06-20 12:35:55,354 [INFO] 
==================================================
2025-06-20 12:35:55,354 [INFO] 正在处理商店 1/4: mall_id 634418212233370
2025-06-20 12:35:55,354 [INFO] ==================================================
2025-06-20 12:35:55,355 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418212233370
2025-06-20 12:35:55,355 [INFO] 随机延迟 13.28 秒
