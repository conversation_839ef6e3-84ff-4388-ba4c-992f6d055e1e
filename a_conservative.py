#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保守版本的爬虫程序
专门用于避免429错误，适合IP被限制后使用
"""

import requests
import json
from datetime import datetime
import time
import traceback
import pytz
import os
import logging
import random
import subprocess

# 导入配置
from config import (
    TEMU_API_CONFIG,
    BACKEND_API_CONFIG,
    TIMEZONE_CONFIG
)

# 保守的配置覆盖
CONSERVATIVE_CONFIG = {
    "SHOP_DELAY": 300,  # 商店间隔5分钟
    "REQUEST_DELAY_MIN": 30,  # API请求最小延迟30秒
    "REQUEST_DELAY_MAX": 60,  # API请求最大延迟60秒
    "BATCH_SIZE": 1,  # 每批只处理1个商店
    "BATCH_DELAY_MIN": 600,  # 批次间最小延迟10分钟
    "BATCH_DELAY_MAX": 1200,  # 批次间最大延迟20分钟
    "MAX_SHOPS_PER_RUN": 3,  # 每次运行最多处理3个商店
}

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"temu_crawler_conservative_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logging.info(f"保守模式日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def conservative_delay(min_delay, max_delay, reason=""):
    """保守的延迟函数"""
    delay = min_delay + random.random() * (max_delay - min_delay)
    logging.info(f"保守延迟 {delay:.2f} 秒 - {reason}")
    
    # 显示倒计时
    for remaining in range(int(delay), 0, -10):
        print(f"等待中... 剩余 {remaining} 秒", end="\r")
        time.sleep(min(10, remaining))
    print(" " * 30, end="\r")  # 清除倒计时显示
    
    return delay

def load_cookies_from_file():
    """从cookie.json文件加载cookie信息"""
    try:
        cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        logging.info(f"尝试从 {cookie_file_path} 加载cookie")
        
        if not os.path.exists(cookie_file_path):
            logging.warning(f"警告: cookie文件不存在: {cookie_file_path}")
            return None
            
        with open(cookie_file_path, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
            
        # 将cookie列表转换为字符串格式
        cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_data if cookie.get('name') and cookie.get('value')])
        logging.info(f"成功加载cookie，包含 {len(cookies_data)} 个条目")
        return cookie_str
    except Exception as e:
        logging.error(f"加载cookie文件时出错: {str(e)}")
        traceback.print_exc()
        return None

def get_temu_headers():
    """返回Temu API请求的请求头"""
    fresh_cookies = load_cookies_from_file()
    if not fresh_cookies:
        logging.error("无法获取cookie")
        return None
    
    headers = {
        "content-type": "application/json;charset=UTF-8",
        "Cookie": fresh_cookies,
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache"
    }
    logging.info(f"生成保守模式请求头")
    return headers

def visit_mall_page(mall_id, session):
    """先访问商店页面，模拟真实用户行为"""
    mall_url = f"https://www.temu.com/mall.html?mall_id={mall_id}"

    logging.info(f"访问商店页面: {mall_url}")

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "max-age=0",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1"
    }

    try:
        response = session.get(mall_url, headers=headers, timeout=30)
        logging.info(f"商店页面访问状态码: {response.status_code}")

        if response.status_code == 200:
            logging.info("✅ 成功访问商店页面")
            # 模拟用户浏览页面的时间
            browse_time = random.uniform(3, 8)
            logging.info(f"模拟浏览页面 {browse_time:.1f} 秒")
            time.sleep(browse_time)
            return True
        else:
            logging.error(f"访问商店页面失败: {response.status_code}")
            return False

    except Exception as e:
        logging.error(f"访问商店页面时发生异常: {str(e)}")
        return False

def fetch_temu_shop_data_realistic(mall_id):
    """模拟真实用户行为的数据获取方式"""
    logging.info(f"开始模拟真实用户行为获取数据: mall_id={mall_id}")

    # 创建session保持cookie
    session = requests.Session()

    # 设置cookie
    fresh_cookies = load_cookies_from_file()
    if not fresh_cookies:
        logging.error("无法获取cookie")
        return None

    # 将cookie字符串转换为session cookies
    for cookie_pair in fresh_cookies.split('; '):
        if '=' in cookie_pair:
            name, value = cookie_pair.split('=', 1)
            session.cookies.set(name, value, domain='.temu.com')

    # 步骤1：先访问商店页面
    if not visit_mall_page(mall_id, session):
        logging.error("访问商店页面失败，跳过API请求")
        return None

    # 步骤2：模拟用户操作后的延迟
    conservative_delay(5, 15, "模拟用户操作延迟")

    # 步骤3：调用API接口（使用真实的Referer）
    api_url = TEMU_API_CONFIG['BASE_URL'] + TEMU_API_CONFIG['API_PATH']

    # 构造更真实的payload
    payload = {
        "mallId": mall_id,
        "mainGoodsIds": ["1"],
        "source_page_sn": "10013",
        "mall_id": mall_id,
        "main_goods_ids": ["1"],
        "filter_items": "",
        "page_number": 1,
        "page_size": TEMU_API_CONFIG['PAGE_SIZE'],
        "list_id": f"r{random.randint(1000000000000000, 9999999999999999)}",  # 随机list_id
        "scene_code": "mall_rule",
        "page_sn": 10040,
        "page_el_sn": 201265,
        "source": 10018,
        "anti_content": "1"
    }

    # 设置真实的API请求头
    api_headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Content-Type": "application/json;charset=UTF-8",
        "Origin": "https://www.temu.com",
        "Referer": f"https://www.temu.com/mall.html?mall_id={mall_id}",  # 真实的Referer
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }

    logging.info(f"发送API请求: {api_url}")

    try:
        response = session.post(api_url, headers=api_headers, json=payload, timeout=60)
        logging.info(f"API响应状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            logging.info("✅ 真实用户模拟请求成功！")
            return result
        elif response.status_code == 429:
            logging.warning("⚠️ 仍然遇到429错误")
            logging.info("真实用户模拟也被限制，建议等待更长时间")
            return None
        else:
            logging.error(f"API请求失败: {response.status_code}")
            logging.error(f"响应内容: {response.text[:500]}...")
            return None

    except Exception as e:
        logging.error(f"API请求时发生异常: {str(e)}")
        traceback.print_exc()
        return None

def main():
    """保守模式主函数 - 使用真实用户行为模拟"""
    logging.info("="*50)
    logging.info("开始运行Temu数据抓取 - 真实用户行为模拟模式")
    logging.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"日志文件: {log_file}")
    logging.info("="*50)

    # 测试单个商店
    test_mall_id = "634418212233370"

    logging.info(f"真实用户行为模拟测试，处理商店: {test_mall_id}")
    logging.info("流程：访问商店页面 → 模拟浏览 → 调用API接口")
    logging.info("这种方式更接近真实用户行为，成功率更高")

    result = fetch_temu_shop_data_realistic(test_mall_id)

    if result:
        logging.info("🎉 真实用户行为模拟成功！")
        logging.info("数据获取正常，可以运行完整程序")

        # 简单验证数据结构
        if "result" in result:
            shop_info = result["result"]
            if "mall_name" in shop_info:
                logging.info(f"商店名称: {shop_info['mall_name']}")
            if "goods_sales_num" in shop_info:
                logging.info(f"商品销量: {shop_info['goods_sales_num']}")

        return True
    else:
        logging.warning("⚠️ 真实用户行为模拟也失败")
        logging.info("可能的原因：")
        logging.info("1. IP仍被限制，需要更长等待时间")
        logging.info("2. 需要更换网络环境")
        logging.info("3. Cookie可能需要更新")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n" + "="*60)
            print("IP限制检测结果：仍被限制")
            print("建议：")
            print("1. 明天再试")
            print("2. 更换网络环境")
            print("3. 使用VPN或代理")
            print("="*60)
    except Exception as e:
        logging.error(f"程序执行过程中发生未捕获的异常: {str(e)}")
        traceback.print_exc()
