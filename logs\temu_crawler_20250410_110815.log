2025-04-10 11:08:15,369 [INFO] 日志系统初始化完成，日志文件: c:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250410_110815.log
2025-04-10 11:08:15,369 [INFO] ==================================================
2025-04-10 11:08:15,369 [INFO] 开始运行Temu数据抓取
2025-04-10 11:08:17,407 [INFO] 时间: 2025-04-10 11:08:17
2025-04-10 11:08:17,407 [INFO] 日志文件: c:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250410_110815.log
2025-04-10 11:08:17,407 [INFO] ==================================================
2025-04-10 11:08:17,408 [INFO] 
更新Temu Cookie...
2025-04-10 11:08:17,408 [INFO] 加载初始cookie文件: c:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json
2025-04-10 11:08:17,409 [INFO] 成功加载初始cookie，共19个
2025-04-10 11:08:17,409 [INFO] 开始访问Temu主页...
2025-04-10 11:08:18,300 [INFO] Temu主页访问状态码: 200
2025-04-10 11:08:18,301 [INFO] 成功更新cookie文件，共19个cookie
2025-04-10 11:08:18,302 [INFO] Cookie更新成功，继续数据抓取
2025-04-10 11:08:18,302 [INFO] 
获取商店ID列表...
2025-04-10 11:08:18,303 [INFO] 请求商店ID列表: http://172.25.165.28:8055/items/shop_data
2025-04-10 11:08:18,345 [INFO] 获取商店ID状态码: 200
2025-04-10 11:08:18,346 [INFO] 获取到的商店数据: {
  "data": [
    {
      "mall_id": "634418213326762"
    },
    {
      "mall_id": "634418210750968"
    },
    {
      "mall_id": "634418214390089"
    }
  ]
}...
2025-04-10 11:08:18,347 [INFO] 获取到的全部mall_id数量: 3
2025-04-10 11:08:18,347 [INFO] 去重后的mall_id数量: 3
2025-04-10 11:08:18,347 [INFO] 去除了 0 个重复的mall_id
2025-04-10 11:08:18,347 [INFO] 最终使用的商店ID列表: ['634418213326762', '634418214390089', '634418210750968']
2025-04-10 11:08:18,347 [INFO] 
==================================================
2025-04-10 11:08:18,348 [INFO] 正在处理商店 1/3: mall_id 634418213326762
2025-04-10 11:08:18,348 [INFO] ==================================================
2025-04-10 11:08:18,348 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418213326762
2025-04-10 11:08:18,348 [INFO] 请求payload: {"mallId": "634418213326762", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418213326762", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-04-10 11:08:18,348 [INFO] 尝试从 c:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-04-10 11:08:18,355 [INFO] 成功加载cookie，包含 19 个条目
2025-04-10 11:08:18,355 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-04-10 11:08:19,165 [INFO] Temu API状态码: 429
2025-04-10 11:08:19,166 [ERROR] 获取mall_id为634418213326762的Temu数据时出错: 429
2025-04-10 11:08:19,166 [ERROR] 返回内容: {"error_code":406008,"error_msg":""}...
2025-04-10 11:08:19,167 [INFO] 由于API错误，跳过mall_id为634418213326762的商店
2025-04-10 11:08:19,167 [INFO] 
==================================================
2025-04-10 11:08:19,167 [INFO] 正在处理商店 2/3: mall_id 634418214390089
2025-04-10 11:08:19,168 [INFO] ==================================================
2025-04-10 11:08:19,168 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418214390089
2025-04-10 11:08:19,168 [INFO] 请求payload: {"mallId": "634418214390089", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418214390089", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-04-10 11:08:19,168 [INFO] 尝试从 c:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-04-10 11:08:19,168 [INFO] 成功加载cookie，包含 19 个条目
2025-04-10 11:08:19,169 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-04-10 11:08:20,031 [INFO] Temu API状态码: 429
2025-04-10 11:08:20,032 [ERROR] 获取mall_id为634418214390089的Temu数据时出错: 429
2025-04-10 11:08:20,032 [ERROR] 返回内容: {"error_code":406008,"error_msg":""}...
2025-04-10 11:08:20,033 [INFO] 由于API错误，跳过mall_id为634418214390089的商店
2025-04-10 11:08:20,033 [INFO] 
==================================================
2025-04-10 11:08:20,033 [INFO] 正在处理商店 3/3: mall_id 634418210750968
2025-04-10 11:08:20,034 [INFO] ==================================================
2025-04-10 11:08:20,034 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418210750968
2025-04-10 11:08:20,034 [INFO] 请求payload: {"mallId": "634418210750968", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418210750968", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-04-10 11:08:20,034 [INFO] 尝试从 c:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-04-10 11:08:20,035 [INFO] 成功加载cookie，包含 19 个条目
2025-04-10 11:08:20,035 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-04-10 11:08:20,870 [INFO] Temu API状态码: 429
2025-04-10 11:08:20,870 [ERROR] 获取mall_id为634418210750968的Temu数据时出错: 429
2025-04-10 11:08:20,870 [ERROR] 返回内容: {"error_code":406008,"error_msg":""}...
2025-04-10 11:08:20,870 [INFO] 由于API错误，跳过mall_id为634418210750968的商店
2025-04-10 11:08:20,872 [INFO] 
==================================================
2025-04-10 11:08:20,872 [INFO] Temu数据抓取汇总
2025-04-10 11:08:20,872 [INFO] 处理时间: 2025-04-10 11:08:20
2025-04-10 11:08:20,873 [INFO] 处理商店总数: 3
2025-04-10 11:08:20,873 [INFO] 成功保存商店数: 0
2025-04-10 11:08:20,873 [INFO] 处理产品总数: 0
2025-04-10 11:08:20,873 [INFO] 成功保存产品数: 0
2025-04-10 11:08:20,873 [INFO] ==================================================
