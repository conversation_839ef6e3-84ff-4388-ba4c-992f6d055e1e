# Temu API 配置（限制请求频率，防止被封禁）
TEMU_API_CONFIG = {
    "BASE_URL": "https://www.temu.com",
    "API_PATH": "/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList",
    "DEFAULT_MALL_ID": "6313567470795",  # 默认测试商店ID
    "PAGE_SIZE": 8,
    "SHOP_DELAY": 60,  # 商店间隔（秒），限制请求频率 - 增加到60秒
    "PRODUCT_DELAY_MIN": 2,  # 产品处理最小延迟（秒）
    "PRODUCT_DELAY_MAX": 5,  # 产品处理最大延迟（秒）
    "REQUEST_DELAY_MIN": 60,  # API请求最小延迟（秒） - 增加到60秒避免429
    "REQUEST_DELAY_MAX": 120,  # API请求最大延迟（秒） - 增加到120秒避免429
    "BATCH_SIZE": 4,  # 每批处理的商店数量 - 改为4个（一次处理所有店铺）
    "BATCH_DELAY_MIN": 30,  # 批次间最小延迟（秒） - 减少到30秒
    "BATCH_DELAY_MAX": 60,  # 批次间最大延迟（秒） - 减少到1分钟
    "COOKIE_REFRESH_MIN": 2,  # Cookie刷新最小延迟（秒）
    "COOKIE_REFRESH_MAX": 5,  # Cookie刷新最大延迟（秒）
}

# 后端 API 配置（本地服务器，无需限制请求频率）
BACKEND_API_CONFIG = {
    "BASE_URL": "http://172.25.165.28:8055",
    "API_TOKEN": "OppexW5M7FRYT3VQHT3EQx8x3Ly6k2ZM",
}

# 时区配置
TIMEZONE_CONFIG = {
    "DEFAULT_TIMEZONE": "Asia/Hong_Kong"
}

# 请求头配置
DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1'
}