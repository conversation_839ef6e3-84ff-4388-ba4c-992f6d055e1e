# Temu爬虫 - Cloudflare Workers版本

这是将Python爬虫程序a.py转换为Cloudflare Workers JavaScript版本的实现。

## 文件说明

- `worker.js` - 主要的Cloudflare Workers脚本
- `wrangler.toml` - Cloudflare Workers配置文件
- `README-CloudflareWorkers.md` - 本说明文档

## 主要优势

1. **减少风控风险**: 使用Cloudflare的全球网络发起请求，IP更加分散
2. **无服务器**: 无需维护服务器，按需执行
3. **全球分布**: 利用Cloudflare的边缘网络，请求速度更快
4. **成本低廉**: 免费版每天10万次请求，付费版成本极低

## 部署步骤

### 1. 安装Wrangler CLI

```bash
npm install -g wrangler
```

### 2. 登录Cloudflare

```bash
wrangler login
```

### 3. 设置环境变量

设置后端API配置：
```bash
wrangler secret put BACKEND_API_URL
# 输入: http://*************:8055

wrangler secret put BACKEND_API_TOKEN
# 输入: OppexW5M7FRYT3VQHT3EQx8x3Ly6k2ZM
```

设置Temu Cookie（需要base64编码）：
```bash
# 首先将cookie.json转换为base64
# 在Windows PowerShell中：
$cookieContent = Get-Content -Path "cookie.json" -Raw
$cookieBase64 = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($cookieContent))
echo $cookieBase64

# 然后设置环境变量
wrangler secret put TEMU_COOKIES
# 输入上面生成的base64字符串
```

### 4. 部署到Cloudflare Workers

```bash
wrangler deploy
```

## 使用方法

部署成功后，你会得到一个类似 `https://temu-scraper.your-subdomain.workers.dev` 的URL。

### API端点

#### 1. 健康检查
```
GET https://your-worker-url.workers.dev/
GET https://your-worker-url.workers.dev/health
```

#### 2. 测试Cookie
```
GET https://your-worker-url.workers.dev/test-cookie
```

#### 3. 执行爬取（处理所有商店）
```
GET https://your-worker-url.workers.dev/scrape
```

#### 4. 执行爬取（指定商店）
```
GET https://your-worker-url.workers.dev/scrape?mallIds=6313567470795,**********

POST https://your-worker-url.workers.dev/scrape
Content-Type: application/json

{
  "mallIds": ["6313567470795", "**********"]
}
```

### 响应格式

成功响应示例：
```json
{
  "success": true,
  "message": "Scraping completed",
  "summary": {
    "totalShops": 2,
    "successfulShops": 2,
    "totalProducts": 16,
    "successfulProducts": 16,
    "processTime": "2024-01-15 14:30:25"
  },
  "details": [
    {
      "mallId": "6313567470795",
      "success": true,
      "shopSaved": true,
      "productsProcessed": 8,
      "productsSaved": 8,
      "error": null
    }
  ]
}
```

## 定时执行

### 方法1: 使用Cron Triggers（推荐）

在`wrangler.toml`中添加：
```toml
[triggers]
crons = ["0 */6 * * *"]  # 每6小时执行一次
```

然后在worker.js中添加scheduled事件处理：
```javascript
export default {
  async fetch(request, env, ctx) {
    return handleRequest(request, env);
  },
  
  async scheduled(event, env, ctx) {
    // 定时执行爬取
    const mallIds = await fetchShopIds(env);
    await processShops(mallIds, env);
  }
};
```

### 方法2: 外部调用

使用GitHub Actions、其他服务器或定时任务服务定期调用API端点。

## 注意事项

1. **执行时间限制**: 
   - 免费版: 10ms CPU时间
   - 付费版: 50ms CPU时间
   - 建议分批处理，每次处理少量商店

2. **Cookie管理**: 
   - Cookie存储在环境变量中
   - 需要定期更新过期的Cookie
   - 可以通过API更新Cookie

3. **错误处理**: 
   - 包含完整的错误处理和重试机制
   - 429错误会自动延迟重试
   - 详细的日志记录

4. **成本控制**: 
   - 免费版每天10万次请求
   - 超出后按使用量付费
   - 建议设置合理的请求频率

## 更新Cookie

当Cookie过期时，需要手动更新：

1. 获取新的cookie.json文件
2. 转换为base64格式
3. 更新环境变量：
```bash
wrangler secret put TEMU_COOKIES
```

## 监控和调试

1. 查看实时日志：
```bash
wrangler tail
```

2. 查看部署状态：
```bash
wrangler deployments list
```

3. 测试本地开发：
```bash
wrangler dev
```

## 故障排除

1. **Cookie问题**: 使用`/test-cookie`端点检查Cookie状态
2. **API连接问题**: 检查后端API URL和Token是否正确
3. **执行超时**: 减少批次大小或分多次执行
4. **429错误**: 增加请求间隔时间

## 与Python版本的差异

1. **执行模式**: 从批量处理改为按需处理
2. **存储方式**: 使用环境变量替代文件系统
3. **日志系统**: 使用console.log替代Python logging
4. **异步处理**: 全面使用async/await模式
5. **错误处理**: 适配JavaScript的错误处理机制

这个Cloudflare Workers版本保持了与Python版本相同的核心业务逻辑，同时充分利用了Cloudflare平台的优势。
