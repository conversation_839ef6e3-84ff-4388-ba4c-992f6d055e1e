2025-04-10 11:11:03,410 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250410_111103.log
2025-04-10 11:11:03,411 [INFO] ==================================================
2025-04-10 11:11:03,411 [INFO] 开始运行Temu数据抓取
2025-04-10 11:11:03,444 [INFO] 时间: 2025-04-10 11:11:03
2025-04-10 11:11:03,445 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250410_111103.log
2025-04-10 11:11:03,445 [INFO] ==================================================
2025-04-10 11:11:03,446 [INFO] 
更新Temu Cookie...
2025-04-10 11:11:03,446 [INFO] 加载初始cookie文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json
2025-04-10 11:11:03,447 [INFO] 成功加载初始cookie，共19个
2025-04-10 11:11:03,448 [INFO] 开始访问Temu主页...
2025-04-10 11:11:04,344 [INFO] Temu主页访问状态码: 200
2025-04-10 11:11:04,345 [INFO] 成功更新cookie文件，共19个cookie
2025-04-10 11:11:04,346 [INFO] Cookie更新成功，继续数据抓取
2025-04-10 11:11:04,346 [INFO] 
获取商店ID列表...
2025-04-10 11:11:04,346 [INFO] 请求商店ID列表: http://172.25.165.28:8055/items/shop_data
2025-04-10 11:11:04,383 [INFO] 获取商店ID状态码: 200
2025-04-10 11:11:04,383 [INFO] 获取到的商店数据: {
  "data": [
    {
      "mall_id": "634418213326762"
    },
    {
      "mall_id": "634418210750968"
    },
    {
      "mall_id": "634418214390089"
    }
  ]
}...
2025-04-10 11:11:04,384 [INFO] 获取到的全部mall_id数量: 3
2025-04-10 11:11:04,385 [INFO] 去重后的mall_id数量: 3
2025-04-10 11:11:04,385 [INFO] 去除了 0 个重复的mall_id
2025-04-10 11:11:04,385 [INFO] 最终使用的商店ID列表: ['634418213326762', '634418210750968', '634418214390089']
2025-04-10 11:11:04,385 [INFO] 
==================================================
2025-04-10 11:11:04,386 [INFO] 正在处理商店 1/3: mall_id 634418213326762
2025-04-10 11:11:04,386 [INFO] ==================================================
2025-04-10 11:11:04,386 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418213326762
2025-04-10 11:11:04,386 [INFO] 请求payload: {"mallId": "634418213326762", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418213326762", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-04-10 11:11:04,386 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-04-10 11:11:04,394 [INFO] 成功加载cookie，包含 19 个条目
2025-04-10 11:11:04,394 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-04-10 11:11:05,611 [INFO] Temu API状态码: 200
2025-04-10 11:11:05,611 [INFO] Temu返回数据结构: ['success', 'error_code', 'errorCode', 'result']
2025-04-10 11:11:05,612 [INFO] 成功获取到8件商品，尽管page_size设置为1
2025-04-10 11:11:05,612 [INFO] 找到'result'键，包含店铺数据
2025-04-10 11:11:05,612 [INFO] 成功获取到8件商品
2025-04-10 11:11:05,613 [INFO] 处理商店数据...
2025-04-10 11:11:05,613 [INFO] 处理后的商店数据: {"update_time": "2025-04-10 11:11:05", "mall_id": "634418213326762", "mall_name": "Double Accessories Studio", "mall_logo": "https://img.kwcdn.com/supplier-public-tag/1fa3205448/d184f556-a99e-421d-b931-6269f98650f8_300x300.png", "goods_sales_num": 71000, "goods_num": 328, "review_num": 1094}
2025-04-10 11:11:05,614 [INFO] 保存商店数据: mall_id=634418213326762
2025-04-10 11:11:05,696 [INFO] 成功更新商店数据: 634418213326762
2025-04-10 11:11:05,783 [INFO] 成功记录商店变更日志: shop_data_id=83, 订单增长: 0, 增长率: 0.0%
2025-04-10 11:11:05,784 [INFO] 处理产品数据...
2025-04-10 11:11:05,785 [INFO] 找到8个产品
2025-04-10 11:11:05,785 [INFO] 价格转换: 576美分 -> 5.76美元
2025-04-10 11:11:05,785 [INFO] 处理后的产品数据 1: {"update_time": "2025-04-10 11:11:05", "mall_id": "634418213326762", "goods_id": "601099716475969", "title": "订制蛋糕装饰、洗礼蛋糕装饰、上帝祝福蛋糕装饰、压克力蛋糕挂件、十字架蛋糕装饰、木质蛋糕装饰、金色、适合婚礼、派对、生日等场景", "image_url": "https://img.kwcdn.com/product/open/09175013fcfa41579143c90535d9d7f9-goods.jpeg", "sales_num": 750, "price": 5.76, "comment": 14}
2025-04-10 11:11:05,786 [INFO] 价格转换: 5107美分 -> 51.07美元
2025-04-10 11:11:05,786 [INFO] 处理后的产品数据 2: {"update_time": "2025-04-10 11:11:05", "mall_id": "634418213326762", "goods_id": "601099658086716", "title": "彩色亚克力板，9件套彩色亚克力闪板，9款颜色亚克力闪板，6.7 x6.7 x 1/8 英寸激光切割亚克力板，九款颜色适用于 DIY 项目，创意无限；可用作展示、展览，吸睛十足", "image_url": "https://img.kwcdn.com/product/open/2024-09-03/1725356127913-55812781df6d43d09ad02cdf5343e52b-goods.jpeg", "sales_num": 95, "price": 51.07, "comment": 2}
2025-04-10 11:11:05,786 [INFO] 价格转换: 592美分 -> 5.92美元
2025-04-10 11:11:05,786 [INFO] 价格转换: 394美分 -> 3.94美元
2025-04-10 11:11:05,786 [INFO] 价格转换: 815美分 -> 8.15美元
2025-04-10 11:11:05,787 [INFO] 价格转换: 468美分 -> 4.68美元
2025-04-10 11:11:05,787 [INFO] 价格转换: 505美分 -> 5.05美元
2025-04-10 11:11:05,787 [INFO] 价格转换: 475美分 -> 4.75美元
2025-04-10 11:11:05,787 [INFO] 为mall_id 634418213326762找到8个产品
2025-04-10 11:11:05,787 [INFO] 正在保存产品 1/8 (goods_id: 601099716475969)
2025-04-10 11:11:05,787 [INFO] 保存/更新产品数据: goods_id=601099716475969
2025-04-10 11:11:05,893 [INFO] 成功更新产品数据: 601099716475969
2025-04-10 11:11:05,976 [INFO] 成功记录产品变更日志: product_data_id=784, 订单增长: 12, 评论增长: 1
2025-04-10 11:11:06,177 [INFO] 正在保存产品 2/8 (goods_id: 601099658086716)
2025-04-10 11:11:06,178 [INFO] 保存/更新产品数据: goods_id=601099658086716
2025-04-10 11:11:06,514 [INFO] 成功更新产品数据: 601099658086716
2025-04-10 11:11:07,003 [INFO] 成功记录产品变更日志: product_data_id=787, 订单增长: 1, 评论增长: 0
2025-04-10 11:11:07,205 [INFO] 正在保存产品 3/8 (goods_id: 601099798454912)
2025-04-10 11:11:07,205 [INFO] 保存/更新产品数据: goods_id=601099798454912
2025-04-10 11:11:07,521 [INFO] 成功更新产品数据: 601099798454912
2025-04-10 11:11:07,689 [INFO] 成功记录产品变更日志: product_data_id=786, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:07,890 [INFO] 正在保存产品 4/8 (goods_id: 601099718821759)
2025-04-10 11:11:07,890 [INFO] 保存/更新产品数据: goods_id=601099718821759
2025-04-10 11:11:08,135 [INFO] 成功更新产品数据: 601099718821759
2025-04-10 11:11:08,316 [INFO] 成功记录产品变更日志: product_data_id=785, 订单增长: 22, 评论增长: 0
2025-04-10 11:11:08,517 [INFO] 正在保存产品 5/8 (goods_id: 601099691798205)
2025-04-10 11:11:08,518 [INFO] 保存/更新产品数据: goods_id=601099691798205
2025-04-10 11:11:08,639 [INFO] 成功创建新产品数据: 601099691798205
2025-04-10 11:11:08,720 [INFO] 成功记录产品变更日志: product_data_id=808, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:08,922 [INFO] 正在保存产品 6/8 (goods_id: 601099646014540)
2025-04-10 11:11:08,922 [INFO] 保存/更新产品数据: goods_id=601099646014540
2025-04-10 11:11:09,116 [INFO] 成功更新产品数据: 601099646014540
2025-04-10 11:11:09,184 [INFO] 成功记录产品变更日志: product_data_id=789, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:09,385 [INFO] 正在保存产品 7/8 (goods_id: 601099578673098)
2025-04-10 11:11:09,385 [INFO] 保存/更新产品数据: goods_id=601099578673098
2025-04-10 11:11:09,498 [INFO] 成功更新产品数据: 601099578673098
2025-04-10 11:11:09,586 [INFO] 成功记录产品变更日志: product_data_id=791, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:09,787 [INFO] 正在保存产品 8/8 (goods_id: 601099681670198)
2025-04-10 11:11:09,788 [INFO] 保存/更新产品数据: goods_id=601099681670198
2025-04-10 11:11:09,894 [INFO] 成功更新产品数据: 601099681670198
2025-04-10 11:11:09,977 [INFO] 成功记录产品变更日志: product_data_id=790, 订单增长: 1, 评论增长: 0
2025-04-10 11:11:10,178 [INFO] 商店 634418213326762 处理完成: 成功保存 8/8 个产品
2025-04-10 11:11:11,179 [INFO] 
==================================================
2025-04-10 11:11:11,179 [INFO] 正在处理商店 2/3: mall_id 634418210750968
2025-04-10 11:11:11,179 [INFO] ==================================================
2025-04-10 11:11:11,179 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418210750968
2025-04-10 11:11:11,179 [INFO] 请求payload: {"mallId": "634418210750968", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418210750968", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-04-10 11:11:11,180 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-04-10 11:11:11,180 [INFO] 成功加载cookie，包含 19 个条目
2025-04-10 11:11:11,181 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-04-10 11:11:12,258 [INFO] Temu API状态码: 200
2025-04-10 11:11:12,259 [INFO] Temu返回数据结构: ['success', 'error_code', 'errorCode', 'result']
2025-04-10 11:11:12,259 [INFO] 成功获取到8件商品，尽管page_size设置为1
2025-04-10 11:11:12,260 [INFO] 找到'result'键，包含店铺数据
2025-04-10 11:11:12,260 [INFO] 成功获取到8件商品
2025-04-10 11:11:12,261 [INFO] 处理商店数据...
2025-04-10 11:11:12,262 [INFO] 处理后的商店数据: {"update_time": "2025-04-10 11:11:12", "mall_id": "634418210750968", "mall_name": "WoyiCo", "mall_logo": "https://img.kwcdn.com/supplier-public-tag/1e78ea0e3d/03f3539d-8028-4bc1-8c46-206a25daa5a4_300x300.png", "goods_sales_num": 100000, "goods_num": 209, "review_num": 515}
2025-04-10 11:11:12,262 [INFO] 保存商店数据: mall_id=634418210750968
2025-04-10 11:11:12,339 [INFO] 成功更新商店数据: 634418210750968
2025-04-10 11:11:12,407 [INFO] 成功记录商店变更日志: shop_data_id=84, 订单增长: 0, 增长率: 0.0%
2025-04-10 11:11:12,407 [INFO] 处理产品数据...
2025-04-10 11:11:12,407 [INFO] 找到8个产品
2025-04-10 11:11:12,408 [INFO] 价格转换: 1104美分 -> 11.04美元
2025-04-10 11:11:12,408 [INFO] 处理后的产品数据 1: {"update_time": "2025-04-10 11:11:12", "mall_id": "634418210750968", "goods_id": "601099620411403", "title": "2件装个性化闪亮心形Y2K可爱DIY双层字母项链耳环套装，四季皆宜的完美女性生日礼物", "image_url": "https://img.kwcdn.com/product/fancy/729b166f-6a4f-4d30-bdb1-795d49729243.jpg", "sales_num": 3, "price": 11.04, "comment": 1}
2025-04-10 11:11:12,408 [INFO] 价格转换: 542美分 -> 5.42美元
2025-04-10 11:11:12,408 [INFO] 处理后的产品数据 2: {"update_time": "2025-04-10 11:11:12", "mall_id": "634418210750968", "goods_id": "601099573790697", "title": "定制热粉色亚克力名字项链，带金色点缀 - 可爱经典字体设计，适合日常穿着或送礼", "image_url": "https://img.kwcdn.com/product/fancy/28924059-35ee-49b6-8bd8-18100500a950.jpg", "sales_num": 7, "price": 5.42, "comment": 93}
2025-04-10 11:11:12,409 [INFO] 价格转换: 470美分 -> 4.7美元
2025-04-10 11:11:12,409 [INFO] 价格转换: 1074美分 -> 10.74美元
2025-04-10 11:11:12,409 [INFO] 价格转换: 466美分 -> 4.66美元
2025-04-10 11:11:12,409 [INFO] 价格转换: 686美分 -> 6.86美元
2025-04-10 11:11:12,409 [INFO] 价格转换: 1330美分 -> 13.3美元
2025-04-10 11:11:12,409 [INFO] 价格转换: 1294美分 -> 12.94美元
2025-04-10 11:11:12,409 [INFO] 为mall_id 634418210750968找到8个产品
2025-04-10 11:11:12,410 [INFO] 正在保存产品 1/8 (goods_id: 601099620411403)
2025-04-10 11:11:12,410 [INFO] 保存/更新产品数据: goods_id=601099620411403
2025-04-10 11:11:12,507 [INFO] 成功更新产品数据: 601099620411403
2025-04-10 11:11:12,575 [INFO] 成功记录产品变更日志: product_data_id=802, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:12,777 [INFO] 正在保存产品 2/8 (goods_id: 601099573790697)
2025-04-10 11:11:12,777 [INFO] 保存/更新产品数据: goods_id=601099573790697
2025-04-10 11:11:12,876 [INFO] 成功更新产品数据: 601099573790697
2025-04-10 11:11:12,956 [INFO] 成功记录产品变更日志: product_data_id=800, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:13,157 [INFO] 正在保存产品 3/8 (goods_id: 601099680825855)
2025-04-10 11:11:13,158 [INFO] 保存/更新产品数据: goods_id=601099680825855
2025-04-10 11:11:13,261 [INFO] 成功更新产品数据: 601099680825855
2025-04-10 11:11:13,329 [INFO] 成功记录产品变更日志: product_data_id=801, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:13,530 [INFO] 正在保存产品 4/8 (goods_id: 601099623136212)
2025-04-10 11:11:13,531 [INFO] 保存/更新产品数据: goods_id=601099623136212
2025-04-10 11:11:13,629 [INFO] 成功更新产品数据: 601099623136212
2025-04-10 11:11:13,702 [INFO] 成功记录产品变更日志: product_data_id=806, 订单增长: -998, 评论增长: 0
2025-04-10 11:11:13,903 [INFO] 正在保存产品 5/8 (goods_id: 601099549323448)
2025-04-10 11:11:13,903 [INFO] 保存/更新产品数据: goods_id=601099549323448
2025-04-10 11:11:14,012 [INFO] 成功更新产品数据: 601099549323448
2025-04-10 11:11:14,079 [INFO] 成功记录产品变更日志: product_data_id=803, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:14,280 [INFO] 正在保存产品 6/8 (goods_id: 601099585661327)
2025-04-10 11:11:14,280 [INFO] 保存/更新产品数据: goods_id=601099585661327
2025-04-10 11:11:14,396 [INFO] 成功更新产品数据: 601099585661327
2025-04-10 11:11:14,477 [INFO] 成功记录产品变更日志: product_data_id=804, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:14,679 [INFO] 正在保存产品 7/8 (goods_id: 601099627217746)
2025-04-10 11:11:14,679 [INFO] 保存/更新产品数据: goods_id=601099627217746
2025-04-10 11:11:14,836 [INFO] 成功更新产品数据: 601099627217746
2025-04-10 11:11:14,905 [INFO] 成功记录产品变更日志: product_data_id=807, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:15,106 [INFO] 正在保存产品 8/8 (goods_id: 601099643685864)
2025-04-10 11:11:15,106 [INFO] 保存/更新产品数据: goods_id=601099643685864
2025-04-10 11:11:15,245 [INFO] 成功创建新产品数据: 601099643685864
2025-04-10 11:11:15,325 [INFO] 成功记录产品变更日志: product_data_id=809, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:15,526 [INFO] 商店 634418210750968 处理完成: 成功保存 8/8 个产品
2025-04-10 11:11:16,527 [INFO] 
==================================================
2025-04-10 11:11:16,527 [INFO] 正在处理商店 3/3: mall_id 634418214390089
2025-04-10 11:11:16,527 [INFO] ==================================================
2025-04-10 11:11:16,527 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418214390089
2025-04-10 11:11:16,528 [INFO] 请求payload: {"mallId": "634418214390089", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418214390089", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-04-10 11:11:16,528 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-04-10 11:11:16,528 [INFO] 成功加载cookie，包含 19 个条目
2025-04-10 11:11:16,529 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-04-10 11:11:17,591 [INFO] Temu API状态码: 200
2025-04-10 11:11:17,592 [INFO] Temu返回数据结构: ['success', 'error_code', 'errorCode', 'result']
2025-04-10 11:11:17,593 [INFO] 成功获取到8件商品，尽管page_size设置为1
2025-04-10 11:11:17,593 [INFO] 找到'result'键，包含店铺数据
2025-04-10 11:11:17,593 [INFO] 成功获取到8件商品
2025-04-10 11:11:17,594 [INFO] 处理商店数据...
2025-04-10 11:11:17,595 [INFO] 处理后的商店数据: {"update_time": "2025-04-10 11:11:17", "mall_id": "634418214390089", "mall_name": "Dream Custom Studio", "mall_logo": "https://img.kwcdn.com/supplier-public-tag/1f19f102eb8/19efd7ef-2d75-4046-af12-060aca8f0a3a_300x300.jpeg", "goods_sales_num": 45000, "goods_num": 142, "review_num": 722}
2025-04-10 11:11:17,595 [INFO] 保存商店数据: mall_id=634418214390089
2025-04-10 11:11:17,671 [INFO] 成功更新商店数据: 634418214390089
2025-04-10 11:11:17,735 [INFO] 成功记录商店变更日志: shop_data_id=85, 订单增长: 0, 增长率: 0.0%
2025-04-10 11:11:17,736 [INFO] 处理产品数据...
2025-04-10 11:11:17,736 [INFO] 找到8个产品
2025-04-10 11:11:17,736 [INFO] 价格转换: 583美分 -> 5.83美元
2025-04-10 11:11:17,737 [INFO] 处理后的产品数据 1: {"update_time": "2025-04-10 11:11:17", "mall_id": "634418214390089", "goods_id": "601099577918790", "title": "定制亚克力钥匙圈，时尚女孩设计 - 个人化姓名标签，生日、毕业礼物和旅行必需品的理想选择，可爱钥匙扣", "image_url": "https://img.kwcdn.com/product/open/2024-05-14/1715680112315-2d719dbe936149c296187cf6189a07bf-goods.jpeg", "sales_num": 850, "price": 5.83, "comment": 12}
2025-04-10 11:11:17,737 [INFO] 价格转换: 601美分 -> 6.01美元
2025-04-10 11:11:17,737 [INFO] 处理后的产品数据 2: {"update_time": "2025-04-10 11:11:17", "mall_id": "634418214390089", "goods_id": "601099636408430", "title": "一款个性化的棒球垒球丙烯酸字母数字挂件，带龙虾扣环 - 装饰性圆形女士钥匙扣，适用于妇女节，运动爱好者的DIY手工艺品，是朋友和家人的理想惊喜礼物 - 单个产品", "image_url": "https://img.kwcdn.com/product/open/2024-08-12/1723447183842-e2e075d4bb8a4818a8083a4f880e83fe-goods.jpeg", "sales_num": 354, "price": 6.01, "comment": 0}
2025-04-10 11:11:17,738 [INFO] 价格转换: 549美分 -> 5.49美元
2025-04-10 11:11:17,738 [INFO] 价格转换: 635美分 -> 6.35美元
2025-04-10 11:11:17,738 [INFO] 价格转换: 523美分 -> 5.23美元
2025-04-10 11:11:17,738 [INFO] 价格转换: 505美分 -> 5.05美元
2025-04-10 11:11:17,738 [INFO] 价格转换: 506美分 -> 5.06美元
2025-04-10 11:11:17,738 [INFO] 价格转换: 558美分 -> 5.58美元
2025-04-10 11:11:17,738 [INFO] 为mall_id 634418214390089找到8个产品
2025-04-10 11:11:17,738 [INFO] 正在保存产品 1/8 (goods_id: 601099577918790)
2025-04-10 11:11:17,738 [INFO] 保存/更新产品数据: goods_id=601099577918790
2025-04-10 11:11:17,848 [INFO] 成功更新产品数据: 601099577918790
2025-04-10 11:11:17,933 [INFO] 成功记录产品变更日志: product_data_id=792, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:18,135 [INFO] 正在保存产品 2/8 (goods_id: 601099636408430)
2025-04-10 11:11:18,135 [INFO] 保存/更新产品数据: goods_id=601099636408430
2025-04-10 11:11:18,230 [INFO] 成功更新产品数据: 601099636408430
2025-04-10 11:11:18,301 [INFO] 成功记录产品变更日志: product_data_id=794, 订单增长: 1, 评论增长: 0
2025-04-10 11:11:18,504 [INFO] 正在保存产品 3/8 (goods_id: 601099726445248)
2025-04-10 11:11:18,504 [INFO] 保存/更新产品数据: goods_id=601099726445248
2025-04-10 11:11:18,612 [INFO] 成功更新产品数据: 601099726445248
2025-04-10 11:11:18,691 [INFO] 成功记录产品变更日志: product_data_id=795, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:18,893 [INFO] 正在保存产品 4/8 (goods_id: 601099768649984)
2025-04-10 11:11:18,893 [INFO] 保存/更新产品数据: goods_id=601099768649984
2025-04-10 11:11:19,006 [INFO] 成功更新产品数据: 601099768649984
2025-04-10 11:11:19,086 [INFO] 成功记录产品变更日志: product_data_id=798, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:19,287 [INFO] 正在保存产品 5/8 (goods_id: 601100566244067)
2025-04-10 11:11:19,287 [INFO] 保存/更新产品数据: goods_id=601100566244067
2025-04-10 11:11:19,409 [INFO] 成功更新产品数据: 601100566244067
2025-04-10 11:11:19,507 [INFO] 成功记录产品变更日志: product_data_id=797, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:19,708 [INFO] 正在保存产品 6/8 (goods_id: 601099646033323)
2025-04-10 11:11:19,708 [INFO] 保存/更新产品数据: goods_id=601099646033323
2025-04-10 11:11:19,808 [INFO] 成功创建新产品数据: 601099646033323
2025-04-10 11:11:19,876 [INFO] 成功记录产品变更日志: product_data_id=810, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:20,077 [INFO] 正在保存产品 7/8 (goods_id: 601100565893926)
2025-04-10 11:11:20,077 [INFO] 保存/更新产品数据: goods_id=601100565893926
2025-04-10 11:11:20,173 [INFO] 成功更新产品数据: 601100565893926
2025-04-10 11:11:20,240 [INFO] 成功记录产品变更日志: product_data_id=793, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:20,441 [INFO] 正在保存产品 8/8 (goods_id: 601099711736131)
2025-04-10 11:11:20,442 [INFO] 保存/更新产品数据: goods_id=601099711736131
2025-04-10 11:11:20,538 [INFO] 成功创建新产品数据: 601099711736131
2025-04-10 11:11:20,611 [INFO] 成功记录产品变更日志: product_data_id=811, 订单增长: 0, 评论增长: 0
2025-04-10 11:11:20,813 [INFO] 商店 634418214390089 处理完成: 成功保存 8/8 个产品
2025-04-10 11:11:21,814 [INFO] 
==================================================
2025-04-10 11:11:21,814 [INFO] Temu数据抓取汇总
2025-04-10 11:11:21,814 [INFO] 处理时间: 2025-04-10 11:11:21
2025-04-10 11:11:21,814 [INFO] 处理商店总数: 3
2025-04-10 11:11:21,814 [INFO] 成功保存商店数: 3
2025-04-10 11:11:21,815 [INFO] 处理产品总数: 24
2025-04-10 11:11:21,815 [INFO] 成功保存产品数: 24
2025-04-10 11:11:21,815 [INFO] ==================================================
