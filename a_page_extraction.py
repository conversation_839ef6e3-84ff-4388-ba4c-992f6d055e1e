#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
页面数据提取版本
直接从页面HTML中提取数据，不依赖API接口
"""

import requests
import json
from datetime import datetime
import time
import traceback
import pytz
import os
import logging
import random
import re
from bs4 import BeautifulSoup

# 导入配置
from config import (
    TEMU_API_CONFIG,
    BACKEND_API_CONFIG,
    TIMEZONE_CONFIG
)

# 导入主程序的函数
from a import (
    get_hk_time,
    save_shop_data,
    save_product_data,
    fetch_shop_ids
)

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"temu_crawler_extraction_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logging.info(f"页面提取日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def load_cookies_from_file():
    """从cookie.json文件加载cookie信息"""
    try:
        cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        logging.info(f"尝试从 {cookie_file_path} 加载cookie")
        
        if not os.path.exists(cookie_file_path):
            logging.warning(f"警告: cookie文件不存在: {cookie_file_path}")
            return None
            
        with open(cookie_file_path, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
            
        # 将cookie列表转换为字符串格式
        cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_data if cookie.get('name') and cookie.get('value')])
        logging.info(f"成功加载cookie，包含 {len(cookies_data)} 个条目")
        return cookie_str
    except Exception as e:
        logging.error(f"加载cookie文件时出错: {str(e)}")
        traceback.print_exc()
        return None

def create_session():
    """创建session"""
    session = requests.Session()
    
    # 设置浏览器headers（不要压缩，避免解码问题）
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'identity',  # 不使用压缩
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    })
    
    # 加载cookie
    cookies = load_cookies_from_file()
    if cookies:
        for cookie_pair in cookies.split('; '):
            if '=' in cookie_pair:
                name, value = cookie_pair.split('=', 1)
                session.cookies.set(name, value, domain='.temu.com')
        logging.info("✅ Cookie已加载到session")
    
    return session

def extract_data_from_page(page_content, mall_id):
    """从页面HTML中提取数据"""
    logging.info("🔍 开始从页面提取数据...")
    
    extracted_data = {
        'mall_id': mall_id,
        'mall_name': None,
        'products': [],
        'raw_data': {}
    }
    
    try:
        # 方法1：提取JavaScript中的JSON数据
        logging.info("方法1：查找页面中的JSON数据...")
        
        # 查找常见的数据模式
        json_patterns = [
            r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
            r'window\.__APOLLO_STATE__\s*=\s*({.*?});',
            r'window\.__NEXT_DATA__\s*=\s*({.*?});',
            r'window\.pageData\s*=\s*({.*?});',
            r'window\.mallData\s*=\s*({.*?});',
            r'__INITIAL_PROPS__\s*=\s*({.*?});'
        ]
        
        for i, pattern in enumerate(json_patterns, 1):
            matches = re.finditer(pattern, page_content, re.DOTALL)
            for match in matches:
                try:
                    json_str = match.group(1)
                    data = json.loads(json_str)
                    logging.info(f"✅ 方法1.{i} 成功提取JSON数据，大小: {len(json_str)} 字符")
                    extracted_data['raw_data'][f'pattern_{i}'] = data
                    
                    # 尝试从数据中提取商店信息
                    shop_info = extract_shop_info_from_json(data)
                    if shop_info:
                        extracted_data.update(shop_info)
                        
                except json.JSONDecodeError:
                    continue
                except Exception as e:
                    logging.warning(f"解析JSON数据时出错: {str(e)}")
                    continue
        
        # 方法2：使用BeautifulSoup解析HTML
        logging.info("方法2：使用BeautifulSoup解析HTML...")
        try:
            soup = BeautifulSoup(page_content, 'html.parser')
            
            # 提取商店名称
            title_selectors = [
                'h1',
                '.mall-name',
                '.store-name',
                '[data-testid="mall-name"]',
                'title'
            ]
            
            for selector in title_selectors:
                element = soup.select_one(selector)
                if element and element.get_text().strip():
                    extracted_data['mall_name'] = element.get_text().strip()
                    logging.info(f"✅ 从HTML提取商店名称: {extracted_data['mall_name']}")
                    break
            
            # 提取产品信息
            product_selectors = [
                '.product-item',
                '.goods-item',
                '[data-testid="product"]',
                '.item-card'
            ]
            
            for selector in product_selectors:
                products = soup.select(selector)
                if products:
                    logging.info(f"✅ 找到 {len(products)} 个产品元素")
                    for product in products[:10]:  # 限制前10个
                        product_info = extract_product_info_from_element(product)
                        if product_info:
                            extracted_data['products'].append(product_info)
                    break
                    
        except Exception as e:
            logging.warning(f"BeautifulSoup解析时出错: {str(e)}")
        
        # 方法3：正则表达式直接提取
        logging.info("方法3：使用正则表达式直接提取...")
        
        # 提取商店名称
        name_patterns = [
            r'"mall_name":"([^"]+)"',
            r'"storeName":"([^"]+)"',
            r'"name":"([^"]+)"',
            r'<title>([^<]+)</title>'
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, page_content)
            if match and not extracted_data['mall_name']:
                extracted_data['mall_name'] = match.group(1)
                logging.info(f"✅ 正则提取商店名称: {extracted_data['mall_name']}")
                break
        
        # 提取产品价格和标题
        product_patterns = [
            r'"title":"([^"]+)".*?"price":"([^"]+)"',
            r'"goods_name":"([^"]+)".*?"price":"([^"]+)"'
        ]
        
        for pattern in product_patterns:
            matches = re.finditer(pattern, page_content)
            for match in matches:
                if len(extracted_data['products']) < 20:  # 限制数量
                    product_info = {
                        'title': match.group(1),
                        'price': match.group(2),
                        'mall_id': mall_id
                    }
                    extracted_data['products'].append(product_info)
        
        logging.info(f"📊 数据提取完成:")
        logging.info(f"  - 商店名称: {extracted_data['mall_name'] or '未找到'}")
        logging.info(f"  - 产品数量: {len(extracted_data['products'])}")
        logging.info(f"  - 原始数据块: {len(extracted_data['raw_data'])}")
        
        return extracted_data
        
    except Exception as e:
        logging.error(f"页面数据提取时发生异常: {str(e)}")
        traceback.print_exc()
        return extracted_data

def extract_shop_info_from_json(data):
    """从JSON数据中提取商店信息"""
    shop_info = {}
    
    try:
        # 递归搜索商店信息
        def find_shop_data(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    # 查找商店名称
                    if key in ['mall_name', 'storeName', 'name', 'title'] and isinstance(value, str):
                        if not shop_info.get('mall_name'):
                            shop_info['mall_name'] = value
                            logging.info(f"从JSON路径 {current_path} 找到商店名称: {value}")
                    
                    # 查找商店ID
                    if key in ['mall_id', 'storeId', 'id'] and isinstance(value, (str, int)):
                        shop_info['mall_id'] = str(value)
                    
                    # 递归搜索
                    if isinstance(value, (dict, list)):
                        find_shop_data(value, current_path)
                        
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    find_shop_data(item, f"{path}[{i}]")
        
        find_shop_data(data)
        return shop_info
        
    except Exception as e:
        logging.warning(f"从JSON提取商店信息时出错: {str(e)}")
        return {}

def extract_product_info_from_element(element):
    """从HTML元素中提取产品信息"""
    try:
        product_info = {}
        
        # 提取标题
        title_selectors = ['h3', '.title', '.name', '.product-title']
        for selector in title_selectors:
            title_elem = element.select_one(selector)
            if title_elem:
                product_info['title'] = title_elem.get_text().strip()
                break
        
        # 提取价格
        price_selectors = ['.price', '.cost', '.amount', '[data-testid="price"]']
        for selector in price_selectors:
            price_elem = element.select_one(selector)
            if price_elem:
                product_info['price'] = price_elem.get_text().strip()
                break
        
        return product_info if product_info else None
        
    except Exception as e:
        logging.warning(f"提取产品信息时出错: {str(e)}")
        return None

def fetch_temu_shop_data_extraction(mall_id):
    """通过页面提取获取商店数据"""
    logging.info(f"🎯 开始页面数据提取: mall_id={mall_id}")
    
    session = create_session()
    mall_url = f"https://www.temu.com/mall.html?mall_id={mall_id}"
    
    try:
        logging.info(f"🌐 访问商店页面: {mall_url}")
        
        # 访问页面
        response = session.get(mall_url, timeout=30)
        logging.info(f"页面访问状态码: {response.status_code}")
        logging.info(f"响应头: {dict(response.headers)}")

        if response.status_code != 200:
            logging.error(f"页面访问失败: {response.status_code}")
            return None

        logging.info("✅ 成功访问商店页面")

        # 检查响应内容
        logging.info(f"响应内容长度: {len(response.content)} 字节")
        logging.info(f"响应文本长度: {len(response.text)} 字符")
        logging.info(f"响应编码: {response.encoding}")

        # 保存页面内容用于调试
        debug_file = f"debug_page_{mall_id}.html"
        try:
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(response.text)
            logging.info(f"📄 页面内容已保存到: {debug_file}")
        except Exception as e:
            logging.warning(f"保存页面内容时出错: {str(e)}")
            # 尝试保存原始字节
            with open(f"debug_page_{mall_id}.raw", 'wb') as f:
                f.write(response.content)
            logging.info(f"📄 原始内容已保存到: debug_page_{mall_id}.raw")

        # 检查页面内容是否有效
        if len(response.text) < 100:
            logging.warning("⚠️ 页面内容过短，可能是重定向或错误页面")
            logging.info(f"页面内容预览: {response.text[:200]}")
            return None

        # 检查是否是重定向页面
        if "redirect" in response.text.lower() or "location.href" in response.text:
            logging.warning("⚠️ 检测到重定向，可能需要处理JavaScript重定向")

        # 检查是否需要登录
        if "login" in response.text.lower() and "password" in response.text.lower():
            logging.warning("⚠️ 检测到登录页面，cookie可能已过期")
            return None
        
        # 提取数据
        extracted_data = extract_data_from_page(response.text, mall_id)
        
        if extracted_data['mall_name'] or extracted_data['products'] or extracted_data['raw_data']:
            logging.info("✅ 页面数据提取成功！")
            return extracted_data
        else:
            logging.warning("⚠️ 未能从页面提取到有效数据")
            return None
        
    except Exception as e:
        logging.error(f"页面数据提取时发生异常: {str(e)}")
        traceback.print_exc()
        return None
    finally:
        session.close()

def main():
    """页面提取主函数"""
    logging.info("="*60)
    logging.info("开始运行Temu数据抓取 - 页面数据提取模式")
    logging.info(f"时间: {get_hk_time()}")
    logging.info(f"日志文件: {log_file}")
    logging.info("="*60)
    
    logging.info("📄 页面提取流程：")
    logging.info("1. 访问商店页面")
    logging.info("2. 提取页面中的JSON数据")
    logging.info("3. 解析HTML元素")
    logging.info("4. 使用正则表达式提取")
    logging.info("5. 保存提取的数据")
    
    # 测试单个商店
    test_mall_id = "634418212233370"
    
    logging.info(f"\n🎯 测试商店: {test_mall_id}")
    
    result = fetch_temu_shop_data_extraction(test_mall_id)
    
    if result:
        logging.info("🎉 页面数据提取成功！")
        
        # 显示提取的数据
        if result['mall_name']:
            logging.info(f"商店名称: {result['mall_name']}")
        
        if result['products']:
            logging.info(f"提取到 {len(result['products'])} 个产品")
            for i, product in enumerate(result['products'][:3], 1):
                logging.info(f"  产品{i}: {product.get('title', 'N/A')} - {product.get('price', 'N/A')}")
        
        if result['raw_data']:
            logging.info(f"提取到 {len(result['raw_data'])} 个原始数据块")
        
        # 保存提取的数据
        output_file = f"extracted_data_{test_mall_id}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        logging.info(f"📁 提取的数据已保存到: {output_file}")
        
        return True
    else:
        logging.warning("⚠️ 页面数据提取失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 页面数据提取测试成功！")
            print("已成功从页面提取数据，无需依赖API接口")
        else:
            print("\n⚠️ 页面数据提取测试失败")
            print("可能需要调整提取策略")
    except Exception as e:
        logging.error(f"程序执行过程中发生未捕获的异常: {str(e)}")
        traceback.print_exc()
