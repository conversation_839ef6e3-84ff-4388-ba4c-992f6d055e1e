#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版：使用正确的headers和cookie调用Temu API
"""

import json
import time
import logging
import requests
import os
from datetime import datetime

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"fixed_api_correct_headers_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"修复版API调用日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def extract_browser_cookies_and_headers():
    """从浏览器获取cookie和必要的headers"""
    try:
        import undetected_chromedriver as uc
        
        logging.info("🔧 启动浏览器获取cookie和headers...")
        
        # 创建用户数据目录
        user_data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "chrome_user_data")
        
        # 配置Chrome选项
        options = uc.ChromeOptions()
        options.add_argument(f'--user-data-dir={user_data_dir}')
        options.add_argument('--profile-directory=Default')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1200,800')  # 不使用headless，这样可以获取更真实的环境
        
        # 创建浏览器
        driver = uc.Chrome(options=options, version_main=137)
        
        # 访问Temu商店页面，确保所有必要的headers都被设置
        driver.get("https://www.temu.com/mall.html?mall_id=634418212233370")
        time.sleep(5)
        
        # 获取所有cookie
        cookies = driver.get_cookies()
        logging.info(f"📥 获取到 {len(cookies)} 个cookie")
        
        # 转换为requests格式
        cookie_dict = {}
        for cookie in cookies:
            cookie_dict[cookie['name']] = cookie['value']
        
        # 获取User-Agent
        user_agent = driver.execute_script("return navigator.userAgent;")
        
        # 尝试从页面中获取anti-content和其他验证字段
        # 这些字段通常在页面的JavaScript中生成
        anti_content = None
        verify_auth_token = None
        x_phan_data = None
        
        try:
            # 尝试从cookie中获取verifyAuthToken
            if 'verifyAuthToken' in cookie_dict:
                verify_auth_token = cookie_dict['verifyAuthToken']
                logging.info(f"✅ 从cookie获取verifyAuthToken: {verify_auth_token[:20]}...")
            
            # 尝试执行JavaScript获取其他验证字段
            # 这些字段可能在window对象或其他全局变量中
            try:
                anti_content = driver.execute_script("""
                    // 尝试从各种可能的位置获取anti-content
                    if (window.antiContent) return window.antiContent;
                    if (window._anti_content) return window._anti_content;
                    if (window.ANTI_CONTENT) return window.ANTI_CONTENT;
                    
                    // 尝试从meta标签获取
                    var metaTags = document.getElementsByTagName('meta');
                    for (var i = 0; i < metaTags.length; i++) {
                        if (metaTags[i].name === 'anti-content' || metaTags[i].getAttribute('name') === 'anti-content') {
                            return metaTags[i].content;
                        }
                    }
                    
                    return null;
                """)
                
                if anti_content:
                    logging.info(f"✅ 获取到anti-content: {anti_content[:50]}...")
                
            except Exception as e:
                logging.warning(f"⚠️ 无法获取anti-content: {str(e)}")
            
            try:
                x_phan_data = driver.execute_script("""
                    // 尝试从各种可能的位置获取x-phan-data
                    if (window.xPhanData) return window.xPhanData;
                    if (window._x_phan_data) return window._x_phan_data;
                    if (window.X_PHAN_DATA) return window.X_PHAN_DATA;
                    return null;
                """)
                
                if x_phan_data:
                    logging.info(f"✅ 获取到x-phan-data: {x_phan_data[:30]}...")
                
            except Exception as e:
                logging.warning(f"⚠️ 无法获取x-phan-data: {str(e)}")
        
        except Exception as e:
            logging.warning(f"⚠️ 获取验证字段时出错: {str(e)}")
        
        driver.quit()
        
        logging.info("✅ 成功获取浏览器数据")
        return cookie_dict, user_agent, anti_content, verify_auth_token, x_phan_data
        
    except Exception as e:
        logging.error(f"❌ 获取浏览器数据失败: {str(e)}")
        return None, None, None, None, None

def create_correct_api_session(cookies, user_agent, anti_content, verify_auth_token, x_phan_data):
    """创建带有正确headers的API会话"""
    try:
        session = requests.Session()
        
        # 设置cookie
        cookie_string = "; ".join([f"{name}={value}" for name, value in cookies.items()])
        
        # 设置完整的headers，模拟您的curl请求
        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
            'content-type': 'application/json;charset=UTF-8',
            'origin': 'https://www.temu.com',
            'priority': 'u=1, i',
            'referer': 'https://www.temu.com/us-zh-Hans/churongcup-m-4057197278111.html?goods_id=601099524562210&sticky_type=3&refer_page_name=login&refer_page_id=10013_1742359717174_p4ebm7lwee&refer_page_sn=10013&_x_sessn_id=ribybg3rq1',
            'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': user_agent,
            'Cookie': cookie_string
        }
        
        # 添加验证字段（如果获取到的话）
        if anti_content:
            headers['anti-content'] = anti_content
            logging.info("✅ 添加anti-content header")
        
        if verify_auth_token:
            headers['verifyauthtoken'] = verify_auth_token
            logging.info("✅ 添加verifyauthtoken header")
        
        if x_phan_data:
            headers['x-phan-data'] = x_phan_data
            logging.info("✅ 添加x-phan-data header")
        
        session.headers.update(headers)
        
        logging.info("✅ 正确的API会话创建成功")
        logging.info(f"📋 Headers数量: {len(headers)}")
        
        return session
        
    except Exception as e:
        logging.error(f"❌ 创建正确API会话失败: {str(e)}")
        return None

def call_temu_api_with_correct_headers(session, mall_id):
    """使用正确的headers调用Temu API"""
    try:
        logging.info(f"🎯 使用正确headers调用Temu API: {mall_id}")
        
        # 使用正确的API端点
        api_url = "https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList"
        
        # 使用与您curl请求相似的payload
        payload = {
            "mallId": mall_id,
            "mainGoodsIds": ["1"],
            "source_page_sn": "10013",
            "mall_id": mall_id,
            "main_goods_ids": ["1"],
            "filter_items": "",
            "page_number": 1,
            "page_size": 60,  # 使用您curl中的page_size
            "list_id": "r7oe7gyw0vd5xo2z2qja1",
            "scene_code": "mall_rule",
            "page_sn": 10040,
            "page_el_sn": 201265,
            "source": 10018
        }
        
        logging.info(f"📡 调用API: {api_url}")
        logging.info(f"📦 请求payload: {json.dumps(payload, ensure_ascii=False)}")
        
        # 显示关键headers
        key_headers = ['anti-content', 'verifyauthtoken', 'x-phan-data', 'Cookie']
        for header in key_headers:
            if header in session.headers:
                value = session.headers[header]
                if len(value) > 50:
                    logging.info(f"🔑 {header}: {value[:50]}...")
                else:
                    logging.info(f"🔑 {header}: {value}")
            else:
                logging.warning(f"⚠️ 缺少header: {header}")
        
        # 发送POST请求
        response = session.post(api_url, json=payload, timeout=30)
        
        logging.info(f"📊 响应状态码: {response.status_code}")
        logging.info(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                logging.info(f"✅ API调用成功！数据长度: {len(str(data))} 字符")
                
                # 保存成功的响应
                filename = f"fixed_api_success_{mall_id}_{datetime.now().strftime('%H%M%S')}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                logging.info(f"📁 响应数据已保存: {filename}")
                
                # 分析响应数据
                if 'result' in data:
                    result = data['result']
                    if 'mall_name' in result:
                        logging.info(f"🏪 商店名称: {result['mall_name']}")
                    if 'data' in result and 'goods_list' in result['data']:
                        goods_count = len(result['data']['goods_list'])
                        logging.info(f"📦 商品数量: {goods_count}")
                
                return True, data, filename
                
            except json.JSONDecodeError:
                logging.warning(f"⚠️ 响应不是有效的JSON格式")
                logging.info(f"📄 响应内容: {response.text[:500]}...")
                return False, response.text, None
                
        else:
            logging.warning(f"⚠️ API返回状态码: {response.status_code}")
            logging.info(f"📄 响应内容: {response.text[:500]}...")
            return False, response.text, None
        
    except Exception as e:
        logging.error(f"❌ 调用Temu API失败: {str(e)}")
        return False, str(e), None

def main():
    """主函数"""
    logging.info("="*60)
    logging.info("开始使用正确headers调用Temu API")
    logging.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info("="*60)
    
    try:
        # 获取浏览器数据
        cookies, user_agent, anti_content, verify_auth_token, x_phan_data = extract_browser_cookies_and_headers()
        if not cookies:
            logging.error("❌ 无法获取浏览器数据")
            return False
        
        logging.info(f"🍪 获取到 {len(cookies)} 个cookie")
        logging.info(f"🌐 User-Agent: {user_agent[:100]}...")
        
        # 显示关键cookie
        key_cookies = ['AccessToken', 'user_uin', '_bee', 'api_uid', 'verifyAuthToken']
        found_key_cookies = [name for name in key_cookies if name in cookies]
        logging.info(f"🔑 找到关键cookie: {found_key_cookies}")
        
        # 创建API会话
        session = create_correct_api_session(cookies, user_agent, anti_content, verify_auth_token, x_phan_data)
        if not session:
            logging.error("❌ 无法创建API会话")
            return False
        
        # 测试目标商店API
        target_mall_id = "634418212233370"
        success, response_data, filename = call_temu_api_with_correct_headers(session, target_mall_id)
        
        # 汇总结果
        print("\n" + "="*60)
        print("🎉 修复版Temu API测试结果")
        print("="*60)
        
        if success:
            print("✅ API调用成功！")
            print(f"📁 数据文件: {filename}")
            print(f"📊 数据长度: {len(str(response_data))} 字符")
            
            # 尝试提取关键信息
            if isinstance(response_data, dict) and 'result' in response_data:
                result = response_data['result']
                if 'mall_name' in result:
                    print(f"🏪 商店名称: {result['mall_name']}")
                if 'data' in result and 'goods_list' in result['data']:
                    print(f"📦 商品数量: {len(result['data']['goods_list'])}")
        else:
            print("❌ API调用失败")
            print(f"📄 错误信息: {str(response_data)[:200]}...")
        
        print("="*60)
        
        return success
        
    except Exception as e:
        logging.error(f"程序执行过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 修复版Temu API调用测试成功！")
        else:
            print("\n⚠️ 修复版Temu API调用测试失败")
    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
