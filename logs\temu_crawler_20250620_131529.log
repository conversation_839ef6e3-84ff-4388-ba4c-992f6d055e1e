2025-06-20 13:15:29,787 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250620_131529.log
2025-06-20 13:15:29,787 [INFO] ==================================================
2025-06-20 13:15:29,788 [INFO] 开始运行Temu数据抓取
2025-06-20 13:15:31,282 [INFO] 时间: 2025-06-20 13:15:31
2025-06-20 13:15:31,283 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250620_131529.log
2025-06-20 13:15:31,283 [INFO] ==================================================
2025-06-20 13:15:31,283 [INFO] 
检查Cookie状态...
2025-06-20 13:15:31,283 [WARNING] ⚠️ Cookie可能已过期，需要手动刷新...
2025-06-20 13:15:35,348 [INFO] Cookie检查/更新完成
2025-06-20 13:15:35,348 [INFO] 
获取商店ID列表...
2025-06-20 13:15:35,348 [INFO] 请求商店ID列表: http://172.25.165.28:8055/items/shop_data
2025-06-20 13:15:35,403 [INFO] 获取商店ID状态码: 200
2025-06-20 13:15:35,405 [INFO] 获取到的商店数据: {
  "data": [
    {
      "mall_id": "634418212233370"
    },
    {
      "mall_id": "634418221321199"
    },
    {
      "mall_id": "634418221704901"
    },
    {
      "mall_id": "634418213167233"
    }
  ]
}...
2025-06-20 13:15:35,405 [INFO] 获取到的全部mall_id数量: 4
2025-06-20 13:15:35,405 [INFO] 去重后的mall_id数量: 4
2025-06-20 13:15:35,405 [INFO] 去除了 0 个重复的mall_id
2025-06-20 13:15:35,405 [INFO] 最终使用的商店ID列表: ['634418221321199', '634418213167233', '634418212233370', '634418221704901']
2025-06-20 13:15:35,405 [INFO] 
开始处理第 1 批商店，共 4 个
2025-06-20 13:15:35,405 [INFO] 
==================================================
2025-06-20 13:15:35,405 [INFO] 正在处理商店 1/4: mall_id 634418221321199
2025-06-20 13:15:35,405 [INFO] ==================================================
2025-06-20 13:15:35,405 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418221321199
2025-06-20 13:15:35,405 [INFO] 随机延迟 16.74 秒
2025-06-20 13:15:52,150 [INFO] 🔧 从手动cookie文件加载: C:\Users\<USER>\Documents\个人文档\dev\temupc\manual_cookies.json
2025-06-20 13:15:52,162 [INFO] ✅ 成功从文件加载cookie，包含 9 个条目
2025-06-20 13:15:52,162 [INFO] 生成增强Temu请求头: 14 个字段
2025-06-20 13:15:52,731 [INFO] Temu API状态码: 429
2025-06-20 13:15:52,732 [ERROR] 获取mall_id为634418221321199的Temu数据时出错: 429
2025-06-20 13:15:52,732 [ERROR] 返回内容: {"error_code":406008,"error_msg":""}...
2025-06-20 13:15:52,732 [WARNING] 遇到429错误（请求频率过高），第1次重试，等待60秒...
