/**
 * Cookie转换工具
 * 将cookie.json文件转换为base64格式，用于Cloudflare Workers环境变量
 */

const fs = require('fs');
const path = require('path');

function convertCookieToBase64() {
  try {
    // 读取cookie.json文件
    const cookieFilePath = path.join(__dirname, 'cookie.json');
    
    if (!fs.existsSync(cookieFilePath)) {
      console.error('❌ 错误: 未找到cookie.json文件');
      console.log('请确保cookie.json文件在当前目录中');
      return;
    }
    
    const cookieContent = fs.readFileSync(cookieFilePath, 'utf8');
    
    // 验证JSON格式
    try {
      const cookieData = JSON.parse(cookieContent);
      console.log(`✅ 成功读取cookie文件，包含 ${cookieData.length} 个cookie条目`);
    } catch (parseError) {
      console.error('❌ 错误: cookie.json文件格式不正确');
      console.error(parseError.message);
      return;
    }
    
    // 转换为base64
    const base64Cookie = Buffer.from(cookieContent, 'utf8').toString('base64');
    
    console.log('\n' + '='.repeat(60));
    console.log('🔄 Cookie转换完成！');
    console.log('='.repeat(60));
    console.log('\n📋 请复制以下base64字符串：');
    console.log('\n' + '-'.repeat(60));
    console.log(base64Cookie);
    console.log('-'.repeat(60));
    
    console.log('\n📝 使用方法：');
    console.log('1. 复制上面的base64字符串');
    console.log('2. 运行命令: wrangler secret put TEMU_COOKIES');
    console.log('3. 粘贴base64字符串并按回车');
    
    // 可选：保存到文件
    const outputFile = path.join(__dirname, 'cookie-base64.txt');
    fs.writeFileSync(outputFile, base64Cookie);
    console.log(`\n💾 Base64字符串已保存到: ${outputFile}`);
    
    // 验证转换
    console.log('\n🔍 验证转换结果：');
    try {
      const decoded = Buffer.from(base64Cookie, 'base64').toString('utf8');
      const decodedData = JSON.parse(decoded);
      console.log(`✅ 验证成功，解码后包含 ${decodedData.length} 个cookie条目`);
      
      // 显示重要的cookie信息
      const importantCookies = ['verifyAuthToken', 'AccessToken', 'user_uin'];
      console.log('\n🔑 重要Cookie检查：');
      importantCookies.forEach(cookieName => {
        const cookie = decodedData.find(c => c.name === cookieName);
        if (cookie) {
          console.log(`  ✅ ${cookieName}: ${cookie.value.substring(0, 20)}...`);
        } else {
          console.log(`  ⚠️  ${cookieName}: 未找到`);
        }
      });
      
    } catch (verifyError) {
      console.error('❌ 验证失败:', verifyError.message);
    }
    
  } catch (error) {
    console.error('❌ 转换过程中发生错误:', error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  console.log('🚀 开始转换cookie.json到base64格式...\n');
  convertCookieToBase64();
}

module.exports = { convertCookieToBase64 };
