#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版本的浏览器自动化程序
解决ChromeDriver版本问题
"""

import json
import time
import logging
import random
import os
from datetime import datetime
import shutil

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"temu_crawler_browser_fixed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logging.info(f"修复版浏览器自动化日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def clear_webdriver_cache():
    """清除webdriver缓存"""
    try:
        cache_dir = os.path.expanduser("~/.wdm")
        if os.path.exists(cache_dir):
            shutil.rmtree(cache_dir)
            logging.info("✅ 已清除ChromeDriver缓存")
            return True
    except Exception as e:
        logging.warning(f"清除缓存时出错: {e}")
    return False

def setup_chrome_driver_fixed():
    """修复版Chrome浏览器驱动设置"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager

        logging.info("🔧 开始设置Chrome浏览器驱动...")

        # 清除旧的缓存
        clear_webdriver_cache()

        chrome_options = Options()

        # 基本设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')

        # 反检测设置
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 设置用户代理
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36')

        # 禁用图片和CSS加载以提高速度
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2
        }
        chrome_options.add_experimental_option("prefs", prefs)

        # 首先尝试使用本地ChromeDriver
        local_chromedriver = os.path.join(os.path.dirname(os.path.abspath(__file__)), "chromedriver-win64", "chromedriver.exe")

        if os.path.exists(local_chromedriver):
            logging.info(f"✅ 使用本地ChromeDriver: {local_chromedriver}")
            service = Service(local_chromedriver)
        else:
            logging.info("📥 本地ChromeDriver不存在，使用webdriver-manager下载...")
            try:
                driver_path = ChromeDriverManager().install()
                logging.info(f"✅ ChromeDriver下载成功: {driver_path}")
                service = Service(driver_path)
            except Exception as e:
                logging.error(f"ChromeDriver下载失败: {e}")
                raise
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 执行反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        logging.info("✅ Chrome浏览器驱动初始化成功")
        return driver
        
    except ImportError as e:
        logging.error(f"❌ 缺少必要的依赖: {e}")
        logging.error("请运行: pip install selenium webdriver-manager")
        return None
    except Exception as e:
        logging.error(f"❌ Chrome浏览器驱动初始化失败: {str(e)}")
        return None

def load_cookies_to_browser(driver):
    """将cookie加载到浏览器中"""
    try:
        cookie_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        
        # 先访问域名以设置cookie
        driver.get("https://www.temu.com")
        time.sleep(2)
        
        if os.path.exists(cookie_file):
            with open(cookie_file, 'r', encoding='utf-8') as f:
                cookies = json.load(f)
            
            for cookie in cookies:
                try:
                    # 只添加必要的cookie字段
                    cookie_dict = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie.get('domain', '.temu.com'),
                        'path': cookie.get('path', '/'),
                        'secure': cookie.get('secure', True)
                    }
                    driver.add_cookie(cookie_dict)
                except Exception as e:
                    logging.warning(f"添加cookie失败: {cookie['name']} - {str(e)}")
            
            logging.info(f"✅ 成功加载 {len(cookies)} 个cookie到浏览器")
            return True
        else:
            logging.warning("⚠️ Cookie文件不存在")
            return False
    except Exception as e:
        logging.error(f"❌ 加载cookie到浏览器失败: {str(e)}")
        return False

def visit_mall_page_with_browser(driver, mall_id):
    """使用浏览器访问商店页面"""
    mall_url = f"https://www.temu.com/mall.html?mall_id={mall_id}"
    
    try:
        logging.info(f"🌐 浏览器访问商店页面: {mall_url}")
        
        # 访问页面
        driver.get(mall_url)
        
        # 等待页面加载
        time.sleep(5)
        
        # 检查页面标题
        page_title = driver.title
        logging.info(f"📄 页面标题: {page_title}")
        
        # 检查页面URL
        current_url = driver.current_url
        logging.info(f"🔗 当前URL: {current_url}")
        
        # 检查是否是挑战页面
        page_source = driver.page_source
        if len(page_source) < 1000:
            logging.warning("⚠️ 页面内容过短，可能是挑战页面")
            logging.info("等待JavaScript挑战完成...")
            time.sleep(10)  # 等待挑战完成
            page_source = driver.page_source
        
        # 模拟用户行为
        logging.info("🎭 模拟用户浏览行为...")
        
        # 随机滚动页面
        for i in range(3):
            scroll_position = random.randint(300, 800) * (i + 1)
            driver.execute_script(f"window.scrollTo(0, {scroll_position});")
            time.sleep(random.uniform(2, 4))
        
        # 等待更多内容加载
        time.sleep(5)
        
        # 获取最终页面内容
        final_page_source = driver.page_source
        logging.info(f"📊 最终页面内容长度: {len(final_page_source)} 字符")
        
        # 保存页面内容
        debug_file = f"browser_page_{mall_id}.html"
        with open(debug_file, 'w', encoding='utf-8') as f:
            f.write(final_page_source)
        logging.info(f"📄 页面内容已保存到: {debug_file}")
        
        # 检查是否包含商店数据
        if "mall_name" in final_page_source or "goods_list" in final_page_source:
            logging.info("✅ 页面包含商店数据")
            return True, final_page_source
        else:
            logging.warning("⚠️ 页面可能仍在加载或被保护")
            return False, final_page_source
        
    except Exception as e:
        logging.error(f"❌ 浏览器访问页面失败: {str(e)}")
        return False, ""

def extract_data_from_browser_page(page_source, mall_id):
    """从浏览器获取的页面中提取数据"""
    logging.info("🔍 从浏览器页面提取数据...")
    
    extracted_data = {
        'mall_id': mall_id,
        'mall_name': None,
        'products': [],
        'success': False
    }
    
    try:
        # 查找JSON数据
        import re
        
        # 查找常见的数据模式
        json_patterns = [
            r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
            r'window\.__APOLLO_STATE__\s*=\s*({.*?});',
            r'window\.__NEXT_DATA__\s*=\s*({.*?});',
            r'window\.pageData\s*=\s*({.*?});',
            r'window\.mallData\s*=\s*({.*?});'
        ]
        
        for i, pattern in enumerate(json_patterns, 1):
            matches = re.finditer(pattern, page_source, re.DOTALL)
            for match in matches:
                try:
                    json_str = match.group(1)
                    data = json.loads(json_str)
                    logging.info(f"✅ 成功提取JSON数据块 {i}")
                    
                    # 递归查找商店信息
                    def find_mall_info(obj, path=""):
                        if isinstance(obj, dict):
                            for key, value in obj.items():
                                if key in ['mall_name', 'storeName', 'name'] and isinstance(value, str):
                                    extracted_data['mall_name'] = value
                                    logging.info(f"找到商店名称: {value}")
                                
                                if isinstance(value, (dict, list)):
                                    find_mall_info(value, f"{path}.{key}")
                        elif isinstance(obj, list):
                            for item in obj:
                                if isinstance(item, (dict, list)):
                                    find_mall_info(item, path)
                    
                    find_mall_info(data)
                    
                    if extracted_data['mall_name']:
                        extracted_data['success'] = True
                        break
                        
                except json.JSONDecodeError:
                    continue
                except Exception as e:
                    logging.warning(f"解析JSON数据时出错: {str(e)}")
                    continue
        
        # 如果没有找到JSON数据，尝试从HTML中提取
        if not extracted_data['success']:
            logging.info("尝试从HTML中提取数据...")
            
            # 使用正则表达式提取商店名称
            name_patterns = [
                r'"mall_name":"([^"]+)"',
                r'"storeName":"([^"]+)"',
                r'<title>([^<]+)</title>',
                r'<h1[^>]*>([^<]+)</h1>'
            ]
            
            for pattern in name_patterns:
                match = re.search(pattern, page_source)
                if match:
                    extracted_data['mall_name'] = match.group(1)
                    extracted_data['success'] = True
                    logging.info(f"从HTML提取商店名称: {extracted_data['mall_name']}")
                    break
        
        return extracted_data
        
    except Exception as e:
        logging.error(f"提取数据时发生异常: {str(e)}")
        return extracted_data

def main():
    """修复版浏览器自动化主函数"""
    logging.info("="*60)
    logging.info("开始运行Temu数据抓取 - 修复版浏览器自动化")
    logging.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"日志文件: {log_file}")
    logging.info("="*60)
    
    driver = None
    try:
        # 设置浏览器
        driver = setup_chrome_driver_fixed()
        if not driver:
            logging.error("❌ 浏览器驱动设置失败")
            return False
        
        # 加载cookie
        if not load_cookies_to_browser(driver):
            logging.warning("⚠️ Cookie加载失败，继续尝试")
        
        # 测试商店
        test_mall_id = "634418212233370"
        logging.info(f"\n🎯 测试商店: {test_mall_id}")
        
        # 访问商店页面
        success, page_source = visit_mall_page_with_browser(driver, test_mall_id)
        
        if success:
            # 提取数据
            extracted_data = extract_data_from_browser_page(page_source, test_mall_id)
            
            if extracted_data['success']:
                logging.info("🎉 浏览器自动化成功！")
                logging.info(f"商店名称: {extracted_data['mall_name']}")
                
                # 保存提取的数据
                output_file = f"browser_extracted_data_{test_mall_id}.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(extracted_data, f, ensure_ascii=False, indent=2)
                logging.info(f"📁 数据已保存到: {output_file}")
                
                return True
            else:
                logging.warning("⚠️ 未能从页面提取到数据")
                return False
        else:
            logging.warning("⚠️ 页面访问失败")
            return False
        
    except Exception as e:
        logging.error(f"程序执行过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if driver:
            try:
                driver.quit()
                logging.info("🔒 浏览器已关闭")
            except:
                pass

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 修复版浏览器自动化测试成功！")
            print("成功绕过Cloudflare保护并提取数据")
        else:
            print("\n⚠️ 修复版浏览器自动化测试失败")
            print("请检查错误日志")
    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
