2025-06-20 10:40:55,037 [INFO] 修复版浏览器自动化日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_browser_fixed_20250620_104055.log
2025-06-20 10:40:55,037 [INFO] ============================================================
2025-06-20 10:40:55,037 [INFO] 开始运行Temu数据抓取 - 修复版浏览器自动化
2025-06-20 10:40:55,037 [INFO] 时间: 2025-06-20 10:40:55
2025-06-20 10:40:55,038 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_browser_fixed_20250620_104055.log
2025-06-20 10:40:55,038 [INFO] ============================================================
2025-06-20 10:40:55,252 [ERROR] ❌ 缺少必要的依赖: cannot import name 'ChromeType' from 'webdriver_manager.core.utils' (C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\webdriver_manager\core\utils.py)
2025-06-20 10:40:55,252 [ERROR] 请运行: pip install selenium webdriver-manager
2025-06-20 10:40:55,252 [ERROR] ❌ 浏览器驱动设置失败
