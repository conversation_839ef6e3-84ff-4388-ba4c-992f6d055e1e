{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "broken_count": 3, "host": "optimizationguide-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 5, "host": "www.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["JAAAAB0AAABodHRwczovL3VwZGF0ZS5nb29nbGVhcGlzLmNvbQAAAA==", false, 0], "broken_count": 1, "host": "update.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "csp.withgoogle.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "www.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "play.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3RlbXUuY29t", false, 0], "broken_count": 1, "host": "content-autofill.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3RlbXUuY29t", false, 0], "broken_count": 10, "broken_until": "**********", "host": "img.kwcdn.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3RlbXUuY29t", false, 0], "broken_count": 5, "host": "static.kwcdn.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3RlbXUuY29t", false, 0], "broken_count": 4, "host": "aimg.kwcdn.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "beacons.gcp.gvt2.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 2, "host": "ogads-pa.clients6.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 2, "host": "play.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwczovL3RlbXUuY29t", false, 0], "broken_count": 7, "broken_until": "1750420962", "host": "www.temu.com", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397456672834091", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false, 0], "server": "https://redirector.gvt1.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397456673505232", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false, 0], "server": "https://dl.google.com", "supports_spdy": true}, {"anonymization": ["OAAAADMAAABjaHJvbWUtZXh0ZW5zaW9uOi8vbmNlbm5mZmtqZGlhbWxwbWNiYWprbWFpaWlkZGdpb28A", false, 0], "server": "https://api-shoulei-ssl.xunlei.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397459931508593", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL2dvb2dsZXVzZXJjb250ZW50LmNvbQAAAA==", false, 0], "server": "https://clients2.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397456677312986", "port": 443, "protocol_str": "quic"}], "anonymization": ["IAAAABkAAABodHRwczovL2t1YWppbmdtYWlodW8uY29tAAAA", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397459960831243", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "server": "https://passwordsleakcheck-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["IAAAABkAAABodHRwczovL2t1YWppbmdtYWlodW8uY29tAAAA", false, 0], "server": "https://kjpfs-cn.kuajingmaihuo.com", "supports_spdy": true}, {"anonymization": ["IAAAABkAAABodHRwczovL2t1YWppbmdtYWlodW8uY29tAAAA", false, 0], "server": "https://bstatic.cdnfe.com", "supports_spdy": true}, {"anonymization": ["IAAAABkAAABodHRwczovL2t1YWppbmdtYWlodW8uY29tAAAA", false, 0], "server": "https://thtk-cn.kuajingmaihuo.com", "supports_spdy": true}, {"anonymization": ["IAAAABkAAABodHRwczovL2t1YWppbmdtYWlodW8uY29tAAAA", false, 0], "server": "https://seller.kuajingmaihuo.com", "supports_spdy": true}, {"anonymization": ["IAAAABkAAABodHRwczovL2t1YWppbmdtYWlodW8uY29tAAAA", false, 0], "server": "https://pftk-cn.kuajingmaihuo.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397459973142913", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397459973697548", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", true, 0], "server": "https://accounts.youtube.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://csp.withgoogle.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://play.google.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3RlbXUuY29t", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["JAAAAB0AAABodHRwczovL3VwZGF0ZS5nb29nbGVhcGlzLmNvbQAAAA==", false, 0], "server": "https://update.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://beacons.gcp.gvt2.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["LAAAACUAAABodHRwczovL2Nocm9tZXdlYnN0b3JlLmdvb2dsZWFwaXMuY29tAAAA", false, 0], "server": "https://chromewebstore.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394951453759531", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3RlbXUuY29t", false, 0], "server": "https://commimg.kwcdn.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3RlbXUuY29t", false, 0], "server": "https://aimg.kwcdn.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3RlbXUuY29t", false, 0], "server": "https://static.kwcdn.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394961762918732", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3RlbXUuY29t", false, 0], "server": "https://img.kwcdn.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3RlbXUuY29t", false, 0], "server": "https://ca.pftk.temu.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL3RlbXUuY29t", false, 0], "server": "https://ca.thtk.temu.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394961764496264", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL3RlbXUuY29t", false, 0], "server": "https://www.temu.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "3G"}}}