#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实用户行为模拟版本的爬虫程序
先访问商店页面，再调用API接口，完全模拟真实用户行为
"""

import requests
import json
from datetime import datetime
import time
import traceback
import pytz
import os
import logging
import random
import subprocess
import re

# 导入配置
from config import (
    TEMU_API_CONFIG,
    BACKEND_API_CONFIG,
    TIMEZONE_CONFIG
)

# 导入主程序的函数
from a import (
    get_hk_time,
    get_headers,
    save_shop_data,
    save_product_data,
    process_shop_data,
    process_products,
    fetch_shop_ids
)

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"temu_crawler_realistic_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logging.info(f"真实用户行为模拟日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def load_cookies_from_file():
    """从cookie.json文件加载cookie信息"""
    try:
        cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        logging.info(f"尝试从 {cookie_file_path} 加载cookie")
        
        if not os.path.exists(cookie_file_path):
            logging.warning(f"警告: cookie文件不存在: {cookie_file_path}")
            return None
            
        with open(cookie_file_path, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
            
        # 将cookie列表转换为字符串格式
        cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_data if cookie.get('name') and cookie.get('value')])
        logging.info(f"成功加载cookie，包含 {len(cookies_data)} 个条目")
        return cookie_str
    except Exception as e:
        logging.error(f"加载cookie文件时出错: {str(e)}")
        traceback.print_exc()
        return None

def realistic_delay(min_delay, max_delay, reason=""):
    """真实用户行为延迟"""
    delay = min_delay + random.random() * (max_delay - min_delay)
    logging.info(f"真实用户延迟 {delay:.2f} 秒 - {reason}")
    time.sleep(delay)
    return delay

def visit_mall_page_and_extract_params(mall_id, session):
    """访问商店页面并提取真实参数"""
    mall_url = f"https://www.temu.com/mall.html?mall_id={mall_id}"
    
    logging.info(f"访问商店页面: {mall_url}")
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "max-age=0",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1"
    }
    
    try:
        response = session.get(mall_url, headers=headers, timeout=30)
        logging.info(f"商店页面访问状态码: {response.status_code}")
        
        if response.status_code == 200:
            logging.info("✅ 成功访问商店页面")
            
            # 模拟用户浏览页面的时间
            browse_time = random.uniform(5, 12)
            logging.info(f"模拟浏览页面 {browse_time:.1f} 秒")
            time.sleep(browse_time)
            
            # 尝试从页面提取一些参数（如果有的话）
            page_content = response.text
            extracted_params = {}
            
            # 尝试提取list_id或其他动态参数
            list_id_match = re.search(r'"list_id":"([^"]+)"', page_content)
            if list_id_match:
                extracted_params['list_id'] = list_id_match.group(1)
                logging.info(f"从页面提取到list_id: {extracted_params['list_id']}")
            
            return True, extracted_params
        else:
            logging.error(f"访问商店页面失败: {response.status_code}")
            return False, {}
            
    except Exception as e:
        logging.error(f"访问商店页面时发生异常: {str(e)}")
        return False, {}

def fetch_temu_shop_data_realistic(mall_id):
    """完全模拟真实用户行为的数据获取"""
    logging.info(f"开始真实用户行为模拟: mall_id={mall_id}")
    
    # 创建session保持cookie和连接
    session = requests.Session()
    
    # 设置cookie
    fresh_cookies = load_cookies_from_file()
    if not fresh_cookies:
        logging.error("无法获取cookie")
        return None
    
    # 将cookie字符串转换为session cookies
    for cookie_pair in fresh_cookies.split('; '):
        if '=' in cookie_pair:
            name, value = cookie_pair.split('=', 1)
            session.cookies.set(name, value, domain='.temu.com')
    
    # 步骤1：访问商店页面并提取参数
    success, extracted_params = visit_mall_page_and_extract_params(mall_id, session)
    if not success:
        logging.error("访问商店页面失败，跳过API请求")
        return None
    
    # 步骤2：模拟用户在页面上的操作延迟
    realistic_delay(3, 8, "模拟用户页面操作")
    
    # 步骤3：调用API接口
    api_url = TEMU_API_CONFIG['BASE_URL'] + TEMU_API_CONFIG['API_PATH']
    
    # 使用提取的参数或生成随机参数
    list_id = extracted_params.get('list_id', f"r{random.randint(1000000000000000, 9999999999999999)}")
    
    payload = {
        "mallId": mall_id,
        "mainGoodsIds": ["1"],
        "source_page_sn": "10013",
        "mall_id": mall_id,
        "main_goods_ids": ["1"],
        "filter_items": "",
        "page_number": 1,
        "page_size": TEMU_API_CONFIG['PAGE_SIZE'],
        "list_id": list_id,
        "scene_code": "mall_rule",
        "page_sn": 10040,
        "page_el_sn": 201265,
        "source": 10018,
        "anti_content": "1"
    }
    
    # 设置真实的API请求头
    api_headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Content-Type": "application/json;charset=UTF-8",
        "Origin": "https://www.temu.com",
        "Referer": f"https://www.temu.com/mall.html?mall_id={mall_id}",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }
    
    logging.info(f"发送API请求: {api_url}")
    
    try:
        response = session.post(api_url, headers=api_headers, json=payload, timeout=60)
        logging.info(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            logging.info("✅ 真实用户行为模拟请求成功！")
            return result
        elif response.status_code == 429:
            logging.warning("⚠️ 仍然遇到429错误")
            logging.info("即使真实用户行为模拟也被限制，建议等待更长时间")
            return None
        else:
            logging.error(f"API请求失败: {response.status_code}")
            logging.error(f"响应内容: {response.text[:500]}...")
            return None
        
    except Exception as e:
        logging.error(f"API请求时发生异常: {str(e)}")
        traceback.print_exc()
        return None

def main():
    """真实用户行为模拟主函数"""
    logging.info("="*60)
    logging.info("开始运行Temu数据抓取 - 真实用户行为完全模拟")
    logging.info(f"时间: {get_hk_time()}")
    logging.info(f"日志文件: {log_file}")
    logging.info("="*60)
    
    logging.info("模拟流程：")
    logging.info("1. 访问商店页面（如真实用户）")
    logging.info("2. 模拟浏览和操作")
    logging.info("3. 提取页面参数")
    logging.info("4. 调用API接口")
    logging.info("5. 处理和保存数据")
    
    # 获取商店ID列表
    logging.info("\n获取商店ID列表...")
    shop_ids = fetch_shop_ids()
    
    if not shop_ids:
        logging.error("无法获取商店ID列表")
        return
    
    # 只处理前3个商店作为测试
    test_shop_ids = shop_ids[:3]
    logging.info(f"真实用户行为测试，处理 {len(test_shop_ids)} 个商店")
    
    successful_shops = 0
    
    for index, mall_id in enumerate(test_shop_ids, 1):
        logging.info(f"\n{'='*50}")
        logging.info(f"正在处理商店 {index}/{len(test_shop_ids)}: mall_id {mall_id}")
        logging.info(f"{'='*50}")
        
        try:
            # 获取数据
            result = fetch_temu_shop_data_realistic(mall_id)
            if not result:
                logging.info(f"跳过mall_id为{mall_id}的商店")
                continue
            
            # 处理并保存商店数据
            shop_data = process_shop_data(result)
            if shop_data and save_shop_data(shop_data):
                successful_shops += 1
                logging.info(f"✅ 商店数据保存成功")
            
            # 处理并保存产品数据
            products = process_products(result, mall_id)
            logging.info(f"找到 {len(products)} 个产品")
            
            saved_products = 0
            for product in products:
                if save_product_data(product):
                    saved_products += 1
            
            logging.info(f"✅ 成功保存 {saved_products}/{len(products)} 个产品")
            
            # 商店间的长延迟
            if index < len(test_shop_ids):
                realistic_delay(60, 120, f"商店间延迟（{index}/{len(test_shop_ids)}）")
            
        except Exception as e:
            logging.error(f"处理商店 {mall_id} 时发生异常: {str(e)}")
            traceback.print_exc()
    
    # 输出汇总
    logging.info(f"\n{'='*60}")
    logging.info("真实用户行为模拟汇总")
    logging.info(f"处理时间: {get_hk_time()}")
    logging.info(f"处理商店总数: {len(test_shop_ids)}")
    logging.info(f"成功保存商店数: {successful_shops}")
    logging.info(f"{'='*60}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logging.error(f"程序执行过程中发生未捕获的异常: {str(e)}")
        traceback.print_exc()
