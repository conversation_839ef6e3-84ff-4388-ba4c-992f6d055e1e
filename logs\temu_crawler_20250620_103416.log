2025-06-20 10:34:16,913 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250620_103416.log
2025-06-20 10:34:16,913 [INFO] 页面提取日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_extraction_20250620_103416.log
2025-06-20 10:34:16,914 [INFO] ============================================================
2025-06-20 10:34:16,914 [INFO] 开始运行Temu数据抓取 - 页面数据提取模式
2025-06-20 10:34:16,945 [INFO] 时间: 2025-06-20 10:34:16
2025-06-20 10:34:16,945 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_extraction_20250620_103416.log
2025-06-20 10:34:16,948 [INFO] ============================================================
2025-06-20 10:34:16,948 [INFO] 📄 页面提取流程：
2025-06-20 10:34:16,948 [INFO] 1. 访问商店页面
2025-06-20 10:34:16,949 [INFO] 2. 提取页面中的JSON数据
2025-06-20 10:34:16,949 [INFO] 3. 解析HTML元素
2025-06-20 10:34:16,949 [INFO] 4. 使用正则表达式提取
2025-06-20 10:34:16,949 [INFO] 5. 保存提取的数据
2025-06-20 10:34:16,949 [INFO] 
🎯 测试商店: 634418212233370
2025-06-20 10:34:16,949 [INFO] 🎯 开始页面数据提取: mall_id=634418212233370
2025-06-20 10:34:16,949 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-06-20 10:34:16,950 [INFO] 成功加载cookie，包含 20 个条目
2025-06-20 10:34:16,950 [INFO] ✅ Cookie已加载到session
2025-06-20 10:34:16,950 [INFO] 🌐 访问商店页面: https://www.temu.com/mall.html?mall_id=634418212233370
2025-06-20 10:34:17,647 [INFO] 页面访问状态码: 200
2025-06-20 10:34:17,647 [INFO] 响应头: {'Date': 'Fri, 20 Jun 2025 02:34:19 GMT', 'Content-Type': 'text/html; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'vary': 'Accept-Encoding', 'Cache-Control': 'no-store', 'x-gateway-request-id': '1750386859461-42af85b87fa863f327feec6b12edfd0d-07', 'alt-svc': 'h3=":443"; ma=86400', 'cip': '************', 'strict-transport-security': 'max-age=31536000', 'cf-cache-status': 'DYNAMIC', 'Set-Cookie': '__cf_bm=Spu4TgP6wI9CBUBmeBXB4OthMTgovFgmhusYUDy96ww-1750386859-*******-DZdlV7p8kqBslJGEUiAfyjXrWyCrG6uBtbSIFmHncpkHtZWIf0.H81aDvy6Vuwjox._ZyBDIAJKiPhiSYrPab2zKq58sbK3_tFdbSJdRlss; path=/; expires=Fri, 20-Jun-25 03:04:19 GMT; domain=.temu.com; HttpOnly; Secure; SameSite=None', 'Server': 'cloudflare', 'CF-RAY': '9527ddce4f46c85d-HKG', 'Content-Encoding': 'br'}
2025-06-20 10:34:17,647 [INFO] ✅ 成功访问商店页面
2025-06-20 10:34:17,647 [INFO] 响应内容长度: 1378 字节
2025-06-20 10:34:17,648 [INFO] 响应文本长度: 1306 字符
2025-06-20 10:34:17,648 [INFO] 响应编码: utf-8
2025-06-20 10:34:17,649 [INFO] 📄 页面内容已保存到: debug_page_634418212233370.html
2025-06-20 10:34:17,649 [INFO] 🔍 开始从页面提取数据...
2025-06-20 10:34:17,649 [INFO] 方法1：查找页面中的JSON数据...
2025-06-20 10:34:17,650 [INFO] 方法2：使用BeautifulSoup解析HTML...
2025-06-20 10:34:17,651 [INFO] 方法3：使用正则表达式直接提取...
2025-06-20 10:34:17,651 [INFO] 📊 数据提取完成:
2025-06-20 10:34:17,651 [INFO]   - 商店名称: 未找到
2025-06-20 10:34:17,651 [INFO]   - 产品数量: 0
2025-06-20 10:34:17,651 [INFO]   - 原始数据块: 0
2025-06-20 10:34:17,651 [WARNING] ⚠️ 未能从页面提取到有效数据
2025-06-20 10:34:17,653 [WARNING] ⚠️ 页面数据提取失败
