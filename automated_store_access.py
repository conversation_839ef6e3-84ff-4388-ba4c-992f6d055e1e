#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于用户行为记录的自动化店铺访问程序
"""

import json
import time
import logging
import random
import os
from datetime import datetime

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"automated_store_access_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"自动化店铺访问日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def setup_automated_browser():
    """设置自动化浏览器"""
    try:
        import undetected_chromedriver as uc
        
        logging.info("🔧 开始设置自动化浏览器...")
        
        # 创建用户数据目录
        user_data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "chrome_user_data")
        if not os.path.exists(user_data_dir):
            os.makedirs(user_data_dir)
        
        # 配置Chrome选项
        options = uc.ChromeOptions()
        
        # 设置用户数据目录以保持登录状态
        options.add_argument(f'--user-data-dir={user_data_dir}')
        options.add_argument('--profile-directory=Default')
        
        # 基本设置
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1200,800')
        
        # 反检测设置
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--no-first-run')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-infobars')
        
        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
        
        # 创建反检测Chrome驱动
        driver = uc.Chrome(options=options, version_main=137)
        
        # 执行反检测脚本
        driver.execute_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            
            window.chrome = {
                runtime: {},
            };
        """)
        
        logging.info("✅ 自动化浏览器初始化成功")
        return driver
        
    except ImportError:
        logging.error("❌ undetected-chromedriver未安装")
        return None
    except Exception as e:
        logging.error(f"❌ 自动化浏览器初始化失败: {str(e)}")
        return None

def automated_seller_center_access(driver):
    """基于行为记录的自动化卖家中心访问"""
    try:
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        logging.info("🏪 开始自动化卖家中心访问流程...")
        
        # 步骤1: 访问卖家后台
        logging.info("📍 步骤1: 访问卖家后台")
        driver.get("https://seller.kuajingmaihuo.com/")
        time.sleep(5)
        
        current_url = driver.current_url
        if "login" in current_url:
            logging.warning("⚠️ 需要登录，请先完成登录")
            print("\n需要登录，请在浏览器中完成登录后按回车继续...")
            input("⏳ 登录完成后按回车...")
            
            # 重新访问主页
            driver.get("https://seller.kuajingmaihuo.com/")
            time.sleep(3)
        
        logging.info(f"✅ 当前页面: {driver.title}")
        
        # 步骤2: 查找并点击HealthMuse菜单
        logging.info("📍 步骤2: 查找并点击HealthMuse菜单")
        
        healthmuse_selectors = [
            "//span[contains(text(), 'HealthMuse')]",
            "//div[contains(text(), 'HealthMuse')]",
            "//a[contains(text(), 'HealthMuse')]",
            "//*[contains(text(), 'HealthMuse')]"
        ]
        
        healthmuse_element = None
        for selector in healthmuse_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    healthmuse_element = elements[0]
                    logging.info(f"✅ 找到HealthMuse元素: {selector}")
                    break
            except:
                continue
        
        if not healthmuse_element:
            logging.error("❌ 未找到HealthMuse菜单")
            return False, None
        
        # 点击HealthMuse菜单
        logging.info("🖱️ 点击HealthMuse菜单...")
        driver.execute_script("arguments[0].click();", healthmuse_element)
        time.sleep(3)
        
        # 步骤3: 查找并点击"访问我的店铺"
        logging.info("📍 步骤3: 查找并点击'访问我的店铺'")
        
        store_visit_selectors = [
            "//span[contains(text(), '访问我的店铺')]",
            "//div[contains(text(), '访问我的店铺')]",
            "//a[contains(text(), '访问我的店铺')]",
            "//span[contains(text(), '店铺')]",
            "//*[contains(text(), '访问') and contains(text(), '店铺')]"
        ]
        
        store_element = None
        for selector in store_visit_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    store_element = elements[0]
                    logging.info(f"✅ 找到店铺访问元素: {selector}")
                    break
            except:
                continue
        
        if not store_element:
            logging.error("❌ 未找到'访问我的店铺'选项")
            return False, None
        
        # 记录点击前的窗口数量
        original_windows = driver.window_handles
        original_window = driver.current_window_handle
        logging.info(f"📊 点击前窗口数量: {len(original_windows)}")
        
        # 点击"访问我的店铺"
        logging.info("🖱️ 点击'访问我的店铺'...")
        driver.execute_script("arguments[0].click();", store_element)
        time.sleep(5)
        
        # 步骤4: 检查新窗口并切换
        logging.info("📍 步骤4: 检查新窗口并切换")

        # 等待更长时间并检查新窗口
        max_wait_time = 20
        for i in range(max_wait_time):
            time.sleep(1)
            current_windows = driver.window_handles
            if len(current_windows) > len(original_windows):
                logging.info(f"📊 检测到新窗口！窗口数量: {len(current_windows)}")
                break
            if i % 5 == 0:
                logging.info(f"⏳ 等待新窗口... ({i+1}/{max_wait_time}秒)")

        new_windows = driver.window_handles
        logging.info(f"📊 最终窗口数量: {len(new_windows)}")

        if len(new_windows) > len(original_windows):
            # 切换到新窗口
            for window in new_windows:
                if window != original_window:
                    driver.switch_to.window(window)
                    logging.info("🔄 已切换到新窗口")
                    break

            # 等待页面加载
            time.sleep(8)
        else:
            logging.warning("⚠️ 未检测到新窗口，检查当前页面是否有变化")
            time.sleep(5)

            # 检查当前页面是否已经跳转
            current_url = driver.current_url
            if "temu.com" in current_url:
                logging.info("✅ 当前页面已跳转到Temu")
            else:
                logging.warning("⚠️ 当前页面未跳转，尝试手动导航")

                # 尝试手动导航到店铺
                own_mall_id = "634418217337839"
                own_store_url = f"https://www.temu.com/mall.html?mall_id={own_mall_id}"
                logging.info(f"🔄 手动导航到店铺: {own_store_url}")
                driver.get(own_store_url)
                time.sleep(8)
        
        # 检查是否成功到达店铺页面
        current_url = driver.current_url
        page_title = driver.title
        
        logging.info(f"📄 新窗口页面标题: {page_title}")
        logging.info(f"🔗 新窗口URL: {current_url}")
        
        if "temu.com" in current_url and "mall" in current_url:
            logging.info("🎉 成功通过卖家中心访问店铺！")
            return True, current_url
        else:
            logging.warning("⚠️ 未能成功访问店铺")
            return False, current_url
        
    except Exception as e:
        logging.error(f"❌ 自动化卖家中心访问失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def access_target_store_after_warmup(driver, target_mall_id):
    """在热身后访问目标店铺"""
    try:
        logging.info(f"🎯 访问目标店铺: {target_mall_id}")
        
        # 在当前店铺页面模拟浏览行为
        logging.info("🎭 在当前店铺模拟浏览行为...")
        
        # 随机滚动页面
        for i in range(3):
            scroll_position = random.randint(300, 800) * (i + 1)
            driver.execute_script(f"window.scrollTo(0, {scroll_position});")
            time.sleep(random.uniform(2, 4))
        
        # 等待一段时间
        time.sleep(random.uniform(5, 8))
        
        # 访问目标店铺
        target_url = f"https://www.temu.com/mall.html?mall_id={target_mall_id}"
        logging.info(f"🔗 访问目标店铺URL: {target_url}")
        
        driver.get(target_url)
        time.sleep(10)
        
        # 检查访问结果
        current_url = driver.current_url
        page_title = driver.title
        page_source = driver.page_source
        
        logging.info(f"📄 目标店铺页面标题: {page_title}")
        logging.info(f"🔗 目标店铺当前URL: {current_url}")
        logging.info(f"📊 目标店铺页面内容长度: {len(page_source)} 字符")
        
        # 判断是否成功
        if "no_access" not in current_url and "没有互联网连接" not in page_title:
            logging.info("🎉 目标店铺访问成功！")
            
            # 保存成功页面
            success_file = f"automated_success_{target_mall_id}_{datetime.now().strftime('%H%M%S')}.html"
            with open(success_file, 'w', encoding='utf-8') as f:
                f.write(page_source)
            logging.info(f"📁 成功页面已保存: {success_file}")
            
            return True, page_source, success_file
        else:
            logging.warning("❌ 目标店铺被重定向")
            
            # 保存失败页面
            fail_file = f"automated_fail_{target_mall_id}_{datetime.now().strftime('%H%M%S')}.html"
            with open(fail_file, 'w', encoding='utf-8') as f:
                f.write(page_source)
            logging.info(f"📁 失败页面已保存: {fail_file}")
            
            return False, page_source, fail_file
        
    except Exception as e:
        logging.error(f"❌ 访问目标店铺失败: {str(e)}")
        return False, None, None

def main():
    """自动化店铺访问主函数"""
    logging.info("="*60)
    logging.info("开始运行基于行为记录的自动化店铺访问")
    logging.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info("="*60)
    
    driver = None
    try:
        # 设置浏览器
        driver = setup_automated_browser()
        if not driver:
            logging.error("❌ 浏览器设置失败")
            return False
        
        # 执行自动化卖家中心访问
        success, own_store_url = automated_seller_center_access(driver)
        if not success:
            logging.error("❌ 自动化卖家中心访问失败")
            return False
        
        logging.info(f"✅ 成功访问自己的店铺: {own_store_url}")
        
        # 访问目标店铺
        target_mall_id = "634418212233370"
        success, page_source, file_path = access_target_store_after_warmup(driver, target_mall_id)
        
        if success:
            print("\n🎉 自动化店铺访问成功！")
            print(f"✅ 成功访问目标店铺: {target_mall_id}")
            print(f"📁 页面已保存: {file_path}")
            print(f"📊 页面内容长度: {len(page_source)} 字符")
            return True
        else:
            print("\n⚠️ 自动化店铺访问失败")
            print("目标店铺仍然被阻止访问")
            return False
        
    except Exception as e:
        logging.error(f"程序执行过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if driver:
            try:
                input("\n⏳ 按回车键关闭浏览器...")
                driver.quit()
                logging.info("🔒 浏览器已关闭")
            except:
                pass

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 自动化店铺访问测试成功！")
        else:
            print("\n⚠️ 自动化店铺访问测试失败")
    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
