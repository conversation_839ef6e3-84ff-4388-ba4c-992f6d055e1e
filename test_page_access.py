#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的页面访问测试
检查页面是否能正常访问并显示内容
"""

import requests
import json
import os

def load_cookies():
    """加载cookie"""
    try:
        cookie_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        with open(cookie_file, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
        
        cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_data if cookie.get('name') and cookie.get('value')])
        print(f"✅ 成功加载 {len(cookies_data)} 个cookie")
        return cookie_str
    except Exception as e:
        print(f"❌ 加载cookie失败: {e}")
        return None

def test_page_access():
    """测试页面访问"""
    mall_id = "634418212233370"
    url = f"https://www.temu.com/mall.html?mall_id={mall_id}"
    
    print(f"🌐 测试访问: {url}")
    
    # 创建session
    session = requests.Session()
    
    # 设置基本headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }
    
    # 加载cookie
    cookies = load_cookies()
    if cookies:
        headers['Cookie'] = cookies
    
    try:
        # 发送请求
        response = session.get(url, headers=headers, timeout=30, allow_redirects=True)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📊 最终URL: {response.url}")
        print(f"📊 响应头: {dict(response.headers)}")
        print(f"📊 内容长度: {len(response.content)} 字节")
        print(f"📊 文本长度: {len(response.text)} 字符")
        print(f"📊 编码: {response.encoding}")
        
        # 检查是否是HTML
        content_type = response.headers.get('content-type', '')
        print(f"📊 内容类型: {content_type}")
        
        # 保存原始内容
        with open('test_page_raw.html', 'wb') as f:
            f.write(response.content)
        print("📄 原始内容已保存到: test_page_raw.html")
        
        # 尝试保存文本内容
        try:
            with open('test_page_text.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("📄 文本内容已保存到: test_page_text.html")
        except Exception as e:
            print(f"⚠️ 保存文本内容失败: {e}")
        
        # 显示前200个字符
        print(f"\n📄 页面内容预览（前200字符）:")
        print(repr(response.text[:200]))
        
        # 检查是否包含常见的HTML标签
        html_indicators = ['<html', '<head', '<body', '<title', '<div', '<script']
        found_html = any(indicator in response.text.lower() for indicator in html_indicators)
        print(f"\n🔍 是否包含HTML标签: {found_html}")
        
        # 检查是否是重定向页面
        redirect_indicators = ['redirect', 'location.href', 'window.location']
        found_redirect = any(indicator in response.text.lower() for indicator in redirect_indicators)
        print(f"🔍 是否包含重定向: {found_redirect}")
        
        # 检查是否需要登录
        login_indicators = ['login', 'sign in', 'password']
        found_login = any(indicator in response.text.lower() for indicator in login_indicators)
        print(f"🔍 是否需要登录: {found_login}")
        
        return response.status_code == 200 and found_html
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    print("="*60)
    print("Temu页面访问测试")
    print("="*60)
    
    success = test_page_access()
    
    if success:
        print("\n✅ 页面访问成功！")
    else:
        print("\n❌ 页面访问失败！")
        print("可能的原因：")
        print("1. Cookie已过期")
        print("2. IP被限制")
        print("3. 需要JavaScript渲染")
        print("4. 页面结构发生变化")

if __name__ == "__main__":
    main()
