2025-06-20 12:04:03,791 [INFO] API调用日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\api_with_browser_cookies_20250620_120403.log
2025-06-20 12:04:03,792 [INFO] ============================================================
2025-06-20 12:04:03,792 [INFO] 开始使用浏览器cookie调用API接口
2025-06-20 12:04:03,792 [INFO] 时间: 2025-06-20 12:04:03
2025-06-20 12:04:03,792 [INFO] ============================================================
2025-06-20 12:04:04,024 [INFO] 🔧 启动浏览器获取cookie...
2025-06-20 12:04:09,358 [INFO] patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-06-20 12:04:10,146 [INFO] setting properties for headless
2025-06-20 12:04:15,415 [INFO] 📥 获取到 16 个cookie
2025-06-20 12:04:15,419 [INFO] ✅ 成功获取浏览器cookie和User-Agent
2025-06-20 12:04:15,419 [INFO] 🍪 获取到 16 个cookie
2025-06-20 12:04:15,419 [INFO] 🌐 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/137....
2025-06-20 12:04:15,420 [INFO] ✅ API会话创建成功
2025-06-20 12:04:15,420 [INFO] 🎯 使用浏览器cookie测试商店API: 634418212233370
2025-06-20 12:04:15,420 [INFO] 📡 测试API端点 1/6: https://www.temu.com/api/poppy/v1/mall/info?mall_id=634418212233370
2025-06-20 12:04:16,031 [INFO] 📊 响应状态码: 403
2025-06-20 12:04:16,031 [INFO] 📊 响应头: {'Date': 'Fri, 20 Jun 2025 04:04:17 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'vary': 'Accept-Encoding, Origin', 'x-gateway-request-id': '1750392257867-23ab3a103558bc98e4359e2af26c9d52-25', 'access-control-allow-origin': 'https://www.temu.com', 'access-control-allow-headers': 'Origin, X-Requested-With, Content-Type, X_Requested_With, Accept, X-HTTP-Method-Override, Cookie, AccessToken, PASSID, VerifyAuthToken, Anti-Content', 'access-control-allow-methods': 'GET, POST, OPTIONS, DELETE, PUT', 'access-control-allow-credentials': 'true', 'strict-transport-security': 'max-age=31536000', 'content-security-policy-report-only': "default-src 'none';script-src 'report-sample';report-uri /api/sec-csp/110000007/sec-gif", 'yak-timeinfo': '1750392257867|0', 'alt-svc': 'h3=":443"; ma=86400', 'cip': '************', 'cf-cache-status': 'DYNAMIC', 'Server': 'cloudflare', 'CF-RAY': '9528619a5bccc82f-HKG', 'Content-Encoding': 'br'}
2025-06-20 12:04:16,032 [WARNING] ⚠️ API端点 1 被禁止访问 (403)
2025-06-20 12:04:18,033 [INFO] 📡 测试API端点 2/6: https://www.temu.com/api/poppy/v1/mall/goods?mall_id=634418212233370&page=1&size=20
2025-06-20 12:04:18,300 [INFO] 📊 响应状态码: 403
2025-06-20 12:04:18,300 [INFO] 📊 响应头: {'Date': 'Fri, 20 Jun 2025 04:04:20 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'vary': 'Accept-Encoding, Origin', 'x-gateway-request-id': '1750392260145-7bc7edd33c7a30b1fa5b98e52c86d29f-25', 'access-control-allow-origin': 'https://www.temu.com', 'access-control-allow-headers': 'Origin, X-Requested-With, Content-Type, X_Requested_With, Accept, X-HTTP-Method-Override, Cookie, AccessToken, PASSID, VerifyAuthToken, Anti-Content', 'access-control-allow-methods': 'GET, POST, OPTIONS, DELETE, PUT', 'access-control-allow-credentials': 'true', 'strict-transport-security': 'max-age=31536000', 'content-security-policy-report-only': "default-src 'none';script-src 'report-sample';report-uri /api/sec-csp/110000007/sec-gif", 'yak-timeinfo': '1750392260145|0', 'alt-svc': 'h3=":443"; ma=86400', 'cip': '************', 'cf-cache-status': 'DYNAMIC', 'Server': 'cloudflare', 'CF-RAY': '952861a91cbcc82f-HKG', 'Content-Encoding': 'br'}
2025-06-20 12:04:18,301 [WARNING] ⚠️ API端点 2 被禁止访问 (403)
2025-06-20 12:04:20,301 [INFO] 📡 测试API端点 3/6: https://www.temu.com/api/poppy/v1/mall/detail?mall_id=634418212233370
2025-06-20 12:04:20,573 [INFO] 📊 响应状态码: 403
2025-06-20 12:04:20,573 [INFO] 📊 响应头: {'Date': 'Fri, 20 Jun 2025 04:04:22 GMT', 'Content-Type': 'application/json', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'vary': 'Accept-Encoding, Origin', 'x-gateway-request-id': '1750392262411-54e04f179a691ff0b90c1c26ba91813e-25', 'access-control-allow-origin': 'https://www.temu.com', 'access-control-allow-headers': 'Origin, X-Requested-With, Content-Type, X_Requested_With, Accept, X-HTTP-Method-Override, Cookie, AccessToken, PASSID, VerifyAuthToken, Anti-Content', 'access-control-allow-methods': 'GET, POST, OPTIONS, DELETE, PUT', 'access-control-allow-credentials': 'true', 'strict-transport-security': 'max-age=31536000', 'content-security-policy-report-only': "default-src 'none';script-src 'report-sample';report-uri /api/sec-csp/110000007/sec-gif", 'yak-timeinfo': '1750392262411|0', 'alt-svc': 'h3=":443"; ma=86400', 'cip': '************', 'cf-cache-status': 'DYNAMIC', 'Server': 'cloudflare', 'CF-RAY': '952861b74debc82f-HKG', 'Content-Encoding': 'br'}
2025-06-20 12:04:20,574 [WARNING] ⚠️ API端点 3 被禁止访问 (403)
2025-06-20 12:04:22,574 [INFO] 📡 测试API端点 4/6: https://www.temu.com/api/poppy/v1/mall/profile?mall_id=634418212233370
2025-06-20 12:04:22,831 [INFO] 📊 响应状态码: 403
2025-06-20 12:04:22,832 [INFO] 📊 响应头: {'Date': 'Fri, 20 Jun 2025 04:04:24 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'vary': 'Accept-Encoding, Origin', 'x-gateway-request-id': '1750392264681-e4ff45dae7408f98a426f129f9a821b9-25', 'access-control-allow-origin': 'https://www.temu.com', 'access-control-allow-headers': 'Origin, X-Requested-With, Content-Type, X_Requested_With, Accept, X-HTTP-Method-Override, Cookie, AccessToken, PASSID, VerifyAuthToken, Anti-Content', 'access-control-allow-methods': 'GET, POST, OPTIONS, DELETE, PUT', 'access-control-allow-credentials': 'true', 'strict-transport-security': 'max-age=31536000', 'content-security-policy-report-only': "default-src 'none';script-src 'report-sample';report-uri /api/sec-csp/110000007/sec-gif", 'yak-timeinfo': '1750392264681|0', 'alt-svc': 'h3=":443"; ma=86400', 'cip': '************', 'cf-cache-status': 'DYNAMIC', 'Server': 'cloudflare', 'CF-RAY': '952861c57a0cc82f-HKG', 'Content-Encoding': 'br'}
2025-06-20 12:04:22,832 [WARNING] ⚠️ API端点 4 被禁止访问 (403)
2025-06-20 12:04:24,832 [INFO] 📡 测试API端点 5/6: https://www.temu.com/api/oak/v1/mall/info?mall_id=634418212233370
2025-06-20 12:04:25,103 [INFO] 📊 响应状态码: 403
2025-06-20 12:04:25,103 [INFO] 📊 响应头: {'Date': 'Fri, 20 Jun 2025 04:04:27 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'vary': 'Accept-Encoding, Origin', 'x-gateway-request-id': '1750392266944-4dfc60b6820e68005f8612f9df5d10d9-25', 'access-control-allow-origin': 'https://www.temu.com', 'access-control-allow-headers': 'Origin, X-Requested-With, Content-Type, X_Requested_With, Accept, X-HTTP-Method-Override, Cookie, AccessToken, PASSID, VerifyAuthToken, Anti-Content', 'access-control-allow-methods': 'GET, POST, OPTIONS, DELETE, PUT', 'access-control-allow-credentials': 'true', 'strict-transport-security': 'max-age=31536000', 'content-security-policy-report-only': "default-src 'none';script-src 'report-sample';report-uri /api/sec-csp/110000007/sec-gif", 'yak-timeinfo': '1750392266944|0', 'alt-svc': 'h3=":443"; ma=86400', 'cip': '************', 'cf-cache-status': 'DYNAMIC', 'Server': 'cloudflare', 'CF-RAY': '952861d39c34c82f-HKG', 'Content-Encoding': 'br'}
2025-06-20 12:04:25,104 [WARNING] ⚠️ API端点 5 被禁止访问 (403)
2025-06-20 12:04:27,105 [INFO] 📡 测试API端点 6/6: https://www.temu.com/api/oak/v1/mall/goods?mall_id=634418212233370&page=1&size=20
2025-06-20 12:04:27,367 [INFO] 📊 响应状态码: 403
2025-06-20 12:04:27,367 [INFO] 📊 响应头: {'Date': 'Fri, 20 Jun 2025 04:04:29 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'vary': 'Accept-Encoding, Origin', 'x-gateway-request-id': '1750392269217-4a2577f469ebefe6b9662f9f49e33da8-25', 'access-control-allow-origin': 'https://www.temu.com', 'access-control-allow-headers': 'Origin, X-Requested-With, Content-Type, X_Requested_With, Accept, X-HTTP-Method-Override, Cookie, AccessToken, PASSID, VerifyAuthToken, Anti-Content', 'access-control-allow-methods': 'GET, POST, OPTIONS, DELETE, PUT', 'access-control-allow-credentials': 'true', 'strict-transport-security': 'max-age=31536000', 'content-security-policy-report-only': "default-src 'none';script-src 'report-sample';report-uri /api/sec-csp/110000007/sec-gif", 'yak-timeinfo': '1750392269217|0', 'alt-svc': 'h3=":443"; ma=86400', 'cip': '************', 'cf-cache-status': 'DYNAMIC', 'Server': 'cloudflare', 'CF-RAY': '952861e1cdf7c82f-HKG', 'Content-Encoding': 'br'}
2025-06-20 12:04:27,367 [WARNING] ⚠️ API端点 6 被禁止访问 (403)
2025-06-20 12:04:29,368 [INFO] 🔍 测试其他相关API...
2025-06-20 12:04:29,368 [INFO] 📡 测试额外API 1/6: https://www.temu.com/api/poppy/v1/search/mall?keyword=634418212233370
2025-06-20 12:04:29,662 [INFO] 📊 额外API状态码: 403
2025-06-20 12:04:30,663 [INFO] 📡 测试额外API 2/6: https://www.temu.com/api/poppy/v1/store/info?store_id=634418212233370
2025-06-20 12:04:30,959 [INFO] 📊 额外API状态码: 403
2025-06-20 12:04:31,961 [INFO] 📡 测试额外API 3/6: https://www.temu.com/api/poppy/v1/merchant/info?merchant_id=634418212233370
2025-06-20 12:04:32,246 [INFO] 📊 额外API状态码: 403
2025-06-20 12:04:33,246 [INFO] 📡 测试额外API 4/6: https://www.temu.com/api/poppy/v1/user/profile
2025-06-20 12:04:33,519 [INFO] 📊 额外API状态码: 403
2025-06-20 12:04:34,519 [INFO] 📡 测试额外API 5/6: https://www.temu.com/api/poppy/v1/cart/list
2025-06-20 12:04:34,785 [INFO] 📊 额外API状态码: 403
2025-06-20 12:04:35,787 [INFO] 📡 测试额外API 6/6: https://www.temu.com/api/poppy/v1/wishlist/list
2025-06-20 12:04:36,478 [INFO] 📊 额外API状态码: 403
2025-06-20 12:04:37,481 [INFO] ensuring close
