#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试版本 - 尝试访问不同的页面
"""

import json
import time
import logging
import random
import os
from datetime import datetime

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"temu_crawler_final_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logging.info(f"最终测试日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def setup_undetected_chrome():
    """设置反检测Chrome浏览器"""
    try:
        import undetected_chromedriver as uc
        
        logging.info("🔧 开始设置反检测Chrome浏览器...")
        
        # 配置Chrome选项
        options = uc.ChromeOptions()
        
        # 基本设置
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        # 反检测增强设置
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--no-first-run')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-infobars')
        
        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
        
        # 创建反检测Chrome驱动
        driver = uc.Chrome(options=options, version_main=137)
        
        # 执行额外的反检测脚本
        driver.execute_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            
            window.chrome = {
                runtime: {},
            };
            
            Object.defineProperty(navigator, 'permissions', {
                get: () => ({
                    query: () => Promise.resolve({ state: 'granted' }),
                }),
            });
        """)
        
        logging.info("✅ 反检测Chrome浏览器初始化成功")
        return driver
        
    except ImportError:
        logging.error("❌ undetected-chromedriver未安装")
        return None
    except Exception as e:
        logging.error(f"❌ 反检测Chrome浏览器初始化失败: {str(e)}")
        return None

def load_cookies_precisely(driver):
    """精确加载cookie到浏览器中"""
    try:
        cookie_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        
        if not os.path.exists(cookie_file):
            logging.error("❌ Cookie文件不存在")
            return False
        
        # 先访问域名以设置cookie上下文
        logging.info("🌐 访问Temu主页以设置cookie上下文...")
        driver.get("https://www.temu.com")
        time.sleep(3)
        
        # 加载cookie文件
        with open(cookie_file, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
        
        logging.info(f"📥 开始精确加载 {len(cookies_data)} 个cookie...")
        
        # 清除现有cookie
        driver.delete_all_cookies()
        
        # 逐个添加cookie
        successful_cookies = 0
        
        for cookie in cookies_data:
            try:
                cookie_dict = {
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie.get('domain', '.temu.com'),
                    'path': cookie.get('path', '/'),
                }
                
                if cookie.get('secure', False):
                    cookie_dict['secure'] = True
                
                if cookie.get('httpOnly', False):
                    cookie_dict['httpOnly'] = True
                
                driver.add_cookie(cookie_dict)
                successful_cookies += 1
                
            except Exception as e:
                logging.warning(f"⚠️ 添加cookie失败: {cookie['name']} - {str(e)}")
        
        logging.info(f"📊 Cookie加载完成: 成功 {successful_cookies}")
        
        # 验证关键cookie
        browser_cookies = driver.get_cookies()
        key_cookies = ['AccessToken', 'user_uin', '_bee', 'api_uid', 'verifyAuthToken']
        found_key_cookies = [c['name'] for c in browser_cookies if c['name'] in key_cookies]
        
        logging.info(f"🔑 找到关键cookie: {found_key_cookies}")
        
        return len(found_key_cookies) >= 3
            
    except Exception as e:
        logging.error(f"❌ 精确加载cookie失败: {str(e)}")
        return False

def test_different_pages(driver):
    """测试访问不同的页面"""
    test_urls = [
        ("Temu首页", "https://www.temu.com"),
        ("搜索页面", "https://www.temu.com/search_result.html?search_key=phone"),
        ("分类页面", "https://www.temu.com/category/electronics-and-gadgets-1732.html"),
        ("商店页面", "https://www.temu.com/mall.html?mall_id=634418212233370"),
    ]
    
    results = []
    
    for page_name, url in test_urls:
        try:
            logging.info(f"\n🌐 测试访问: {page_name}")
            logging.info(f"🔗 URL: {url}")
            
            # 访问页面
            driver.get(url)
            time.sleep(8)
            
            # 检查页面状态
            page_title = driver.title
            current_url = driver.current_url
            page_source = driver.page_source
            
            logging.info(f"📄 页面标题: {page_title}")
            logging.info(f"🔗 当前URL: {current_url}")
            logging.info(f"📊 页面内容长度: {len(page_source)} 字符")
            
            # 判断是否成功
            is_blocked = "no_access" in current_url or "没有互联网连接" in page_title
            
            if not is_blocked:
                logging.info(f"✅ {page_name} 访问成功！")
                
                # 保存成功的页面
                filename = f"success_{page_name.replace(' ', '_')}_{datetime.now().strftime('%H%M%S')}.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(page_source)
                logging.info(f"📁 成功页面已保存: {filename}")
                
                results.append({
                    'page': page_name,
                    'url': url,
                    'success': True,
                    'title': page_title,
                    'final_url': current_url,
                    'content_length': len(page_source)
                })
            else:
                logging.warning(f"❌ {page_name} 被阻止访问")
                results.append({
                    'page': page_name,
                    'url': url,
                    'success': False,
                    'title': page_title,
                    'final_url': current_url,
                    'content_length': len(page_source)
                })
            
            # 等待一下再测试下一个页面
            time.sleep(3)
            
        except Exception as e:
            logging.error(f"❌ 测试 {page_name} 时发生错误: {str(e)}")
            results.append({
                'page': page_name,
                'url': url,
                'success': False,
                'error': str(e)
            })
    
    return results

def main():
    """最终测试主函数"""
    logging.info("="*60)
    logging.info("开始运行Temu数据抓取 - 最终测试版本")
    logging.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"日志文件: {log_file}")
    logging.info("="*60)
    
    driver = None
    try:
        # 设置反检测浏览器
        driver = setup_undetected_chrome()
        if not driver:
            logging.error("❌ 反检测浏览器设置失败")
            return False
        
        # 精确加载cookie
        if not load_cookies_precisely(driver):
            logging.error("❌ 精确cookie加载失败")
            return False
        
        # 测试不同页面
        logging.info("\n🎯 开始测试不同页面的访问情况...")
        results = test_different_pages(driver)
        
        # 输出测试结果
        logging.info("\n📊 测试结果汇总:")
        success_count = 0
        for result in results:
            if result.get('success', False):
                success_count += 1
                logging.info(f"✅ {result['page']}: 成功 ({result['content_length']} 字符)")
            else:
                logging.info(f"❌ {result['page']}: 失败")
        
        logging.info(f"\n🎉 总计: {success_count}/{len(results)} 个页面访问成功")
        
        if success_count > 0:
            logging.info("🎉 找到可以访问的页面！")
            return True
        else:
            logging.warning("⚠️ 所有页面都被阻止访问")
            return False
        
    except Exception as e:
        logging.error(f"程序执行过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if driver:
            try:
                logging.info("⏳ 保持浏览器打开10秒以便观察...")
                time.sleep(10)
                driver.quit()
                logging.info("🔒 浏览器已关闭")
            except:
                pass

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 最终测试成功！")
            print("找到了可以访问的页面")
        else:
            print("\n⚠️ 最终测试失败")
            print("所有页面都被阻止访问")
    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
