# 快速部署指南 - Temu爬虫Cloudflare Workers版本

## 🚀 立即部署步骤

### 第一步：安装Wrangler CLI
在PowerShell中运行：
```powershell
npm install -g wrangler
```

### 第二步：登录Cloudflare
```powershell
wrangler login
```
这会打开浏览器，登录您的Cloudflare账户并授权。

### 第三步：准备Cookie数据
运行Cookie转换工具：
```powershell
node cookie-converter.js
```
这会生成base64格式的Cookie字符串，复制输出的长字符串。

### 第四步：设置环境变量
```powershell
# 设置后端API地址
wrangler secret put BACKEND_API_URL
# 输入: http://172.25.165.28:8055

# 设置API Token
wrangler secret put BACKEND_API_TOKEN  
# 输入: OppexW5M7FRYT3VQHT3EQx8x3Ly6k2ZM

# 设置Cookie数据
wrangler secret put TEMU_COOKIES
# 粘贴第三步生成的base64字符串
```

### 第五步：部署
```powershell
wrangler deploy
```

## ✅ 部署完成后测试

部署成功后，您会得到一个URL，类似：
`https://temu-scraper.your-subdomain.workers.dev`

### 测试步骤：

1. **健康检查**：
```
访问: https://your-worker-url.workers.dev/health
```

2. **测试Cookie**：
```
访问: https://your-worker-url.workers.dev/test-cookie
```

3. **执行爬取**：
```
访问: https://your-worker-url.workers.dev/scrape
```

## 🔧 常见问题

### Q: 如果没有npm怎么办？
A: 先安装Node.js：
1. 访问 https://nodejs.org
2. 下载并安装LTS版本
3. 重启PowerShell后再运行npm命令

### Q: Cookie转换失败怎么办？
A: 确保cookie.json文件在当前目录，且格式正确。

### Q: 部署失败怎么办？
A: 检查：
1. 是否已登录Cloudflare (`wrangler whoami`)
2. 环境变量是否都设置了
3. 网络连接是否正常

### Q: 执行超时怎么办？
A: 这是正常的，因为免费版有执行时间限制。可以：
1. 升级到付费版（$5/月）
2. 分批处理商店
3. 使用定时触发器

## 📱 使用方式

部署成功后，您可以：

1. **浏览器直接访问**：
   - 打开 `https://your-worker-url.workers.dev/scrape`

2. **定时调用**：
   - 设置定时任务调用API
   - 或使用Cloudflare的Cron Triggers

3. **程序调用**：
```javascript
fetch('https://your-worker-url.workers.dev/scrape', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    mallIds: ['6313567470795', '其他商店ID']
  })
})
```

## 🎯 优势

✅ **减少风控**：使用Cloudflare全球IP池
✅ **无需服务器**：完全无服务器架构  
✅ **成本低廉**：免费版每天10万次请求
✅ **全球加速**：利用Cloudflare边缘网络
✅ **自动扩展**：根据需求自动扩容

现在您可以直接按照这个指南部署了！有任何问题随时问我。
