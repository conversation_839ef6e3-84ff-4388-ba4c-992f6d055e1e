#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器行为模拟版本（不需要真实浏览器）
通过requests模拟完整的浏览器访问流程
"""

import requests
import json
from datetime import datetime
import time
import traceback
import pytz
import os
import logging
import random
import re

# 导入配置
from config import (
    TEMU_API_CONFIG,
    BACKEND_API_CONFIG,
    TIMEZONE_CONFIG
)

# 导入主程序的函数
from a import (
    get_hk_time,
    save_shop_data,
    save_product_data,
    process_shop_data,
    process_products,
    fetch_shop_ids
)

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"temu_crawler_simulation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logging.info(f"浏览器模拟日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def load_cookies_from_file():
    """从cookie.json文件加载cookie信息"""
    try:
        cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        logging.info(f"尝试从 {cookie_file_path} 加载cookie")
        
        if not os.path.exists(cookie_file_path):
            logging.warning(f"警告: cookie文件不存在: {cookie_file_path}")
            return None
            
        with open(cookie_file_path, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
            
        # 将cookie列表转换为字符串格式
        cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_data if cookie.get('name') and cookie.get('value')])
        logging.info(f"成功加载cookie，包含 {len(cookies_data)} 个条目")
        return cookie_str
    except Exception as e:
        logging.error(f"加载cookie文件时出错: {str(e)}")
        traceback.print_exc()
        return None

def create_browser_session():
    """创建模拟浏览器的session"""
    session = requests.Session()
    
    # 设置通用的浏览器headers
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1'
    })
    
    # 加载cookie
    cookies = load_cookies_from_file()
    if cookies:
        # 将cookie字符串转换为session cookies
        for cookie_pair in cookies.split('; '):
            if '=' in cookie_pair:
                name, value = cookie_pair.split('=', 1)
                session.cookies.set(name, value, domain='.temu.com')
        logging.info("✅ Cookie已加载到session")
    
    return session

def simulate_page_visit(session, mall_id):
    """模拟访问商店页面"""
    mall_url = f"https://www.temu.com/mall.html?mall_id={mall_id}"
    
    logging.info(f"🌐 模拟访问商店页面: {mall_url}")
    
    try:
        # 第一步：访问商店页面
        response = session.get(mall_url, timeout=30)
        logging.info(f"页面访问状态码: {response.status_code}")
        
        if response.status_code != 200:
            logging.error(f"页面访问失败: {response.status_code}")
            return False, {}
        
        logging.info("✅ 成功访问商店页面")
        
        # 模拟用户浏览时间
        browse_time = random.uniform(3, 8)
        logging.info(f"🎭 模拟浏览页面 {browse_time:.1f} 秒")
        time.sleep(browse_time)
        
        # 尝试从页面提取一些参数
        page_content = response.text
        extracted_params = {}
        
        # 提取可能的动态参数
        patterns = {
            'list_id': r'"list_id":"([^"]+)"',
            'anti_content': r'"anti_content":"([^"]+)"',
            'page_sn': r'"page_sn":(\d+)',
            'page_el_sn': r'"page_el_sn":(\d+)'
        }
        
        for param_name, pattern in patterns.items():
            match = re.search(pattern, page_content)
            if match:
                extracted_params[param_name] = match.group(1)
                logging.info(f"从页面提取到 {param_name}: {extracted_params[param_name]}")
        
        return True, extracted_params
        
    except Exception as e:
        logging.error(f"模拟页面访问时发生异常: {str(e)}")
        return False, {}

def simulate_api_request(session, mall_id, extracted_params):
    """模拟API请求"""
    api_url = TEMU_API_CONFIG['BASE_URL'] + TEMU_API_CONFIG['API_PATH']
    
    # 使用提取的参数或默认值
    list_id = extracted_params.get('list_id', f"r{random.randint(1000000000000000, 9999999999999999)}")
    anti_content = extracted_params.get('anti_content', "1")
    page_sn = int(extracted_params.get('page_sn', 10040))
    page_el_sn = int(extracted_params.get('page_el_sn', 201265))
    
    payload = {
        "mallId": mall_id,
        "mainGoodsIds": ["1"],
        "source_page_sn": "10013",
        "mall_id": mall_id,
        "main_goods_ids": ["1"],
        "filter_items": "",
        "page_number": 1,
        "page_size": TEMU_API_CONFIG['PAGE_SIZE'],
        "list_id": list_id,
        "scene_code": "mall_rule",
        "page_sn": page_sn,
        "page_el_sn": page_el_sn,
        "source": 10018,
        "anti_content": anti_content
    }
    
    # 设置API请求头（模拟从页面发起的AJAX请求）
    api_headers = {
        "Accept": "application/json, text/plain, */*",
        "Content-Type": "application/json;charset=UTF-8",
        "Origin": "https://www.temu.com",
        "Referer": f"https://www.temu.com/mall.html?mall_id={mall_id}",
        "X-Requested-With": "XMLHttpRequest",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin"
    }
    
    # 更新session headers
    session.headers.update(api_headers)
    
    logging.info(f"📡 发送API请求: {api_url}")
    logging.info(f"使用参数: list_id={list_id}, anti_content={anti_content}")
    
    # 模拟用户操作延迟
    operation_delay = random.uniform(2, 5)
    logging.info(f"⏱️ 模拟用户操作延迟 {operation_delay:.1f} 秒")
    time.sleep(operation_delay)
    
    try:
        response = session.post(api_url, json=payload, timeout=60)
        logging.info(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            logging.info("✅ 浏览器模拟API请求成功！")
            return result
        elif response.status_code == 429:
            logging.warning("⚠️ 仍然遇到429错误")
            logging.info("即使完整模拟浏览器行为也被限制")
            return None
        else:
            logging.error(f"API请求失败: {response.status_code}")
            logging.error(f"响应内容: {response.text[:500]}...")
            return None
        
    except Exception as e:
        logging.error(f"API请求时发生异常: {str(e)}")
        traceback.print_exc()
        return None

def fetch_temu_shop_data_simulation(mall_id):
    """完整的浏览器行为模拟"""
    logging.info(f"🎯 开始完整浏览器行为模拟: mall_id={mall_id}")
    
    # 创建模拟浏览器session
    session = create_browser_session()
    
    try:
        # 步骤1：模拟访问商店页面
        success, extracted_params = simulate_page_visit(session, mall_id)
        if not success:
            logging.error("模拟页面访问失败")
            return None
        
        # 步骤2：模拟API请求
        result = simulate_api_request(session, mall_id, extracted_params)
        return result
        
    except Exception as e:
        logging.error(f"浏览器模拟过程中发生异常: {str(e)}")
        traceback.print_exc()
        return None
    finally:
        session.close()

def main():
    """浏览器模拟主函数"""
    logging.info("="*60)
    logging.info("开始运行Temu数据抓取 - 完整浏览器行为模拟")
    logging.info(f"时间: {get_hk_time()}")
    logging.info(f"日志文件: {log_file}")
    logging.info("="*60)
    
    logging.info("🎭 浏览器模拟流程：")
    logging.info("1. 创建模拟浏览器session")
    logging.info("2. 加载cookie到session")
    logging.info("3. 访问商店页面")
    logging.info("4. 提取页面参数")
    logging.info("5. 模拟用户操作延迟")
    logging.info("6. 发送API请求")
    logging.info("7. 处理和保存数据")
    
    # 测试单个商店
    test_mall_id = "634418212233370"
    
    logging.info(f"\n🎯 测试商店: {test_mall_id}")
    
    result = fetch_temu_shop_data_simulation(test_mall_id)
    
    if result:
        logging.info("🎉 浏览器模拟成功获取数据！")
        
        # 处理数据
        shop_data = process_shop_data(result)
        if shop_data:
            if save_shop_data(shop_data):
                logging.info("✅ 商店数据保存成功")
                logging.info(f"商店名称: {shop_data.get('mall_name', 'N/A')}")
        
        products = process_products(result, test_mall_id)
        if products:
            saved_count = 0
            for product in products:
                if save_product_data(product):
                    saved_count += 1
            logging.info(f"✅ 成功保存 {saved_count}/{len(products)} 个产品")
        
        return True
    else:
        logging.warning("⚠️ 浏览器模拟未能获取数据")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 浏览器模拟测试成功！")
            print("数据获取正常，可以集成到主程序中")
        else:
            print("\n⚠️ 浏览器模拟测试失败")
            print("可能仍需要等待IP限制解除")
    except Exception as e:
        logging.error(f"程序执行过程中发生未捕获的异常: {str(e)}")
        traceback.print_exc()
