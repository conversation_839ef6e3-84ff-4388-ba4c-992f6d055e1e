2025-07-29 15:22:38,336 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250729_152238.log
2025-07-29 15:22:38,336 [INFO] ==================================================
2025-07-29 15:22:38,336 [INFO] 开始运行Temu数据抓取
2025-07-29 15:22:40,044 [INFO] 时间: 2025-07-29 15:22:40
2025-07-29 15:22:40,044 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250729_152238.log
2025-07-29 15:22:40,044 [INFO] ==================================================
2025-07-29 15:22:40,044 [INFO] 
检查Cookie状态...
2025-07-29 15:22:40,044 [WARNING] ⚠️ Cookie可能已过期，需要手动更新...
2025-07-29 15:22:40,045 [INFO] 程序将退出，请更新cookie.json后重新运行
2025-07-29 15:22:40,046 [WARNING] Cookie检查/更新过程中出现问题，但继续尝试数据抓取
2025-07-29 15:22:40,046 [INFO] 
获取商店ID列表...
2025-07-29 15:22:40,046 [INFO] 请求商店ID列表: http://*************:8055/items/shop_data
2025-07-29 15:23:01,101 [ERROR] 获取商店ID时发生异常: HTTPConnectionPool(host='*************', port=8055): Max retries exceeded with url: /items/shop_data?fields=mall_id (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000002300F1270E0>, 'Connection to ************* timed out. (connect timeout=None)'))
2025-07-29 15:23:01,167 [INFO] 没有找到商店ID，使用测试ID: ['6313567470795']
2025-07-29 15:23:01,167 [INFO] 
开始处理第 1 批商店，共 1 个
2025-07-29 15:23:01,167 [INFO] 
==================================================
2025-07-29 15:23:01,167 [INFO] 正在处理商店 1/1: mall_id 6313567470795
2025-07-29 15:23:01,167 [INFO] ==================================================
2025-07-29 15:23:01,167 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 6313567470795
2025-07-29 15:23:01,168 [INFO] 随机延迟 118.28 秒
