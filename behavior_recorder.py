#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户行为记录器 - 记录用户的手工操作行为
"""

import json
import time
import logging
import os
from datetime import datetime

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"behavior_recorder_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"用户行为记录器日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def setup_recording_browser():
    """设置用于记录行为的浏览器"""
    try:
        import undetected_chromedriver as uc
        
        logging.info("🔧 开始设置行为记录浏览器...")
        
        # 创建用户数据目录
        user_data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "chrome_user_data")
        if not os.path.exists(user_data_dir):
            os.makedirs(user_data_dir)
        
        # 配置Chrome选项
        options = uc.ChromeOptions()
        
        # 设置用户数据目录以保持登录状态
        options.add_argument(f'--user-data-dir={user_data_dir}')
        options.add_argument('--profile-directory=Default')
        
        # 基本设置
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1200,800')
        
        # 反检测设置
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--no-first-run')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-infobars')
        
        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36')
        
        # 创建反检测Chrome驱动
        driver = uc.Chrome(options=options, version_main=137)
        
        logging.info(f"✅ 行为记录浏览器初始化成功")
        return driver
        
    except ImportError:
        logging.error("❌ undetected-chromedriver未安装")
        return None
    except Exception as e:
        logging.error(f"❌ 行为记录浏览器初始化失败: {str(e)}")
        return None

def record_page_state(driver, step_name, behavior_log):
    """记录页面状态"""
    try:
        current_url = driver.current_url
        page_title = driver.title
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 获取页面源码的关键信息
        page_source = driver.page_source
        page_length = len(page_source)
        
        # 记录页面状态
        page_state = {
            'step': step_name,
            'timestamp': timestamp,
            'url': current_url,
            'title': page_title,
            'page_length': page_length,
            'window_handles': len(driver.window_handles)
        }
        
        behavior_log.append(page_state)
        
        logging.info(f"📊 [{step_name}] 页面状态记录:")
        logging.info(f"   URL: {current_url}")
        logging.info(f"   标题: {page_title}")
        logging.info(f"   页面长度: {page_length} 字符")
        logging.info(f"   窗口数量: {len(driver.window_handles)}")
        
        return page_state
        
    except Exception as e:
        logging.error(f"❌ 记录页面状态失败: {str(e)}")
        return None

def save_behavior_log(behavior_log):
    """保存行为记录"""
    try:
        log_file = f"user_behavior_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(behavior_log, f, ensure_ascii=False, indent=2)
        
        logging.info(f"💾 用户行为记录已保存: {log_file}")
        return log_file
        
    except Exception as e:
        logging.error(f"❌ 保存行为记录失败: {str(e)}")
        return None

def record_user_behavior(driver):
    """记录用户行为的主要流程"""
    behavior_log = []
    
    try:
        print("\n" + "="*60)
        print("📹 用户行为记录器")
        print("="*60)
        print("📋 我将记录您访问店铺的完整操作流程：")
        print("1. 我会在每个关键步骤暂停")
        print("2. 您进行手工操作")
        print("3. 我记录页面状态和变化")
        print("4. 最后生成完整的操作记录")
        print("="*60)
        
        # 步骤1: 访问卖家后台
        print("\n🔸 步骤1: 访问卖家后台")
        driver.get("https://seller.kuajingmaihuo.com/")
        time.sleep(3)
        record_page_state(driver, "访问卖家后台", behavior_log)
        
        input("⏳ 请确认已在卖家后台页面，然后按回车继续...")
        
        # 步骤2: 记录登录前状态
        print("\n🔸 步骤2: 检查登录状态")
        record_page_state(driver, "检查登录状态", behavior_log)
        
        current_url = driver.current_url
        if "login" in current_url:
            print("需要登录，请完成登录操作")
            input("⏳ 请完成登录后按回车继续...")
            record_page_state(driver, "登录完成", behavior_log)
        
        # 步骤3: 在卖家后台主页
        print("\n🔸 步骤3: 卖家后台主页状态")
        record_page_state(driver, "卖家后台主页", behavior_log)
        
        print("📋 现在请您手工操作：")
        print("1. 找到右上角的'HealthMuse'菜单")
        print("2. 点击'HealthMuse'")
        print("3. 完成后按回车，我会记录状态")
        
        input("⏳ 请点击HealthMuse菜单后按回车...")
        
        # 步骤4: 点击HealthMuse后的状态
        print("\n🔸 步骤4: 点击HealthMuse后")
        time.sleep(2)
        record_page_state(driver, "点击HealthMuse后", behavior_log)
        
        print("📋 继续手工操作：")
        print("1. 在弹出的菜单中找到'访问我的店铺'")
        print("2. 点击'访问我的店铺'")
        print("3. 完成后按回车，我会记录状态")
        
        input("⏳ 请点击'访问我的店铺'后按回车...")
        
        # 步骤5: 点击访问店铺后的状态
        print("\n🔸 步骤5: 点击访问店铺后")
        time.sleep(3)
        record_page_state(driver, "点击访问店铺后", behavior_log)
        
        # 检查是否有新窗口
        all_windows = driver.window_handles
        if len(all_windows) > 1:
            print(f"🔄 检测到 {len(all_windows)} 个窗口")
            
            for i, window in enumerate(all_windows):
                driver.switch_to.window(window)
                time.sleep(2)
                print(f"\n🔸 步骤5.{i+1}: 窗口 {i+1} 状态")
                record_page_state(driver, f"窗口{i+1}", behavior_log)
        
        # 步骤6: 等待页面完全加载
        print("\n🔸 步骤6: 等待页面完全加载")
        print("请等待页面完全加载...")
        time.sleep(10)
        record_page_state(driver, "页面完全加载后", behavior_log)
        
        # 步骤7: 最终状态确认
        print("\n🔸 步骤7: 最终状态确认")
        current_url = driver.current_url
        if "temu.com" in current_url:
            print("✅ 成功到达Temu页面！")
            if "mall" in current_url:
                print("✅ 成功到达店铺页面！")
            else:
                print("⚠️ 在Temu但不是店铺页面")
        else:
            print("❌ 未到达Temu页面")
        
        record_page_state(driver, "最终状态", behavior_log)
        
        # 保存行为记录
        log_file = save_behavior_log(behavior_log)
        
        print("\n" + "="*60)
        print("📹 用户行为记录完成！")
        print("="*60)
        print(f"📁 行为记录已保存: {log_file}")
        print(f"📊 总共记录了 {len(behavior_log)} 个步骤")
        print("\n📋 记录摘要:")
        for i, step in enumerate(behavior_log, 1):
            print(f"  {i}. {step['step']}: {step['url']}")
        print("="*60)
        
        return behavior_log, log_file
        
    except Exception as e:
        logging.error(f"❌ 记录用户行为失败: {str(e)}")
        return behavior_log, None

def main():
    """用户行为记录器主函数"""
    logging.info("="*60)
    logging.info("开始运行用户行为记录器")
    logging.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info("="*60)
    
    driver = None
    try:
        # 设置浏览器
        driver = setup_recording_browser()
        if not driver:
            logging.error("❌ 浏览器设置失败")
            return False
        
        # 记录用户行为
        behavior_log, log_file = record_user_behavior(driver)
        
        if log_file:
            print(f"\n🎉 行为记录成功！")
            print(f"📁 记录文件: {log_file}")
            print("现在可以基于这个记录创建自动化程序")
            return True
        else:
            print("\n⚠️ 行为记录失败")
            return False
        
    except Exception as e:
        logging.error(f"程序执行过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if driver:
            try:
                input("\n⏳ 按回车键关闭浏览器...")
                driver.quit()
                logging.info("🔒 浏览器已关闭")
            except:
                pass

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 用户行为记录完成！")
        else:
            print("\n⚠️ 用户行为记录失败")
    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
