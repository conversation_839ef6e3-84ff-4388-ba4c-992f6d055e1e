# Temu商店数据爬虫程序说明

## 1. 程序概述

本程序是一个专门用于抓取Temu平台商店和产品数据的爬虫系统。程序会自动从Directus数据库中获取已有的商店ID列表，然后从Temu API获取每个商店的最新数据，包括店铺信息和产品信息，最后将这些数据更新到Directus数据库中并记录变更日志。

## 2. 程序架构

程序主要由以下几个部分组成：

1. **配置模块**（config.py）：
   - 存储Temu API的配置信息
   - 存储后端API（Directus）的配置信息
   - 管理请求延迟、批处理等参数设置

2. **主程序模块**（a.py）：
   - 数据抓取与处理的核心逻辑
   - 从Directus获取商店ID列表
   - 抓取Temu商店和产品数据
   - 处理和保存数据到Directus数据库
   - 记录数据变更日志

3. **测试模块**（test_shop.py）：
   - 用于测试单个商店ID的数据抓取功能
   - 将抓取结果保存为JSON文件

4. **Cookie管理**：
   - 从cookie.json文件中加载和更新Cookie信息
   - 提供给Temu API请求使用

## 3. 工作流程

程序的完整工作流程如下：

1. **初始化**：
   - 设置日志系统
   - 加载配置信息

2. **更新Cookie**：
   - 访问Temu主页获取最新的Cookie信息
   - 更新cookie.json文件

3. **获取商店ID列表**：
   - 从Directus数据库中获取所有商店ID
   - 对商店ID进行去重处理

4. **批量处理商店**：
   - 将商店ID列表分成多个批次处理
   - 每批处理一定数量的商店后，添加较长延迟

5. **处理单个商店**：
   - 从Temu API获取商店数据
   - 解析和处理商店信息
   - 将商店信息保存到数据库
   - 记录商店数据变更日志

6. **处理商店产品**：
   - 从API响应中提取产品列表
   - 处理每个产品的详细信息
   - 将产品信息保存到数据库
   - 记录产品数据变更日志
   - 每处理一个产品后添加短暂延迟

7. **汇总结果**：
   - 记录处理总量和成功率
   - 输出完整日志

## 4. 反爬虫策略

为了避免被Temu的反爬虫系统检测，程序采用了多种策略：

1. **随机延迟**：
   - API请求间添加随机延迟
   - 产品处理过程中添加短暂延迟
   - 商店之间添加较长延迟
   - 批次处理间添加更长随机延迟

2. **Cookie管理**：
   - 定期更新Cookie
   - 每次请求前重新加载Cookie

3. **批次处理**：
   - 将大量请求分批进行
   - 批次间添加较长休息时间

4. **模拟浏览器行为**：
   - 使用合适的请求头
   - 保持Cookie会话一致性

## 5. 数据存储

所有抓取的数据都保存到Directus数据库中，主要包含以下几张表：

1. **shop_data**：存储商店基本信息
   - mall_id：商店ID
   - mall_name：商店名称
   - mall_logo：商店Logo URL
   - goods_sales_num：商品销售总量
   - goods_num：商品总数
   - review_num：评论总数
   - update_time：数据更新时间

2. **product_data**：存储产品信息
   - goods_id：产品ID
   - title：产品标题
   - image_url：产品图片URL
   - price：价格
   - sales_num：销售数量
   - comment：评论数量
   - update_time：数据更新时间
   - shop_data：关联的商店ID（外键）

3. **shop_log**：商店数据变更日志
   - log_time：日志时间
   - order_increase：订单增长数
   - growth_rate：增长率
   - sales_num：当前销售数量
   - shop_data：关联的商店ID（外键）

4. **product_log**：产品数据变更日志
   - log_time：日志时间
   - order_increase：订单增长数
   - order_growth_rate：订单增长率
   - comment_increase：评论增长数
   - comment_growth_rate：评论增长率
   - sales_num：当前销售数量
   - comment_num：当前评论数量
   - product_data：关联的产品ID（外键）

## 6. 使用说明

### 6.1 环境准备

1. 确保已安装Python 3.6或以上版本
2. 安装所需依赖：
   ```
   pip install requests pytz
   ```
3. 确保Directus数据库已正确设置并可访问

### 6.2 配置文件

编辑`config.py`文件，设置以下参数：

```python
# Temu API 配置
TEMU_API_CONFIG = {
    "BASE_URL": "https://www.temu.com",
    "API_PATH": "/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList",
    "DEFAULT_MALL_ID": "6313567470795",  # 默认测试商店ID
    "PAGE_SIZE": 8,
    "SHOP_DELAY": 3,  # 商店间隔（秒）
    # 其他延迟参数...
}

# 后端 API 配置
BACKEND_API_CONFIG = {
    "BASE_URL": "http://你的Directus服务器地址",
    "API_TOKEN": "你的Directus API令牌",
}
```

### 6.3 Cookie配置

1. 准备`cookie.json`文件，内容为从浏览器获取的Temu Cookie，格式如下：
   ```json
   [
     {
       "domain": ".temu.com",
       "name": "cookie名称",
       "value": "cookie值",
       "path": "/",
       "secure": true
     },
     // 更多cookie...
   ]
   ```

2. 将此文件放在程序根目录下

### 6.4 运行程序

1. 运行主程序：
   ```
   python a.py
   ```

2. 测试单个商店数据抓取：
   ```
   python test_shop.py
   ```
   注意：使用前需要在`test_shop.py`中修改要测试的商店ID

### 6.5 日志查看

程序运行日志保存在`logs`目录下，文件名格式为：
```
temu_crawler_YYYYMMDD_HHMMSS.log
```

## 7. 常见问题

1. **429错误（请求过多）**：
   - 原因：被Temu反爬虫系统检测
   - 解决方法：
     - 增加请求延迟时间
     - 更新Cookie
     - 使用代理IP

2. **Cookie无效**：
   - 原因：Cookie过期或被禁用
   - 解决方法：
     - 从浏览器重新获取Cookie
     - 运行程序前手动访问Temu网站

3. **数据保存失败**：
   - 原因：Directus数据库连接问题
   - 解决方法：
     - 检查API令牌是否有效
     - 确认数据库服务器是否可访问
     - 检查网络连接

## 8. 维护与更新

为保持程序正常运行，建议定期进行以下维护：

1. 更新Cookie：
   - 每1-2天手动更新一次cookie.json
   - 或实现自动Cookie获取机制

2. 调整爬取频率：
   - 根据实际情况调整config.py中的延迟参数
   - 夜间可适当提高爬取频率

3. 检查日志：
   - 定期查看日志文件，监控错误率
   - 根据错误类型调整程序策略

4. 数据库维护：
   - 定期清理过期日志数据
   - 优化数据库查询性能

## 9. 未来改进计划

1. 添加IP代理池支持
2. 实现多线程爬取（注意控制请求频率）
3. 添加邮件或消息通知功能
4. 优化数据处理算法，提高效率
5. 添加Web界面，方便监控和管理

---

*最后更新时间：2025年6月19日* 