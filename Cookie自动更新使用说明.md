# Cookie自动更新系统使用说明

## 概述

本系统为您的爬虫程序 `a.py` 提供了自动的cookie管理功能。当cookie过期时，系统会自动使用您提供的登录接口来更新cookie，确保爬虫程序能够持续正常运行。

## 系统组件

### 1. 主要文件

- **`a.py`** - 主爬虫程序（已集成自动cookie更新功能）
- **`cookie_updater.py`** - 专门的cookie更新程序
- **`cookie.json`** - cookie存储文件
- **`test_cookie_system.py`** - 系统测试脚本

### 2. 新增功能

#### 在 `a.py` 中新增的功能：
- `update_cookies_if_needed()` - 检查并更新cookie
- 增强的 `get_temu_headers()` - 自动处理cookie过期
- 增强的 `fetch_temu_shop_data()` - 自动重试机制

#### `cookie_updater.py` 的功能：
- `check_cookie_validity()` - 检查cookie是否有效
- `update_cookies_via_api()` - 使用您提供的登录接口更新cookie
- `load_cookies_from_file()` / `save_cookies_to_file()` - cookie文件管理

## 工作流程

### 1. 自动检测和更新流程

```
开始运行爬虫
    ↓
检查cookie是否有效
    ↓
如果有效 → 继续爬取数据
    ↓
如果无效 → 调用登录接口更新cookie
    ↓
验证新cookie是否有效
    ↓
继续爬取数据
```

### 2. 登录接口详情

系统使用您提供的完整登录接口：

- **URL**: `https://seller.kuajingmaihuo.com/bg/quiet/api/mms/login`
- **方法**: POST
- **包含所有必要的headers**（anti-content、cookie等）
- **登录凭据**: 用户名和加密密码

## 使用方法

### 1. 正常运行爬虫

```bash
python a.py
```

系统会自动：
- 在开始时检查cookie状态
- 在API请求失败时自动重试
- 在检测到401/403错误时自动更新cookie

### 2. 手动更新cookie

```bash
python cookie_updater.py
```

### 3. 测试系统功能

```bash
python test_cookie_system.py
```

## 日志和监控

### 1. 日志文件位置

- 主程序日志: `logs/temu_crawler_YYYYMMDD_HHMMSS.log`
- Cookie更新日志: `logs/cookie_updater_YYYYMMDD_HHMMSS.log`

### 2. 关键日志信息

- **Cookie检查**: "检查cookie有效性..."
- **Cookie更新**: "开始通过登录接口更新cookie"
- **登录成功**: "登录成功！"
- **Cookie保存**: "成功保存新的cookie"

## 错误处理

### 1. 常见错误和解决方案

| 错误类型 | 可能原因 | 解决方案 |
|---------|---------|---------|
| Cookie加载失败 | cookie.json文件损坏 | 手动运行cookie_updater.py |
| 登录失败 | 登录凭据过期 | 更新cookie_updater.py中的登录信息 |
| API请求401/403 | Cookie过期 | 系统会自动处理 |

### 2. 自动重试机制

- API请求失败时自动重试1次
- Cookie更新失败时记录错误但继续运行
- 支持多种错误状态码的处理

## 配置说明

### 1. 登录信息配置

如需更新登录信息，请修改 `cookie_updater.py` 中的以下部分：

```python
data = {
    "loginName": "您的用户名",
    "encryptPassword": "您的加密密码",
    "keyVersion": "1"
}
```

### 2. Cookie文件格式

cookie.json文件采用标准的浏览器cookie格式：

```json
[
  {
    "domain": ".kuajingmaihuo.com",
    "name": "cookie_name",
    "value": "cookie_value",
    "path": "/",
    "secure": true,
    ...
  }
]
```

## 安全注意事项

1. **保护登录凭据**: cookie_updater.py包含敏感的登录信息
2. **定期检查日志**: 监控cookie更新是否正常
3. **备份cookie文件**: 建议定期备份有效的cookie.json

## 故障排除

### 1. 如果cookie更新失败

```bash
# 1. 检查网络连接
ping seller.kuajingmaihuo.com

# 2. 手动测试cookie更新
python cookie_updater.py

# 3. 查看详细日志
tail -f logs/cookie_updater_*.log
```

### 2. 如果爬虫仍然无法工作

1. 检查cookie.json文件是否存在且格式正确
2. 验证登录凭据是否仍然有效
3. 检查目标网站是否有新的反爬虫措施

## 更新历史

- **v1.0** - 基础cookie自动更新功能
- 集成了您提供的完整登录接口
- 添加了自动重试和错误处理机制
- 包含完整的测试套件

## 技术支持

如遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 运行测试脚本检查系统状态
3. 检查网络连接和登录凭据

---

**注意**: 本系统已经完全集成到您的爬虫程序中，正常情况下无需手动干预即可自动处理cookie过期问题。
