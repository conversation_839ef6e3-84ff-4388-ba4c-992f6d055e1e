#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持久化浏览器访问 - 保持登录状态并按正确流程访问店铺
"""

import json
import time
import logging
import random
import os
from datetime import datetime

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"persistent_browser_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"持久化浏览器日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def setup_persistent_browser():
    """设置持久化浏览器（保持用户数据）"""
    try:
        import undetected_chromedriver as uc
        
        logging.info("🔧 开始设置持久化浏览器...")
        
        # 创建用户数据目录
        user_data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "chrome_user_data")
        if not os.path.exists(user_data_dir):
            os.makedirs(user_data_dir)
        
        # 配置Chrome选项
        options = uc.ChromeOptions()
        
        # 设置用户数据目录以保持登录状态
        options.add_argument(f'--user-data-dir={user_data_dir}')
        options.add_argument('--profile-directory=Default')
        
        # 基本设置
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1200,800')
        
        # 反检测设置
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--no-first-run')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-infobars')
        
        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
        
        # 创建反检测Chrome驱动
        driver = uc.Chrome(options=options, version_main=137)
        
        # 执行反检测脚本
        driver.execute_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            
            window.chrome = {
                runtime: {},
            };
        """)
        
        logging.info(f"✅ 持久化浏览器初始化成功，用户数据目录: {user_data_dir}")
        return driver
        
    except ImportError:
        logging.error("❌ undetected-chromedriver未安装")
        return None
    except Exception as e:
        logging.error(f"❌ 持久化浏览器初始化失败: {str(e)}")
        return None

def check_seller_login_status(driver):
    """检查卖家后台登录状态"""
    try:
        logging.info("🔍 检查卖家后台登录状态...")
        
        # 访问卖家后台
        driver.get("https://seller.kuajingmaihuo.com/")
        time.sleep(5)
        
        current_url = driver.current_url
        page_title = driver.title
        
        logging.info(f"📄 页面标题: {page_title}")
        logging.info(f"🔗 当前URL: {current_url}")
        
        if "login" in current_url.lower():
            logging.warning("⚠️ 需要重新登录")
            return False
        else:
            logging.info("✅ 已登录卖家后台")
            return True
            
    except Exception as e:
        logging.error(f"❌ 检查登录状态失败: {str(e)}")
        return False

def manual_login_if_needed(driver):
    """如果需要的话进行手动登录"""
    try:
        if check_seller_login_status(driver):
            return True
        
        logging.info("🔐 需要手动登录...")
        
        # 访问登录页面
        driver.get("https://seller.kuajingmaihuo.com/login")
        time.sleep(3)
        
        print("\n" + "="*60)
        print("🔐 需要手动登录")
        print("="*60)
        print("📋 请在浏览器中完成登录：")
        print("1. 输入用户名和密码")
        print("2. 完成验证码验证")
        print("3. 点击登录按钮")
        print("4. 登录成功后按回车键继续")
        print("="*60)
        
        input("⏳ 请完成登录后按回车键继续...")
        
        # 验证登录状态
        return check_seller_login_status(driver)
        
    except Exception as e:
        logging.error(f"❌ 手动登录过程失败: {str(e)}")
        return False

def access_own_store_via_seller_center(driver):
    """通过卖家中心正确访问自己的店铺"""
    try:
        logging.info("🏪 通过卖家中心访问自己的店铺...")
        
        # 确保在卖家后台主页
        driver.get("https://seller.kuajingmaihuo.com/")
        time.sleep(5)
        
        # 查找右上角的HealthMuse菜单
        logging.info("🔍 查找右上角的HealthMuse菜单...")
        
        # 尝试多种方式找到HealthMuse菜单
        possible_selectors = [
            "//span[contains(text(), 'HealthMuse')]",
            "//div[contains(text(), 'HealthMuse')]",
            "//a[contains(text(), 'HealthMuse')]",
            "//*[contains(text(), 'HealthMuse')]"
        ]
        
        healthmuse_element = None
        for selector in possible_selectors:
            try:
                from selenium.webdriver.common.by import By
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    healthmuse_element = elements[0]
                    logging.info(f"✅ 找到HealthMuse元素: {selector}")
                    break
            except:
                continue
        
        if healthmuse_element:
            logging.info("🖱️ 点击HealthMuse菜单...")
            driver.execute_script("arguments[0].click();", healthmuse_element)
            time.sleep(3)
            
            # 查找"访问我的店铺"选项
            logging.info("🔍 查找'访问我的店铺'选项...")
            
            store_visit_selectors = [
                "//span[contains(text(), '访问我的店铺')]",
                "//div[contains(text(), '访问我的店铺')]",
                "//a[contains(text(), '访问我的店铺')]",
                "//span[contains(text(), '店铺')]",
                "//*[contains(text(), '店铺')]"
            ]
            
            store_element = None
            for selector in store_visit_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        store_element = elements[0]
                        logging.info(f"✅ 找到店铺访问元素: {selector}")
                        break
                except:
                    continue
            
            if store_element:
                logging.info("🖱️ 点击'访问我的店铺'...")
                driver.execute_script("arguments[0].click();", store_element)
                time.sleep(3)

                # 检查是否打开了新标签页
                original_window = driver.current_window_handle
                all_windows = driver.window_handles

                if len(all_windows) > 1:
                    logging.info("🔄 检测到新标签页，切换到新标签页...")
                    for window in all_windows:
                        if window != original_window:
                            driver.switch_to.window(window)
                            break
                    time.sleep(5)

                # 等待更长时间让页面加载
                time.sleep(10)

                # 检查是否成功跳转到店铺页面
                current_url = driver.current_url
                logging.info(f"🔗 点击后的URL: {current_url}")

                if "temu.com" in current_url and "mall" in current_url:
                    logging.info("✅ 成功通过卖家中心访问店铺！")
                    logging.info(f"🔗 店铺URL: {current_url}")
                    return True, current_url
                elif "temu.com" in current_url:
                    logging.info("✅ 成功跳转到Temu，但不是店铺页面")
                    logging.info(f"🔗 当前URL: {current_url}")

                    # 尝试手动导航到店铺页面
                    own_mall_id = "634418217337839"
                    own_store_url = f"https://www.temu.com/mall.html?mall_id={own_mall_id}"
                    logging.info(f"🔄 手动导航到自己的店铺: {own_store_url}")
                    driver.get(own_store_url)
                    time.sleep(8)

                    final_url = driver.current_url
                    if "no_access" not in final_url:
                        logging.info("✅ 手动导航到店铺成功！")
                        return True, final_url
                    else:
                        logging.warning("⚠️ 手动导航也被阻止")
                        return False, final_url
                else:
                    logging.warning("⚠️ 未能跳转到Temu页面")
                    logging.info(f"🔗 当前URL: {current_url}")
                    return False, current_url
            else:
                logging.warning("⚠️ 未找到'访问我的店铺'选项")
                return False, None
        else:
            logging.warning("⚠️ 未找到HealthMuse菜单")
            
            # 手动指导用户
            print("\n" + "="*60)
            print("🔍 需要手动操作")
            print("="*60)
            print("📋 请在浏览器中手动操作：")
            print("1. 在卖家中心页面右上角找到'HealthMuse'菜单")
            print("2. 点击'HealthMuse'")
            print("3. 选择'访问我的店铺'")
            print("4. 等待跳转到Temu店铺页面")
            print("5. 完成后按回车键继续")
            print("="*60)
            
            input("⏳ 请完成手动操作后按回车键继续...")
            
            current_url = driver.current_url
            if "temu.com" in current_url:
                logging.info("✅ 手动操作成功，已在Temu店铺页面")
                return True, current_url
            else:
                logging.warning("⚠️ 仍未在Temu店铺页面")
                return False, current_url
        
    except Exception as e:
        logging.error(f"❌ 通过卖家中心访问店铺失败: {str(e)}")
        return False, None

def access_target_store(driver, target_mall_id):
    """访问目标店铺"""
    try:
        logging.info(f"🎯 访问目标店铺: {target_mall_id}")
        
        target_url = f"https://www.temu.com/mall.html?mall_id={target_mall_id}"
        logging.info(f"🔗 目标URL: {target_url}")
        
        driver.get(target_url)
        time.sleep(10)
        
        current_url = driver.current_url
        page_title = driver.title
        page_source = driver.page_source
        
        logging.info(f"📄 页面标题: {page_title}")
        logging.info(f"🔗 当前URL: {current_url}")
        logging.info(f"📊 页面内容长度: {len(page_source)} 字符")
        
        if "no_access" not in current_url and "没有互联网连接" not in page_title:
            logging.info("🎉 目标店铺访问成功！")
            
            # 保存成功页面
            success_file = f"persistent_success_{target_mall_id}_{datetime.now().strftime('%H%M%S')}.html"
            with open(success_file, 'w', encoding='utf-8') as f:
                f.write(page_source)
            logging.info(f"📁 成功页面已保存: {success_file}")
            
            return True, page_source, success_file
        else:
            logging.warning("❌ 目标店铺被重定向")
            return False, page_source, None
        
    except Exception as e:
        logging.error(f"❌ 访问目标店铺失败: {str(e)}")
        return False, None, None

def main():
    """持久化浏览器访问主函数"""
    logging.info("="*60)
    logging.info("开始运行持久化浏览器访问")
    logging.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"日志文件: {log_file}")
    logging.info("="*60)
    
    driver = None
    try:
        # 设置持久化浏览器
        driver = setup_persistent_browser()
        if not driver:
            logging.error("❌ 浏览器设置失败")
            return False
        
        # 检查并完成登录
        if not manual_login_if_needed(driver):
            logging.error("❌ 登录失败")
            return False
        
        # 通过卖家中心访问自己的店铺
        own_store_success, own_store_url = access_own_store_via_seller_center(driver)
        if not own_store_success:
            logging.error("❌ 无法通过卖家中心访问自己的店铺")
            return False
        
        # 在自己店铺中模拟浏览行为
        logging.info("🎭 在自己店铺中模拟浏览行为...")
        for i in range(3):
            scroll_position = random.randint(300, 800) * (i + 1)
            driver.execute_script(f"window.scrollTo(0, {scroll_position});")
            time.sleep(random.uniform(2, 4))
        
        time.sleep(5)
        
        # 访问目标店铺
        target_mall_id = "634418212233370"
        success, page_source, file_path = access_target_store(driver, target_mall_id)
        
        if success:
            print("\n🎉 持久化浏览器访问成功！")
            print(f"✅ 成功访问目标店铺: {target_mall_id}")
            print(f"📁 页面已保存: {file_path}")
            return True
        else:
            print("\n⚠️ 持久化浏览器访问失败")
            return False
        
    except Exception as e:
        logging.error(f"程序执行过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if driver:
            try:
                input("\n⏳ 按回车键关闭浏览器...")
                driver.quit()
                logging.info("🔒 浏览器已关闭")
            except:
                pass

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 持久化浏览器访问测试成功！")
        else:
            print("\n⚠️ 持久化浏览器访问测试失败")
    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
