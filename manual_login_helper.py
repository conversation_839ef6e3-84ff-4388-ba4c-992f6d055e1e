#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动登录助手 - 帮助用户完成真实登录并保存cookie
"""

import json
import time
import logging
import os
from datetime import datetime

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"manual_login_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"手动登录助手日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def setup_manual_login_browser():
    """设置用于手动登录的浏览器"""
    try:
        import undetected_chromedriver as uc
        
        logging.info("🔧 开始设置手动登录浏览器...")
        
        # 配置Chrome选项
        options = uc.ChromeOptions()
        
        # 基本设置 - 保持可见以便手动操作
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1200,800')
        
        # 反检测设置
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--no-first-run')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-infobars')
        
        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
        
        # 创建反检测Chrome驱动
        driver = uc.Chrome(options=options, version_main=137)
        
        # 执行反检测脚本
        driver.execute_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            
            window.chrome = {
                runtime: {},
            };
        """)
        
        logging.info("✅ 手动登录浏览器初始化成功")
        return driver
        
    except ImportError:
        logging.error("❌ undetected-chromedriver未安装")
        return None
    except Exception as e:
        logging.error(f"❌ 手动登录浏览器初始化失败: {str(e)}")
        return None

def wait_for_manual_login(driver):
    """等待用户手动完成登录"""
    try:
        # 访问登录页面
        login_url = "https://seller.kuajingmaihuo.com/login"
        logging.info(f"🌐 访问登录页面: {login_url}")
        driver.get(login_url)
        
        # 等待页面加载
        time.sleep(5)
        
        print("\n" + "="*60)
        print("🔐 手动登录助手")
        print("="*60)
        print("📋 请按照以下步骤操作：")
        print("1. 浏览器已打开登录页面")
        print("2. 请在浏览器中手动输入用户名和密码")
        print("3. 完成任何验证码或安全验证")
        print("4. 点击登录按钮")
        print("5. 等待登录成功并跳转到主页")
        print("6. 登录成功后，在此控制台按回车键继续")
        print("="*60)
        
        # 等待用户确认登录完成
        input("⏳ 请完成登录后按回车键继续...")
        
        # 检查登录状态
        current_url = driver.current_url
        page_title = driver.title
        
        logging.info(f"📄 当前页面标题: {page_title}")
        logging.info(f"🔗 当前URL: {current_url}")
        
        # 判断是否登录成功
        if "login" not in current_url.lower() and "seller.kuajingmaihuo.com" in current_url:
            logging.info("✅ 登录成功！")
            return True
        else:
            logging.warning("⚠️ 可能还未完成登录，请检查")
            retry = input("是否重新检查登录状态？(y/n): ")
            if retry.lower() == 'y':
                return wait_for_manual_login(driver)
            return False
            
    except Exception as e:
        logging.error(f"❌ 等待手动登录时发生错误: {str(e)}")
        return False

def save_login_cookies(driver):
    """保存登录后的cookie"""
    try:
        # 获取所有cookie
        cookies = driver.get_cookies()
        logging.info(f"📥 获取到 {len(cookies)} 个cookie")
        
        # 保存到文件
        cookie_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "seller_cookies.json")
        with open(cookie_file, 'w', encoding='utf-8') as f:
            json.dump(cookies, f, ensure_ascii=False, indent=2)
        
        logging.info(f"💾 Cookie已保存到: {cookie_file}")
        
        # 显示重要的cookie信息
        important_cookies = []
        for cookie in cookies:
            if any(keyword in cookie['name'].lower() for keyword in ['token', 'session', 'auth', 'login', 'user']):
                important_cookies.append(cookie['name'])
        
        if important_cookies:
            logging.info(f"🔑 重要cookie: {important_cookies}")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 保存cookie失败: {str(e)}")
        return False

def test_seller_access(driver):
    """测试访问卖家后台页面"""
    try:
        test_urls = [
            ("卖家主页", "https://seller.kuajingmaihuo.com/"),
            ("商品管理", "https://seller.kuajingmaihuo.com/goods"),
            ("订单管理", "https://seller.kuajingmaihuo.com/order"),
        ]
        
        logging.info("\n🧪 测试访问卖家后台页面...")
        
        for page_name, url in test_urls:
            try:
                logging.info(f"🌐 测试访问: {page_name}")
                driver.get(url)
                time.sleep(3)
                
                current_url = driver.current_url
                page_title = driver.title
                
                if "login" not in current_url.lower():
                    logging.info(f"✅ {page_name} 访问成功")
                    logging.info(f"   标题: {page_title}")
                else:
                    logging.warning(f"❌ {page_name} 需要重新登录")
                
            except Exception as e:
                logging.warning(f"⚠️ 测试 {page_name} 时出错: {str(e)}")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 测试卖家后台访问失败: {str(e)}")
        return False

def main():
    """手动登录助手主函数"""
    logging.info("="*60)
    logging.info("开始运行手动登录助手")
    logging.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"日志文件: {log_file}")
    logging.info("="*60)
    
    driver = None
    try:
        # 设置浏览器
        driver = setup_manual_login_browser()
        if not driver:
            logging.error("❌ 浏览器设置失败")
            return False
        
        # 等待用户手动登录
        if not wait_for_manual_login(driver):
            logging.error("❌ 登录失败或未完成")
            return False
        
        # 保存登录cookie
        if not save_login_cookies(driver):
            logging.error("❌ 保存cookie失败")
            return False
        
        # 测试访问权限
        test_seller_access(driver)
        
        print("\n" + "="*60)
        print("🎉 手动登录助手完成！")
        print("="*60)
        print("✅ 登录成功")
        print("✅ Cookie已保存到 seller_cookies.json")
        print("✅ 访问权限已验证")
        print("\n📋 下一步：")
        print("1. 现在可以使用保存的cookie进行自动化访问")
        print("2. 运行自动化脚本来访问商店数据")
        print("="*60)
        
        # 询问是否立即测试Temu访问
        test_temu = input("\n是否立即测试访问Temu商店页面？(y/n): ")
        if test_temu.lower() == 'y':
            test_temu_access(driver)
        
        return True
        
    except Exception as e:
        logging.error(f"程序执行过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if driver:
            try:
                input("\n⏳ 按回车键关闭浏览器...")
                driver.quit()
                logging.info("🔒 浏览器已关闭")
            except:
                pass

def test_temu_access(driver):
    """使用登录后的状态测试Temu访问"""
    try:
        logging.info("\n🧪 测试Temu商店访问...")
        
        # 访问Temu商店页面
        mall_url = "https://www.temu.com/mall.html?mall_id=634418212233370"
        logging.info(f"🌐 访问商店页面: {mall_url}")
        
        driver.get(mall_url)
        time.sleep(10)
        
        current_url = driver.current_url
        page_title = driver.title
        page_source = driver.page_source
        
        logging.info(f"📄 页面标题: {page_title}")
        logging.info(f"🔗 当前URL: {current_url}")
        logging.info(f"📊 页面内容长度: {len(page_source)} 字符")
        
        if "no_access" not in current_url and "没有互联网连接" not in page_title:
            logging.info("🎉 Temu商店页面访问成功！")
            
            # 保存成功页面
            success_file = f"temu_success_{datetime.now().strftime('%H%M%S')}.html"
            with open(success_file, 'w', encoding='utf-8') as f:
                f.write(page_source)
            logging.info(f"📁 成功页面已保存: {success_file}")
            
        else:
            logging.warning("⚠️ Temu商店页面仍然被阻止")
        
    except Exception as e:
        logging.error(f"❌ 测试Temu访问失败: {str(e)}")

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 手动登录助手执行成功！")
        else:
            print("\n⚠️ 手动登录助手执行失败")
    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
