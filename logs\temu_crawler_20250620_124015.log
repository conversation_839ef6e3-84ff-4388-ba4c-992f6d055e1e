2025-06-20 12:40:15,543 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250620_124015.log
2025-06-20 12:40:15,543 [INFO] ==================================================
2025-06-20 12:40:15,543 [INFO] 开始运行Temu数据抓取
2025-06-20 12:40:15,575 [INFO] 时间: 2025-06-20 12:40:15
2025-06-20 12:40:15,575 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250620_124015.log
2025-06-20 12:40:15,575 [INFO] ==================================================
2025-06-20 12:40:15,576 [INFO] 
检查Cookie状态...
2025-06-20 12:40:15,576 [WARNING] ⚠️ Cookie可能已过期，需要手动刷新...
2025-06-20 12:40:17,433 [INFO] Cookie检查/更新完成
2025-06-20 12:40:17,434 [INFO] 
获取商店ID列表...
2025-06-20 12:40:17,434 [INFO] 请求商店ID列表: http://172.25.165.28:8055/items/shop_data
2025-06-20 12:40:17,484 [INFO] 获取商店ID状态码: 200
2025-06-20 12:40:17,484 [INFO] 获取到的商店数据: {
  "data": [
    {
      "mall_id": "634418212233370"
    },
    {
      "mall_id": "634418221321199"
    },
    {
      "mall_id": "634418221704901"
    },
    {
      "mall_id": "634418213167233"
    }
  ]
}...
2025-06-20 12:40:17,484 [INFO] 获取到的全部mall_id数量: 4
2025-06-20 12:40:17,484 [INFO] 去重后的mall_id数量: 4
2025-06-20 12:40:17,484 [INFO] 去除了 0 个重复的mall_id
2025-06-20 12:40:17,485 [INFO] 最终使用的商店ID列表: ['634418213167233', '634418212233370', '634418221321199', '634418221704901']
2025-06-20 12:40:17,485 [INFO] 
开始处理第 1 批商店，共 1 个
2025-06-20 12:40:17,485 [INFO] 
==================================================
2025-06-20 12:40:17,485 [INFO] 正在处理商店 1/4: mall_id 634418213167233
2025-06-20 12:40:17,485 [INFO] ==================================================
2025-06-20 12:40:17,485 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418213167233
2025-06-20 12:40:17,485 [INFO] 随机延迟 11.93 秒
2025-06-20 12:40:29,413 [INFO] 🔧 启动浏览器等待手动操作...
2025-06-20 12:40:33,496 [INFO] patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-06-20 12:40:41,096 [INFO] 📥 获取到 20 个cookie
2025-06-20 12:40:41,102 [INFO] ✅ 成功获取cookie，包含 20 个条目
2025-06-20 12:40:41,102 [INFO] 🔑 获取到verifyAuthToken: pBBujQO62qk1lY_YjNmB...
2025-06-20 12:40:46,594 [INFO] 🌐 浏览器保持打开状态
2025-06-20 12:40:46,594 [INFO] ✅ 添加verifyAuthToken到headers
2025-06-20 12:40:46,594 [INFO] 生成增强Temu请求头: 15 个字段
2025-06-20 12:40:47,424 [INFO] Temu API状态码: 200
2025-06-20 12:40:47,427 [INFO] 处理商店数据...
2025-06-20 12:40:47,427 [INFO] 处理后的商店数据: {"update_time": "2025-06-20 12:40:47", "mall_id": "634418213167233", "mall_name": "PersonalizedHall", "mall_logo": "https://img.kwcdn.com/supplier-public-tag/1fa3206750/f52a4c0c-9dbe-4ed2-b5fd-b7ff5e80bc5c_300x300.jpeg", "goods_sales_num": 100000, "goods_num": 653, "review_num": 3141}
2025-06-20 12:40:47,427 [INFO] 保存商店数据: mall_id=634418213167233
2025-06-20 12:40:47,519 [INFO] 成功更新商店数据: 634418213167233
2025-06-20 12:40:47,550 [ERROR] 记录商店变更日志时发生异常: unsupported operand type(s) for -: 'int' and 'NoneType'
2025-06-20 12:40:47,552 [INFO] 处理产品数据...
2025-06-20 12:40:47,552 [INFO] 找到8个产品
2025-06-20 12:40:47,552 [INFO] 价格转换: 693美分 -> 6.93美元
2025-06-20 12:40:47,552 [INFO] 处理后的产品数据 1: {"update_time": "2025-06-20 12:40:47", "mall_id": "634418213167233", "goods_id": "601099689880288", "title": "定制名字手镯 - 可调节不锈钢手环，18K镀金，适合日常佩戴及派对", "image_url": "https://img.kwcdn.com/product/fancy/91642ddd-c1ba-4499-ae2b-ebc439142d13.jpg", "sales_num": 1, "price": 6.93, "comment": 44}
2025-06-20 12:40:47,552 [INFO] 价格转换: 319美分 -> 3.19美元
2025-06-20 12:40:47,553 [INFO] 处理后的产品数据 2: {"update_time": "2025-06-20 12:40:47", "mall_id": "634418213167233", "goods_id": "601099607345319", "title": "1 件个人化波西米亚风格开口字母戒指，订制姓名雕刻不锈钢首饰，优雅的女士浪花设计，非常适合休闲装或特殊场合，生日或周年纪念礼物的理想选择", "image_url": "https://img.kwcdn.com/product/fancy/45038461-281b-430a-bbc5-a8c7db0f4b1c.jpg", "sales_num": 15, "price": 3.19, "comment": 254}
2025-06-20 12:40:47,553 [INFO] 价格转换: 487美分 -> 4.87美元
2025-06-20 12:40:47,554 [INFO] 价格转换: 391美分 -> 3.91美元
2025-06-20 12:40:47,554 [INFO] 价格转换: 504美分 -> 5.04美元
2025-06-20 12:40:47,554 [INFO] 价格转换: 330美分 -> 3.3美元
2025-06-20 12:40:47,554 [INFO] 价格转换: 505美分 -> 5.05美元
2025-06-20 12:40:47,555 [INFO] 价格转换: 725美分 -> 7.25美元
2025-06-20 12:40:47,555 [INFO] 为mall_id 634418213167233找到8个产品
2025-06-20 12:40:47,555 [INFO] 正在保存产品 1/8 (goods_id: 601099689880288)
2025-06-20 12:40:47,555 [INFO] 保存/更新产品数据: goods_id=601099689880288
2025-06-20 12:40:47,670 [INFO] 成功创建新产品数据: 601099689880288
2025-06-20 12:40:47,745 [INFO] 成功记录产品变更日志: product_data_id=838, 订单增长: 0, 评论增长: 0, 当前销售量: 1, 当前评论数: 44
2025-06-20 12:40:47,746 [INFO] 随机延迟 3.76 秒
2025-06-20 12:40:51,506 [INFO] 正在保存产品 2/8 (goods_id: 601099607345319)
2025-06-20 12:40:51,507 [INFO] 保存/更新产品数据: goods_id=601099607345319
2025-06-20 12:40:51,620 [INFO] 成功创建新产品数据: 601099607345319
2025-06-20 12:40:51,689 [INFO] 成功记录产品变更日志: product_data_id=839, 订单增长: 0, 评论增长: 0, 当前销售量: 15, 当前评论数: 254
2025-06-20 12:40:51,690 [INFO] 随机延迟 4.85 秒
2025-06-20 12:40:56,545 [INFO] 正在保存产品 3/8 (goods_id: 601100138925628)
2025-06-20 12:40:56,545 [INFO] 保存/更新产品数据: goods_id=601100138925628
2025-06-20 12:40:56,649 [INFO] 成功创建新产品数据: 601100138925628
2025-06-20 12:40:56,720 [INFO] 成功记录产品变更日志: product_data_id=840, 订单增长: 0, 评论增长: 0, 当前销售量: 479, 当前评论数: 30
2025-06-20 12:40:56,720 [INFO] 随机延迟 3.86 秒
2025-06-20 12:41:00,585 [INFO] 正在保存产品 4/8 (goods_id: 601099599030981)
2025-06-20 12:41:00,585 [INFO] 保存/更新产品数据: goods_id=601099599030981
2025-06-20 12:41:00,685 [INFO] 成功创建新产品数据: 601099599030981
2025-06-20 12:41:00,769 [INFO] 成功记录产品变更日志: product_data_id=841, 订单增长: 0, 评论增长: 0, 当前销售量: 3, 当前评论数: 142
2025-06-20 12:41:00,771 [INFO] 随机延迟 2.84 秒
2025-06-20 12:41:03,611 [INFO] 正在保存产品 5/8 (goods_id: 601099762447985)
2025-06-20 12:41:03,612 [INFO] 保存/更新产品数据: goods_id=601099762447985
2025-06-20 12:41:03,713 [INFO] 成功创建新产品数据: 601099762447985
2025-06-20 12:41:03,807 [INFO] 成功记录产品变更日志: product_data_id=842, 订单增长: 0, 评论增长: 0, 当前销售量: 2, 当前评论数: 34
2025-06-20 12:41:03,808 [INFO] 随机延迟 2.28 秒
2025-06-20 12:41:06,087 [INFO] 正在保存产品 6/8 (goods_id: 601099984342617)
2025-06-20 12:41:06,087 [INFO] 保存/更新产品数据: goods_id=601099984342617
2025-06-20 12:41:06,203 [INFO] 成功创建新产品数据: 601099984342617
2025-06-20 12:41:06,275 [INFO] 成功记录产品变更日志: product_data_id=843, 订单增长: 0, 评论增长: 0, 当前销售量: 2, 当前评论数: 85
2025-06-20 12:41:06,276 [INFO] 随机延迟 4.47 秒
2025-06-20 12:41:10,744 [INFO] 正在保存产品 7/8 (goods_id: 601099717060853)
2025-06-20 12:41:10,744 [INFO] 保存/更新产品数据: goods_id=601099717060853
2025-06-20 12:41:10,877 [INFO] 成功创建新产品数据: 601099717060853
2025-06-20 12:41:10,945 [INFO] 成功记录产品变更日志: product_data_id=844, 订单增长: 0, 评论增长: 0, 当前销售量: 960, 当前评论数: 26
2025-06-20 12:41:10,946 [INFO] 随机延迟 4.31 秒
2025-06-20 12:41:15,256 [INFO] 正在保存产品 8/8 (goods_id: 601099975304074)
2025-06-20 12:41:15,256 [INFO] 保存/更新产品数据: goods_id=601099975304074
2025-06-20 12:41:15,400 [INFO] 成功创建新产品数据: 601099975304074
2025-06-20 12:41:15,480 [INFO] 成功记录产品变更日志: product_data_id=845, 订单增长: 0, 评论增长: 0, 当前销售量: 61, 当前评论数: 1
2025-06-20 12:41:15,481 [INFO] 商店 634418213167233 处理完成: 成功保存 8/8 个产品
2025-06-20 12:41:15,481 [INFO] 商店间延迟 60.51 秒
2025-06-20 12:42:15,989 [INFO] 
批次间休息 234.19 秒，降低请求频率...
2025-06-20 12:46:10,179 [INFO] 
开始处理第 2 批商店，共 1 个
2025-06-20 12:46:10,190 [INFO] 
==================================================
2025-06-20 12:46:10,190 [INFO] 正在处理商店 2/4: mall_id 634418212233370
2025-06-20 12:46:10,191 [INFO] ==================================================
2025-06-20 12:46:10,191 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418212233370
2025-06-20 12:46:10,191 [INFO] 随机延迟 12.98 秒
2025-06-20 12:46:23,172 [INFO] 🔧 启动浏览器等待手动操作...
2025-06-20 12:47:24,394 [ERROR] ❌ 获取cookie时出错: Message: session not created: cannot connect to chrome at 127.0.0.1:49242
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0xdd3b03+62899]
	GetHandleVerifier [0x0xdd3b44+62964]
	(No symbol) [0x0xc00f50]
	(No symbol) [0x0xbf4cca]
	(No symbol) [0x0xc39c66]
	(No symbol) [0x0xc304cf]
	(No symbol) [0x0xc30306]
	(No symbol) [0x0xc7a36a]
	(No symbol) [0x0xc79c0a]
	(No symbol) [0x0xc6e306]
	(No symbol) [0x0xc3d670]
	(No symbol) [0x0xc3e4e4]
	GetHandleVerifier [0x0x1034793+2556483]
	GetHandleVerifier [0x0x102fd02+2537394]
	GetHandleVerifier [0x0xdfa2fa+220586]
	GetHandleVerifier [0x0xdeaae8+157080]
	GetHandleVerifier [0x0xdf141d+184013]
	GetHandleVerifier [0x0xddba68+95512]
	GetHandleVerifier [0x0xddbc10+95936]
	GetHandleVerifier [0x0xdc6b5a+9738]
	BaseThreadInitThunk [0x0x75997ba9+25]
	RtlInitializeExceptionChain [0x0x76ecc3ab+107]
	RtlClearBits [0x0x76ecc32f+191]

2025-06-20 12:47:24,411 [WARNING] 警告: 无法获取cookie，尝试更新cookie
2025-06-20 12:47:24,411 [WARNING] ⚠️ Cookie可能已过期，需要手动刷新...
2025-06-20 13:21:22,234 [INFO] ensuring close
2025-06-20 13:21:22,235 [INFO] ensuring close
