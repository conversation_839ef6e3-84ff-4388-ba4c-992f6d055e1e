2025-04-09 12:51:22,023 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250409_125122.log
2025-04-09 12:51:22,024 [INFO] ==================================================
2025-04-09 12:51:22,024 [INFO] 开始运行Temu数据抓取
2025-04-09 12:51:22,052 [INFO] 时间: 2025-04-09 12:51:22
2025-04-09 12:51:22,053 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250409_125122.log
2025-04-09 12:51:22,053 [INFO] ==================================================
2025-04-09 12:51:22,053 [INFO] 
更新Temu Cookie...
2025-04-09 12:51:22,054 [INFO] 加载初始cookie文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json
2025-04-09 12:51:22,054 [INFO] 成功加载初始cookie，共19个
2025-04-09 12:51:22,054 [INFO] 开始访问Temu主页...
2025-04-09 12:51:25,891 [INFO] Temu主页访问状态码: 200
2025-04-09 12:51:25,891 [INFO] 成功更新cookie文件，共19个cookie
2025-04-09 12:51:25,891 [INFO] Cookie更新成功，继续数据抓取
2025-04-09 12:51:25,891 [INFO] 
获取商店ID列表...
2025-04-09 12:51:25,891 [INFO] 请求商店ID列表: http://172.25.165.28:8055/items/shop_data
2025-04-09 12:51:25,985 [INFO] 获取商店ID状态码: 200
2025-04-09 12:51:25,985 [INFO] 获取到的商店数据: {
  "data": [
    {
      "mall_id": "634418219240900"
    },
    {
      "mall_id": "634418210147170"
    },
    {
      "mall_id": "634418219240900"
    },
    {
      "mall_id": "634418210147170"
    }
  ]
}...
2025-04-09 12:51:25,986 [INFO] 获取到的全部mall_id数量: 4
2025-04-09 12:51:25,986 [INFO] 去重后的mall_id数量: 2
2025-04-09 12:51:25,986 [INFO] 去除了 2 个重复的mall_id
2025-04-09 12:51:25,986 [INFO] 以下mall_id有重复记录: {"634418219240900": 2, "634418210147170": 2}
2025-04-09 12:51:25,987 [INFO] 最终使用的商店ID列表: ['634418219240900', '634418210147170']
2025-04-09 12:51:25,987 [INFO] 
==================================================
2025-04-09 12:51:25,987 [INFO] 正在处理商店 1/2: mall_id 634418219240900
2025-04-09 12:51:25,987 [INFO] ==================================================
2025-04-09 12:51:25,987 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418219240900
2025-04-09 12:51:25,987 [INFO] 请求payload: {"mallId": "634418219240900", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418219240900", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-04-09 12:51:25,987 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-04-09 12:51:25,992 [INFO] 成功加载cookie，包含 19 个条目
2025-04-09 12:51:25,992 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-04-09 12:51:27,477 [INFO] Temu API状态码: 200
2025-04-09 12:51:27,486 [INFO] Temu返回数据结构: ['success', 'error_code', 'errorCode', 'result']
2025-04-09 12:51:27,486 [INFO] 成功获取到8件商品，尽管page_size设置为1
2025-04-09 12:51:27,486 [INFO] 找到'result'键，包含店铺数据
2025-04-09 12:51:27,486 [INFO] 成功获取到8件商品
2025-04-09 12:51:27,486 [INFO] 处理商店数据...
2025-04-09 12:51:27,486 [INFO] 处理后的商店数据: {"update_time": "2025-04-09 12:51:27", "mall_id": "634418219240900", "mall_name": "HongYi TieYi", "mall_logo": "https://img.kwcdn.com/supplier-public-tag/1fad185920/66ed21e1-6994-4e43-84be-664e35e7d67b_300x300.jpeg", "goods_sales_num": 27000, "goods_num": 287, "review_num": 210}
2025-04-09 12:51:27,486 [INFO] 处理商店 634418219240900 时发生异常: name 'save_shop_data' is not defined
2025-04-09 12:51:27,486 [INFO] 
==================================================
2025-04-09 12:51:27,486 [INFO] 正在处理商店 2/2: mall_id 634418210147170
2025-04-09 12:51:27,486 [INFO] ==================================================
2025-04-09 12:51:27,486 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418210147170
2025-04-09 12:51:27,486 [INFO] 请求payload: {"mallId": "634418210147170", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418210147170", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-04-09 12:51:27,486 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-04-09 12:51:27,486 [INFO] 成功加载cookie，包含 19 个条目
2025-04-09 12:51:27,494 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-04-09 12:51:28,977 [INFO] Temu API状态码: 200
2025-04-09 12:51:28,979 [INFO] Temu返回数据结构: ['success', 'error_code', 'errorCode', 'result']
2025-04-09 12:51:28,979 [INFO] 成功获取到8件商品，尽管page_size设置为1
2025-04-09 12:51:28,979 [INFO] 找到'result'键，包含店铺数据
2025-04-09 12:51:28,979 [INFO] 成功获取到8件商品
2025-04-09 12:51:28,982 [INFO] 处理商店数据...
2025-04-09 12:51:28,982 [INFO] 处理后的商店数据: {"update_time": "2025-04-09 12:51:28", "mall_id": "634418210147170", "mall_name": "LN JEWELRY", "mall_logo": "https://img.kwcdn.com/supplier-public-tag/1e2331480c/cf3510fe-8e79-4443-bb8e-1b1cd13802cc_300x300.jpeg", "goods_sales_num": 300000, "goods_num": 80, "review_num": 8556}
2025-04-09 12:51:28,982 [INFO] 处理商店 634418210147170 时发生异常: name 'save_shop_data' is not defined
2025-04-09 12:51:28,986 [INFO] 
==================================================
2025-04-09 12:51:28,986 [INFO] Temu数据抓取汇总
2025-04-09 12:51:28,986 [INFO] 处理时间: 2025-04-09 12:51:28
2025-04-09 12:51:28,986 [INFO] 处理商店总数: 2
2025-04-09 12:51:28,986 [INFO] 成功保存商店数: 0
2025-04-09 12:51:28,986 [INFO] 处理产品总数: 0
2025-04-09 12:51:28,986 [INFO] 成功保存产品数: 0
2025-04-09 12:51:28,986 [INFO] ==================================================
