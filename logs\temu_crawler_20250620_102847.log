2025-06-20 10:28:47,001 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250620_102847.log
2025-06-20 10:28:47,002 [INFO] 浏览器模拟日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_simulation_20250620_102847.log
2025-06-20 10:28:47,002 [INFO] ============================================================
2025-06-20 10:28:47,002 [INFO] 开始运行Temu数据抓取 - 完整浏览器行为模拟
2025-06-20 10:28:47,034 [INFO] 时间: 2025-06-20 10:28:47
2025-06-20 10:28:47,034 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_simulation_20250620_102847.log
2025-06-20 10:28:47,037 [INFO] ============================================================
2025-06-20 10:28:47,037 [INFO] 🎭 浏览器模拟流程：
2025-06-20 10:28:47,037 [INFO] 1. 创建模拟浏览器session
2025-06-20 10:28:47,037 [INFO] 2. 加载cookie到session
2025-06-20 10:28:47,037 [INFO] 3. 访问商店页面
2025-06-20 10:28:47,038 [INFO] 4. 提取页面参数
2025-06-20 10:28:47,038 [INFO] 5. 模拟用户操作延迟
2025-06-20 10:28:47,038 [INFO] 6. 发送API请求
2025-06-20 10:28:47,038 [INFO] 7. 处理和保存数据
2025-06-20 10:28:47,038 [INFO] 
🎯 测试商店: 634418212233370
2025-06-20 10:28:47,038 [INFO] 🎯 开始完整浏览器行为模拟: mall_id=634418212233370
2025-06-20 10:28:47,038 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-06-20 10:28:47,039 [INFO] 成功加载cookie，包含 20 个条目
2025-06-20 10:28:47,039 [INFO] ✅ Cookie已加载到session
2025-06-20 10:28:47,039 [INFO] 🌐 模拟访问商店页面: https://www.temu.com/mall.html?mall_id=634418212233370
2025-06-20 10:28:47,670 [INFO] 页面访问状态码: 200
2025-06-20 10:28:47,670 [INFO] ✅ 成功访问商店页面
2025-06-20 10:28:47,670 [INFO] 🎭 模拟浏览页面 6.1 秒
2025-06-20 10:28:53,744 [INFO] 📡 发送API请求: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList
2025-06-20 10:28:53,744 [INFO] 使用参数: list_id=r8472007775184705, anti_content=1
2025-06-20 10:28:53,744 [INFO] ⏱️ 模拟用户操作延迟 3.0 秒
2025-06-20 10:28:57,262 [INFO] API响应状态码: 429
2025-06-20 10:28:57,262 [WARNING] ⚠️ 仍然遇到429错误
2025-06-20 10:28:57,263 [INFO] 即使完整模拟浏览器行为也被限制
2025-06-20 10:28:57,264 [WARNING] ⚠️ 浏览器模拟未能获取数据
