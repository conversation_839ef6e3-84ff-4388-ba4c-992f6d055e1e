2025-06-20 12:26:57,275 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250620_122657.log
2025-06-20 12:26:57,276 [INFO] ==================================================
2025-06-20 12:26:57,276 [INFO] 开始运行Temu数据抓取
2025-06-20 12:26:57,304 [INFO] 时间: 2025-06-20 12:26:57
2025-06-20 12:26:57,304 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250620_122657.log
2025-06-20 12:26:57,305 [INFO] ==================================================
2025-06-20 12:26:57,305 [INFO] 
检查Cookie状态...
2025-06-20 12:26:57,305 [INFO] 🔄 开始自动化刷新cookie...
2025-06-20 12:27:01,414 [INFO] patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-06-20 12:27:02,212 [INFO] 🌐 访问卖家中心...
2025-06-20 12:27:07,644 [WARNING] ⚠️ 需要手动登录，请在浏览器中完成登录...
2025-06-20 12:27:17,647 [INFO] ⏳ 等待登录中... (10/300秒)
2025-06-20 12:27:27,651 [INFO] ⏳ 等待登录中... (20/300秒)
2025-06-20 12:27:37,655 [INFO] ⏳ 等待登录中... (30/300秒)
2025-06-20 12:27:47,659 [INFO] ⏳ 等待登录中... (40/300秒)
2025-06-20 12:27:57,663 [INFO] ⏳ 等待登录中... (50/300秒)
2025-06-20 12:27:57,669 [INFO] ✅ 登录成功，获取新cookie...
2025-06-20 12:28:01,365 [INFO] 📥 获取到 15 个新cookie
2025-06-20 12:28:01,368 [INFO] ✅ Cookie刷新成功
2025-06-20 12:28:01,368 [INFO] Cookie检查/更新完成
2025-06-20 12:28:01,368 [INFO] 
获取商店ID列表...
2025-06-20 12:28:01,368 [INFO] 请求商店ID列表: http://172.25.165.28:8055/items/shop_data
2025-06-20 12:28:01,421 [INFO] 获取商店ID状态码: 200
2025-06-20 12:28:01,421 [INFO] 获取到的商店数据: {
  "data": [
    {
      "mall_id": "634418212233370"
    },
    {
      "mall_id": "634418221321199"
    },
    {
      "mall_id": "634418221704901"
    },
    {
      "mall_id": "634418213167233"
    }
  ]
}...
2025-06-20 12:28:01,421 [INFO] 获取到的全部mall_id数量: 4
2025-06-20 12:28:01,421 [INFO] 去重后的mall_id数量: 4
2025-06-20 12:28:01,422 [INFO] 去除了 0 个重复的mall_id
2025-06-20 12:28:01,422 [INFO] 最终使用的商店ID列表: ['634418213167233', '634418221321199', '634418221704901', '634418212233370']
2025-06-20 12:28:01,422 [INFO] 
开始处理第 1 批商店，共 1 个
2025-06-20 12:28:01,422 [INFO] 
==================================================
2025-06-20 12:28:01,422 [INFO] 正在处理商店 1/4: mall_id 634418213167233
2025-06-20 12:28:01,422 [INFO] ==================================================
2025-06-20 12:28:01,422 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418213167233
2025-06-20 12:28:01,423 [INFO] 随机延迟 16.49 秒
2025-06-20 12:28:17,916 [INFO] 🔧 启动自动化cookie获取...
2025-06-20 12:28:21,976 [INFO] patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-06-20 12:28:22,746 [INFO] setting properties for headless
2025-06-20 12:28:26,324 [INFO] 📥 获取到 16 个cookie
2025-06-20 12:28:26,328 [INFO] ✅ 成功获取cookie，包含 16 个条目
2025-06-20 12:28:26,328 [INFO] 🔑 获取到verifyAuthToken: pBBujQO62qk1lY_YjNmB...
2025-06-20 12:28:26,328 [INFO] ✅ 添加verifyAuthToken到headers
2025-06-20 12:28:26,329 [INFO] 生成增强Temu请求头: 15 个字段
2025-06-20 12:28:27,067 [INFO] Temu API状态码: 403
2025-06-20 12:28:27,067 [ERROR] 获取mall_id为634418213167233的Temu数据时出错: 403
2025-06-20 12:28:27,067 [ERROR] 返回内容: {"success":false,"error_code":40001,"errorCode":40001,"verify_auth_token":"pBBujQO62qk1lY_YjNmBmw582062ca0af13fff3"}...
2025-06-20 12:28:27,067 [WARNING] 可能是cookie过期，尝试更新cookie...
2025-06-20 12:28:27,067 [INFO] 🔄 开始自动化刷新cookie...
2025-06-20 12:28:31,114 [INFO] patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-06-20 12:28:31,900 [INFO] 🌐 访问卖家中心...
2025-06-20 12:28:37,452 [INFO] ✅ 登录成功，获取新cookie...
2025-06-20 12:28:41,411 [INFO] 📥 获取到 15 个新cookie
2025-06-20 12:28:41,416 [INFO] ✅ Cookie刷新成功
2025-06-20 12:28:41,417 [INFO] Cookie更新成功，重试请求...
2025-06-20 12:28:41,417 [INFO] 🔧 启动自动化cookie获取...
2025-06-20 12:28:41,978 [INFO] setting properties for headless
2025-06-20 12:28:45,763 [INFO] 📥 获取到 16 个cookie
2025-06-20 12:28:45,821 [INFO] ✅ 成功获取cookie，包含 16 个条目
2025-06-20 12:28:45,821 [INFO] 🔑 获取到verifyAuthToken: pBBujQO62qk1lY_YjNmB...
2025-06-20 12:28:45,821 [INFO] ✅ 添加verifyAuthToken到headers
2025-06-20 12:28:45,821 [INFO] 生成增强Temu请求头: 15 个字段
2025-06-20 12:28:46,565 [INFO] 重试后的Temu API状态码: 403
2025-06-20 12:28:46,565 [ERROR] 重试后仍然失败: 403
2025-06-20 12:28:46,567 [INFO] 由于API错误，跳过mall_id为634418213167233的商店
2025-06-20 12:28:46,567 [INFO] 
批次间休息 226.59 秒，降低请求频率...
2025-06-20 12:29:04,086 [INFO] ensuring close
2025-06-20 12:29:04,087 [INFO] ensuring close
2025-06-20 12:29:04,087 [INFO] ensuring close
2025-06-20 12:29:04,087 [INFO] ensuring close
