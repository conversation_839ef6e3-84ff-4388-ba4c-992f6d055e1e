2025-06-20 12:07:51,915 [INFO] 正确API调用日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\correct_api_with_browser_cookies_20250620_120751.log
2025-06-20 12:07:51,915 [INFO] ============================================================
2025-06-20 12:07:51,916 [INFO] 开始使用浏览器cookie调用正确的Temu API接口
2025-06-20 12:07:51,916 [INFO] 时间: 2025-06-20 12:07:51
2025-06-20 12:07:51,916 [INFO] ============================================================
2025-06-20 12:07:52,130 [INFO] 🔧 启动浏览器获取cookie...
2025-06-20 12:07:57,014 [INFO] patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
2025-06-20 12:07:57,826 [INFO] setting properties for headless
2025-06-20 12:08:01,276 [INFO] 📥 获取到 16 个cookie
2025-06-20 12:08:01,279 [INFO] ✅ 成功获取浏览器cookie和User-Agent
2025-06-20 12:08:01,280 [INFO] 🍪 获取到 16 个cookie
2025-06-20 12:08:01,280 [INFO] 🌐 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/137....
2025-06-20 12:08:01,280 [INFO] 🔑 找到关键cookie: ['_bee', 'api_uid', 'verifyAuthToken']
2025-06-20 12:08:01,280 [INFO] ✅ Temu API会话创建成功
2025-06-20 12:08:01,281 [INFO] 🎯 使用浏览器cookie调用Temu商店API: 634418212233370
2025-06-20 12:08:01,281 [INFO] 📡 调用API: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList
2025-06-20 12:08:01,281 [INFO] 📦 请求payload: {"mallId": "634418212233370", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418212233370", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "source_page_id": "10040_1750392257867_ulwao19zgq"}
2025-06-20 12:08:01,842 [INFO] 📊 响应状态码: 403
2025-06-20 12:08:01,842 [INFO] 📊 响应头: {'Date': 'Fri, 20 Jun 2025 04:08:03 GMT', 'Content-Type': 'application/json', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'x-gateway-request-id': '1750392483634-0907bdc3c8ab7aecb122ee30625300da-20', 'access-control-allow-origin': 'https://www.temu.com', 'vary': 'Origin', 'access-control-allow-headers': 'Origin, X-Requested-With, Content-Type, X_Requested_With, Accept, X-HTTP-Method-Override, Cookie, AccessToken, PASSID, VerifyAuthToken, Anti-Content', 'access-control-allow-methods': 'GET, POST, OPTIONS, DELETE, PUT', 'access-control-allow-credentials': 'true', 'strict-transport-security': 'max-age=31536000', 'content-security-policy-report-only': "default-src 'none';script-src 'report-sample';report-uri /api/sec-csp/110000007/sec-gif", 'yak-timeinfo': '1750392483634|48', 'alt-svc': 'h3=":443"; ma=86400', 'cip': '************', 'cf-cache-status': 'DYNAMIC', 'Server': 'cloudflare', 'CF-RAY': '9528671dff22c863-HKG', 'Content-Encoding': 'br'}
2025-06-20 12:08:01,842 [WARNING] ⚠️ API被禁止访问 (403)
2025-06-20 12:08:01,843 [INFO] 📄 响应内容: !�   �ԗ
L�� rg?&1A��"��!�3�{�d#<��"��H�t�...
2025-06-20 12:08:01,845 [INFO] ensuring close
