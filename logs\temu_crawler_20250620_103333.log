2025-06-20 10:33:33,716 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250620_103333.log
2025-06-20 10:33:33,717 [INFO] 页面提取日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_extraction_20250620_103333.log
2025-06-20 10:33:33,717 [INFO] ============================================================
2025-06-20 10:33:33,717 [INFO] 开始运行Temu数据抓取 - 页面数据提取模式
2025-06-20 10:33:33,750 [INFO] 时间: 2025-06-20 10:33:33
2025-06-20 10:33:33,750 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_extraction_20250620_103333.log
2025-06-20 10:33:33,750 [INFO] ============================================================
2025-06-20 10:33:33,751 [INFO] 📄 页面提取流程：
2025-06-20 10:33:33,751 [INFO] 1. 访问商店页面
2025-06-20 10:33:33,751 [INFO] 2. 提取页面中的JSON数据
2025-06-20 10:33:33,751 [INFO] 3. 解析HTML元素
2025-06-20 10:33:33,751 [INFO] 4. 使用正则表达式提取
2025-06-20 10:33:33,751 [INFO] 5. 保存提取的数据
2025-06-20 10:33:33,752 [INFO] 
🎯 测试商店: 634418212233370
2025-06-20 10:33:33,752 [INFO] 🎯 开始页面数据提取: mall_id=634418212233370
2025-06-20 10:33:33,752 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-06-20 10:33:33,753 [INFO] 成功加载cookie，包含 20 个条目
2025-06-20 10:33:33,753 [INFO] ✅ Cookie已加载到session
2025-06-20 10:33:33,753 [INFO] 🌐 访问商店页面: https://www.temu.com/mall.html?mall_id=634418212233370
2025-06-20 10:33:34,297 [INFO] 页面访问状态码: 200
2025-06-20 10:33:34,297 [INFO] ✅ 成功访问商店页面
2025-06-20 10:33:34,298 [INFO] 📄 页面内容已保存到: debug_page_634418212233370.html
2025-06-20 10:33:34,299 [INFO] 🔍 开始从页面提取数据...
2025-06-20 10:33:34,299 [INFO] 方法1：查找页面中的JSON数据...
2025-06-20 10:33:34,299 [INFO] 方法2：使用BeautifulSoup解析HTML...
2025-06-20 10:33:34,300 [INFO] 方法3：使用正则表达式直接提取...
2025-06-20 10:33:34,301 [INFO] 📊 数据提取完成:
2025-06-20 10:33:34,301 [INFO]   - 商店名称: 未找到
2025-06-20 10:33:34,301 [INFO]   - 产品数量: 0
2025-06-20 10:33:34,301 [INFO]   - 原始数据块: 0
2025-06-20 10:33:34,302 [WARNING] ⚠️ 未能从页面提取到有效数据
2025-06-20 10:33:34,303 [WARNING] ⚠️ 页面数据提取失败
