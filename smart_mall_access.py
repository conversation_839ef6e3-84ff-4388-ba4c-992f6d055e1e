#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能商店访问策略 - 先访问自己的店铺，再访问目标店铺
"""

import json
import time
import logging
import random
import os
from datetime import datetime

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"smart_mall_access_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"智能商店访问日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def setup_smart_browser():
    """设置智能访问浏览器"""
    try:
        import undetected_chromedriver as uc
        
        logging.info("🔧 开始设置智能访问浏览器...")
        
        # 配置Chrome选项
        options = uc.ChromeOptions()
        
        # 基本设置
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1200,800')
        
        # 反检测设置
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--no-first-run')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-infobars')
        
        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
        
        # 创建反检测Chrome驱动
        driver = uc.Chrome(options=options, version_main=137)
        
        # 执行反检测脚本
        driver.execute_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            
            window.chrome = {
                runtime: {},
            };
        """)
        
        logging.info("✅ 智能访问浏览器初始化成功")
        return driver
        
    except ImportError:
        logging.error("❌ undetected-chromedriver未安装")
        return None
    except Exception as e:
        logging.error(f"❌ 智能访问浏览器初始化失败: {str(e)}")
        return None

def load_seller_cookies(driver):
    """加载卖家后台的cookie"""
    try:
        cookie_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "seller_cookies.json")
        
        if not os.path.exists(cookie_file):
            logging.error("❌ 卖家Cookie文件不存在，请先运行手动登录助手")
            return False
        
        # 先访问卖家后台域名
        logging.info("🌐 访问卖家后台以设置cookie上下文...")
        driver.get("https://seller.kuajingmaihuo.com/")
        time.sleep(3)
        
        # 加载cookie文件
        with open(cookie_file, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
        
        logging.info(f"📥 开始加载 {len(cookies_data)} 个卖家cookie...")
        
        # 清除现有cookie
        driver.delete_all_cookies()
        
        # 逐个添加cookie
        successful_cookies = 0
        
        for cookie in cookies_data:
            try:
                cookie_dict = {
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie.get('domain', '.kuajingmaihuo.com'),
                    'path': cookie.get('path', '/'),
                }
                
                if cookie.get('secure', False):
                    cookie_dict['secure'] = True
                
                if cookie.get('httpOnly', False):
                    cookie_dict['httpOnly'] = True
                
                driver.add_cookie(cookie_dict)
                successful_cookies += 1
                
            except Exception as e:
                logging.warning(f"⚠️ 添加cookie失败: {cookie['name']} - {str(e)}")
        
        logging.info(f"📊 卖家Cookie加载完成: 成功 {successful_cookies}")
        
        # 验证登录状态
        driver.refresh()
        time.sleep(3)
        
        current_url = driver.current_url
        if "login" not in current_url.lower():
            logging.info("✅ 卖家后台登录状态验证成功")
            return True
        else:
            logging.warning("⚠️ 卖家后台登录状态验证失败")
            return False
            
    except Exception as e:
        logging.error(f"❌ 加载卖家cookie失败: {str(e)}")
        return False

def load_temu_cookies(driver):
    """加载Temu的cookie"""
    try:
        cookie_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        
        if not os.path.exists(cookie_file):
            logging.warning("⚠️ Temu Cookie文件不存在，跳过加载")
            return True
        
        # 访问Temu主页
        logging.info("🌐 访问Temu主页以设置cookie上下文...")
        driver.get("https://www.temu.com")
        time.sleep(3)
        
        # 加载cookie文件
        with open(cookie_file, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
        
        logging.info(f"📥 开始加载 {len(cookies_data)} 个Temu cookie...")
        
        # 清除现有cookie
        driver.delete_all_cookies()
        
        # 逐个添加cookie
        successful_cookies = 0
        
        for cookie in cookies_data:
            try:
                cookie_dict = {
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie.get('domain', '.temu.com'),
                    'path': cookie.get('path', '/'),
                }
                
                if cookie.get('secure', False):
                    cookie_dict['secure'] = True
                
                if cookie.get('httpOnly', False):
                    cookie_dict['httpOnly'] = True
                
                driver.add_cookie(cookie_dict)
                successful_cookies += 1
                
            except Exception as e:
                logging.warning(f"⚠️ 添加cookie失败: {cookie['name']} - {str(e)}")
        
        logging.info(f"📊 Temu Cookie加载完成: 成功 {successful_cookies}")
        return True
            
    except Exception as e:
        logging.error(f"❌ 加载Temu cookie失败: {str(e)}")
        return False

def smart_mall_access_strategy(driver, own_mall_id, target_mall_id):
    """智能商店访问策略"""
    try:
        logging.info("\n🎯 开始执行智能商店访问策略...")
        
        # 步骤1: 先访问自己的店铺建立信任
        own_mall_url = f"https://www.temu.com/mall.html?mall_id={own_mall_id}"
        logging.info(f"🏠 步骤1: 访问自己的店铺 - {own_mall_id}")
        logging.info(f"🔗 URL: {own_mall_url}")
        
        driver.get(own_mall_url)
        time.sleep(8)
        
        # 检查自己店铺的访问状态
        current_url = driver.current_url
        page_title = driver.title
        
        logging.info(f"📄 自己店铺页面标题: {page_title}")
        logging.info(f"🔗 自己店铺当前URL: {current_url}")
        
        if "no_access" in current_url or "没有互联网连接" in page_title:
            logging.warning("⚠️ 连自己的店铺都无法访问，可能需要重新登录")
            return False, None, None
        else:
            logging.info("✅ 自己的店铺访问成功！")
        
        # 模拟真实浏览行为
        logging.info("🎭 在自己店铺中模拟真实浏览行为...")
        
        # 随机滚动页面
        for i in range(3):
            scroll_position = random.randint(300, 800) * (i + 1)
            driver.execute_script(f"window.scrollTo(0, {scroll_position});")
            time.sleep(random.uniform(2, 4))
        
        # 等待一段时间，模拟真实用户浏览
        time.sleep(random.uniform(5, 8))
        
        # 步骤2: 访问目标店铺
        target_mall_url = f"https://www.temu.com/mall.html?mall_id={target_mall_id}"
        logging.info(f"\n🎯 步骤2: 访问目标店铺 - {target_mall_id}")
        logging.info(f"🔗 URL: {target_mall_url}")
        
        driver.get(target_mall_url)
        time.sleep(10)
        
        # 检查目标店铺的访问状态
        target_current_url = driver.current_url
        target_page_title = driver.title
        target_page_source = driver.page_source
        
        logging.info(f"📄 目标店铺页面标题: {target_page_title}")
        logging.info(f"🔗 目标店铺当前URL: {target_current_url}")
        logging.info(f"📊 目标店铺页面内容长度: {len(target_page_source)} 字符")
        
        # 判断是否成功访问
        if "no_access" not in target_current_url and "没有互联网连接" not in target_page_title:
            logging.info("🎉 目标店铺访问成功！")
            
            # 保存成功页面
            success_file = f"smart_access_success_{target_mall_id}_{datetime.now().strftime('%H%M%S')}.html"
            with open(success_file, 'w', encoding='utf-8') as f:
                f.write(target_page_source)
            logging.info(f"📁 成功页面已保存: {success_file}")
            
            return True, target_page_source, success_file
        else:
            logging.warning("❌ 目标店铺仍然被重定向")
            
            # 保存失败页面用于分析
            fail_file = f"smart_access_fail_{target_mall_id}_{datetime.now().strftime('%H%M%S')}.html"
            with open(fail_file, 'w', encoding='utf-8') as f:
                f.write(target_page_source)
            logging.info(f"📁 失败页面已保存: {fail_file}")
            
            return False, target_page_source, fail_file
        
    except Exception as e:
        logging.error(f"❌ 智能商店访问策略执行失败: {str(e)}")
        return False, None, None

def main():
    """智能商店访问主函数"""
    logging.info("="*60)
    logging.info("开始运行智能商店访问策略")
    logging.info(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"日志文件: {log_file}")
    logging.info("="*60)
    
    driver = None
    try:
        # 设置浏览器
        driver = setup_smart_browser()
        if not driver:
            logging.error("❌ 浏览器设置失败")
            return False
        
        # 加载卖家后台cookie
        if not load_seller_cookies(driver):
            logging.error("❌ 卖家cookie加载失败")
            return False
        
        # 加载Temu cookie
        load_temu_cookies(driver)
        
        # 执行智能访问策略
        own_mall_id = "634418217337839"  # 您自己的店铺ID
        target_mall_id = "634418212233370"  # 目标店铺ID
        
        success, page_source, file_path = smart_mall_access_strategy(driver, own_mall_id, target_mall_id)
        
        if success:
            print("\n🎉 智能商店访问策略成功！")
            print(f"✅ 成功访问目标店铺: {target_mall_id}")
            print(f"📁 页面已保存: {file_path}")
            return True
        else:
            print("\n⚠️ 智能商店访问策略失败")
            print("目标店铺仍然被阻止访问")
            return False
        
    except Exception as e:
        logging.error(f"程序执行过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if driver:
            try:
                input("\n⏳ 按回车键关闭浏览器...")
                driver.quit()
                logging.info("🔒 浏览器已关闭")
            except:
                pass

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 智能商店访问测试成功！")
        else:
            print("\n⚠️ 智能商店访问测试失败")
    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
