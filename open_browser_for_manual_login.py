#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打开浏览器供用户手动登录
"""

import os
import time
import json
import logging
from datetime import datetime

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"manual_browser_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"浏览器打开日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def open_browser_for_manual_operation():
    """打开浏览器供用户手动操作"""
    try:
        logging.info("🔧 正在为您打开浏览器...")
        
        # 尝试导入undetected_chromedriver
        try:
            import undetected_chromedriver as uc
        except ImportError:
            print("❌ 缺少undetected_chromedriver")
            print("请安装: pip install undetected-chromedriver")
            return None, None
        
        # 创建用户数据目录
        user_data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "chrome_user_data")
        
        # 配置Chrome选项 - 可见窗口
        options = uc.ChromeOptions()
        options.add_argument(f'--user-data-dir={user_data_dir}')
        options.add_argument('--profile-directory=Default')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1200,800')
        # 不使用headless，让用户可以看到和操作浏览器
        
        # 创建浏览器
        driver = uc.Chrome(options=options, version_main=137)
        
        print("\n" + "="*60)
        print("🌐 浏览器已为您打开！")
        print("="*60)
        print("📋 请按照以下步骤操作：")
        print()
        print("1️⃣ 在浏览器地址栏输入: https://seller.kuajingmaihuo.com/login")
        print("2️⃣ 完成登录（输入用户名密码等）")
        print("3️⃣ 登录成功后，找到并点击 'HealthMuse' 店铺")
        print("4️⃣ 点击 '访问我的店铺' 按钮")
        print("5️⃣ 确保成功进入店铺页面")
        print()
        print("✅ 完成上述步骤后，回到这里按 Enter 继续...")
        print("="*60)
        
        # 先访问一个基础页面
        driver.get("https://www.temu.com")
        time.sleep(2)
        
        # 等待用户完成手动操作
        input("请完成上述步骤后按 Enter 继续...")
        
        # 获取当前页面的URL，确认用户操作状态
        current_url = driver.current_url
        logging.info(f"当前页面URL: {current_url}")
        
        # 获取所有cookie
        cookies = driver.get_cookies()
        logging.info(f"📥 获取到 {len(cookies)} 个cookie")
        
        # 显示关键cookie信息
        key_cookies = ['AccessToken', 'user_uin', '_bee', 'api_uid', 'verifyAuthToken']
        found_cookies = []
        for cookie in cookies:
            if cookie['name'] in key_cookies:
                found_cookies.append(cookie['name'])
        
        if found_cookies:
            logging.info(f"🔑 找到关键cookie: {found_cookies}")
        else:
            logging.warning("⚠️ 未找到关键cookie，可能需要重新登录")
        
        # 转换为字符串格式
        cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies if cookie.get('name') and cookie.get('value')])
        
        # 获取User-Agent
        user_agent = driver.execute_script("return navigator.userAgent;")
        
        # 保存cookie到文件（备用）
        cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "manual_cookies.json")
        with open(cookie_file_path, 'w', encoding='utf-8') as f:
            json.dump(cookies, f, ensure_ascii=False, indent=2)
        logging.info(f"📁 Cookie已保存到: {cookie_file_path}")
        
        print("\n" + "="*60)
        print("✅ Cookie获取完成！")
        print(f"📊 获取到 {len(cookies)} 个cookie")
        print(f"🔑 关键cookie: {found_cookies}")
        print(f"📁 已保存到: manual_cookies.json")
        print("="*60)
        
        # 询问是否关闭浏览器
        print("\n🤔 是否关闭浏览器？")
        print("y - 关闭浏览器")
        print("n - 保持浏览器打开（推荐，方便后续操作）")
        choice = input("请选择 (y/n): ").lower().strip()
        
        if choice == 'y':
            driver.quit()
            logging.info("🔒 浏览器已关闭")
            return cookie_str, user_agent
        else:
            logging.info("🌐 浏览器保持打开状态，您可以继续使用")
            print("💡 提示: 浏览器将保持打开，您可以继续在其中操作")
            return cookie_str, user_agent
        
    except Exception as e:
        logging.error(f"❌ 打开浏览器时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

def test_cookies_with_api(cookie_str, user_agent):
    """测试获取的cookie是否可用"""
    if not cookie_str:
        print("❌ 没有可用的cookie进行测试")
        return False
    
    try:
        import requests
        
        print("\n🧪 测试cookie是否可用...")
        
        # 构建headers
        headers = {
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
            "content-type": "application/json;charset=UTF-8",
            "origin": "https://www.temu.com",
            "referer": "https://www.temu.com/",
            "user-agent": user_agent,
            "Cookie": cookie_str
        }
        
        # 测试API调用
        api_url = "https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList"
        payload = {
            "mallId": "634418212233370",
            "mainGoodsIds": ["1"],
            "source_page_sn": "10013",
            "mall_id": "634418212233370",
            "main_goods_ids": ["1"],
            "filter_items": "",
            "page_number": 1,
            "page_size": 20,
            "list_id": "r7oe7gyw0vd5xo2z2qja2",
            "scene_code": "mall_rule",
            "page_sn": 10040,
            "page_el_sn": 201265,
            "source": 10018
        }
        
        response = requests.post(api_url, headers=headers, json=payload, timeout=10)
        
        print(f"📊 API测试结果: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Cookie测试成功！API调用正常")
            return True
        elif response.status_code == 403:
            print("⚠️ Cookie可能需要特殊处理（403错误）")
            print(f"响应内容: {response.text[:200]}...")
            return False
        else:
            print(f"⚠️ API调用返回状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 测试cookie时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始打开浏览器供您手动登录...")
    
    # 打开浏览器并等待用户操作
    cookie_str, user_agent = open_browser_for_manual_operation()
    
    if cookie_str and user_agent:
        # 测试cookie是否可用
        test_cookies_with_api(cookie_str, user_agent)
        
        print("\n🎉 操作完成！")
        print("💡 您现在可以:")
        print("1. 使用获取的cookie运行主程序")
        print("2. 或者继续在浏览器中操作")
    else:
        print("❌ 未能成功获取cookie")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
