#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器自动化版本的爬虫程序
使用真实浏览器访问页面并捕获网络请求
"""

import json
import time
import logging
import random
import os
from datetime import datetime
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, WebDriverException
    from webdriver_manager.chrome import ChromeDriverManager
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    logging.warning("Selenium未安装，请运行: pip install selenium webdriver-manager")

import requests

# 导入主程序的函数
from a import (
    get_hk_time,
    get_headers,
    save_shop_data,
    save_product_data,
    process_shop_data,
    process_products,
    fetch_shop_ids
)

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"temu_crawler_browser_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logging.info(f"浏览器自动化日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

def setup_chrome_driver():
    """设置Chrome浏览器驱动"""
    if not SELENIUM_AVAILABLE:
        logging.error("❌ Selenium未安装，请运行: pip install selenium webdriver-manager")
        return None, None

    chrome_options = Options()

    # 基本设置
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')

    # 反检测设置
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    # 设置用户代理
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')

    # 加载cookie文件路径
    cookie_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")

    try:
        # 清除缓存并重新下载正确版本的ChromeDriver
        import shutil
        import os
        cache_dir = os.path.expanduser("~/.wdm")
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                logging.info("已清除ChromeDriver缓存")
            except:
                pass

        # 使用webdriver-manager自动管理ChromeDriver
        from webdriver_manager.chrome import ChromeDriverManager
        from webdriver_manager.core.utils import ChromeType

        # 强制下载64位版本
        service = Service(ChromeDriverManager(chrome_type=ChromeType.GOOGLE).install())
        driver = webdriver.Chrome(service=service, options=chrome_options)

        # 执行反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        logging.info("✅ Chrome浏览器驱动初始化成功")
        return driver, cookie_file
    except Exception as e:
        logging.error(f"❌ Chrome浏览器驱动初始化失败: {str(e)}")
        logging.error("请确保已安装Chrome浏览器和相关依赖")
        logging.error("安装命令: pip install selenium webdriver-manager")
        return None, None

def load_cookies_to_browser(driver, cookie_file):
    """将cookie加载到浏览器中"""
    try:
        # 先访问域名以设置cookie
        driver.get("https://www.temu.com")
        time.sleep(2)
        
        if os.path.exists(cookie_file):
            with open(cookie_file, 'r', encoding='utf-8') as f:
                cookies = json.load(f)
            
            for cookie in cookies:
                try:
                    # 只添加必要的cookie字段
                    cookie_dict = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie.get('domain', '.temu.com'),
                        'path': cookie.get('path', '/'),
                        'secure': cookie.get('secure', True)
                    }
                    driver.add_cookie(cookie_dict)
                except Exception as e:
                    logging.warning(f"添加cookie失败: {cookie['name']} - {str(e)}")
            
            logging.info(f"✅ 成功加载 {len(cookies)} 个cookie到浏览器")
            return True
        else:
            logging.warning("⚠️ Cookie文件不存在")
            return False
    except Exception as e:
        logging.error(f"❌ 加载cookie到浏览器失败: {str(e)}")
        return False

def capture_network_requests(driver):
    """捕获网络请求（简化版本）"""
    # 这里我们使用JavaScript来监听网络请求
    script = """
    window.capturedRequests = [];
    
    // 重写XMLHttpRequest
    const originalXHR = window.XMLHttpRequest;
    window.XMLHttpRequest = function() {
        const xhr = new originalXHR();
        const originalOpen = xhr.open;
        const originalSend = xhr.send;
        
        xhr.open = function(method, url, ...args) {
            this._method = method;
            this._url = url;
            return originalOpen.apply(this, arguments);
        };
        
        xhr.send = function(data) {
            this.addEventListener('load', function() {
                if (this._url && this._url.includes('mallInfoWithGoodsList')) {
                    window.capturedRequests.push({
                        method: this._method,
                        url: this._url,
                        status: this.status,
                        response: this.responseText,
                        requestData: data
                    });
                }
            });
            return originalSend.apply(this, arguments);
        };
        
        return xhr;
    };
    
    // 重写fetch
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
        return originalFetch(url, options).then(response => {
            if (url.includes('mallInfoWithGoodsList')) {
                response.clone().text().then(text => {
                    window.capturedRequests.push({
                        method: options.method || 'GET',
                        url: url,
                        status: response.status,
                        response: text,
                        requestData: options.body
                    });
                });
            }
            return response;
        });
    };
    """
    
    driver.execute_script(script)
    logging.info("✅ 网络请求监听器已设置")

def visit_mall_page_with_browser(driver, mall_id):
    """使用浏览器访问商店页面"""
    mall_url = f"https://www.temu.com/mall.html?mall_id={mall_id}"
    
    try:
        logging.info(f"🌐 浏览器访问商店页面: {mall_url}")
        
        # 访问页面
        driver.get(mall_url)
        
        # 等待页面加载
        wait = WebDriverWait(driver, 30)
        
        # 等待页面主要内容加载
        try:
            # 等待商店名称或商品列表加载
            wait.until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            logging.info("✅ 页面加载完成")
        except TimeoutException:
            logging.warning("⚠️ 页面加载超时，但继续执行")
        
        # 模拟用户行为
        logging.info("🎭 模拟用户浏览行为...")
        
        # 随机滚动页面
        for i in range(3):
            scroll_position = random.randint(300, 800) * (i + 1)
            driver.execute_script(f"window.scrollTo(0, {scroll_position});")
            time.sleep(random.uniform(1, 3))
        
        # 等待API请求触发
        time.sleep(random.uniform(5, 10))
        
        # 获取捕获的网络请求
        captured_requests = driver.execute_script("return window.capturedRequests || [];")
        
        logging.info(f"📡 捕获到 {len(captured_requests)} 个相关网络请求")
        
        return captured_requests
        
    except Exception as e:
        logging.error(f"❌ 浏览器访问页面失败: {str(e)}")
        return []

def extract_data_from_captured_requests(captured_requests):
    """从捕获的请求中提取数据"""
    for request in captured_requests:
        try:
            if request['status'] == 200 and request['response']:
                response_data = json.loads(request['response'])
                logging.info("✅ 成功从浏览器请求中提取数据")
                return response_data
        except Exception as e:
            logging.warning(f"⚠️ 解析请求数据失败: {str(e)}")
    
    return None

def fetch_temu_shop_data_browser(mall_id):
    """使用浏览器自动化获取商店数据"""
    driver = None
    try:
        # 设置浏览器
        driver, cookie_file = setup_chrome_driver()
        if not driver:
            return None
        
        # 加载cookie
        if not load_cookies_to_browser(driver, cookie_file):
            logging.warning("⚠️ Cookie加载失败，继续尝试")
        
        # 设置网络请求监听
        capture_network_requests(driver)
        
        # 访问商店页面
        captured_requests = visit_mall_page_with_browser(driver, mall_id)
        
        # 提取数据
        if captured_requests:
            data = extract_data_from_captured_requests(captured_requests)
            if data:
                return data
        
        # 如果没有捕获到请求，尝试直接从页面提取数据
        logging.info("🔍 尝试从页面直接提取数据...")
        
        # 这里可以添加页面数据提取逻辑
        # 例如：解析页面中的JSON数据
        page_source = driver.page_source
        
        # 查找页面中的JSON数据
        import re
        json_pattern = r'window\.__INITIAL_STATE__\s*=\s*({.*?});'
        match = re.search(json_pattern, page_source, re.DOTALL)
        
        if match:
            try:
                initial_state = json.loads(match.group(1))
                logging.info("✅ 从页面初始状态提取数据成功")
                return initial_state
            except:
                pass
        
        logging.warning("⚠️ 未能从浏览器获取数据")
        return None
        
    except Exception as e:
        logging.error(f"❌ 浏览器自动化过程中发生异常: {str(e)}")
        return None
    finally:
        if driver:
            try:
                driver.quit()
                logging.info("🔒 浏览器已关闭")
            except:
                pass

def main():
    """浏览器自动化主函数"""
    logging.info("="*60)
    logging.info("开始运行Temu数据抓取 - 浏览器自动化模式")
    logging.info(f"时间: {get_hk_time()}")
    logging.info(f"日志文件: {log_file}")
    logging.info("="*60)
    
    logging.info("🤖 浏览器自动化流程：")
    logging.info("1. 启动真实Chrome浏览器")
    logging.info("2. 加载cookie到浏览器")
    logging.info("3. 访问商店页面")
    logging.info("4. 模拟用户浏览行为")
    logging.info("5. 捕获网络请求数据")
    logging.info("6. 提取和保存数据")
    
    # 测试单个商店
    test_mall_id = "634418212233370"
    
    logging.info(f"\n🎯 测试商店: {test_mall_id}")
    
    result = fetch_temu_shop_data_browser(test_mall_id)
    
    if result:
        logging.info("🎉 浏览器自动化成功获取数据！")
        
        # 处理数据
        shop_data = process_shop_data(result)
        if shop_data:
            if save_shop_data(shop_data):
                logging.info("✅ 商店数据保存成功")
        
        products = process_products(result, test_mall_id)
        if products:
            saved_count = 0
            for product in products:
                if save_product_data(product):
                    saved_count += 1
            logging.info(f"✅ 成功保存 {saved_count}/{len(products)} 个产品")
        
        return True
    else:
        logging.warning("⚠️ 浏览器自动化未能获取数据")
        logging.info("可能的原因：")
        logging.info("1. 页面结构发生变化")
        logging.info("2. 需要安装ChromeDriver")
        logging.info("3. 网络连接问题")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 浏览器自动化测试成功！")
            print("可以考虑将此方法集成到主程序中")
        else:
            print("\n⚠️ 浏览器自动化测试失败")
            print("请检查ChromeDriver是否正确安装")
    except Exception as e:
        logging.error(f"程序执行过程中发生未捕获的异常: {str(e)}")
        import traceback
        traceback.print_exc()
