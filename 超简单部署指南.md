# 🚀 超简单部署指南 - 只需2步！

## ✨ 特性
- ✅ Cookie已硬编码到worker.js中
- ✅ 无需设置任何环境变量
- ✅ 只维护一个文件
- ✅ 固定爬取两个店铺：634418221704901、634418221321199

## 📦 部署步骤（只需2步）

### 第一步：安装并登录
```powershell
npm install -g wrangler
wrangler login
```

### 第二步：部署
```powershell
wrangler deploy
```

就这么简单！🎉

## 🌐 使用方法

部署成功后，您会得到一个URL，例如：
`https://temu-scraper-simple.your-subdomain.workers.dev`

### 可用端点：

1. **主页面** (推荐)
   ```
   https://your-worker-url.workers.dev/
   ```
   - 美观的HTML页面展示两个店铺数据
   - 包含商店信息和产品列表
   - 支持手机和电脑查看

2. **API数据**
   ```
   https://your-worker-url.workers.dev/api
   ```
   - 返回JSON格式数据

3. **健康检查**
   ```
   https://your-worker-url.workers.dev/health
   ```

4. **Cookie测试**
   ```
   https://your-worker-url.workers.dev/test-cookie
   ```

## 🔄 更新Cookie

当Cookie过期时，只需要：

1. 打开 `worker.js` 文件
2. 找到 `HARDCODED_COOKIES` 数组
3. 替换为新的Cookie数据
4. 运行 `wrangler deploy`

## 📝 Cookie更新示例

在worker.js中找到这部分：
```javascript
const HARDCODED_COOKIES = [
  {"name": "region", "value": "37"},
  {"name": "verifyAuthToken", "value": "新的token值"},
  // ... 其他cookie
];
```

替换为新的Cookie值即可。

## 🎯 测试目的

这个版本专门用于测试：
- Cloudflare Workers是否会被Temu检测为爬虫
- 全球IP池是否能减少风控
- 执行性能和稳定性

## ⚡ 优势

1. **极简部署**: 只需2个命令
2. **单文件维护**: 所有配置都在worker.js中
3. **无环境变量**: 不需要设置任何secrets
4. **即时生效**: 部署后立即可用
5. **易于更新**: 修改文件后重新部署即可

## 🔧 故障排除

### 问题1: 部署失败
- 确保已安装Node.js
- 确保已登录Cloudflare: `wrangler whoami`

### 问题2: 页面显示错误
- 可能是Cookie过期，更新Cookie后重新部署

### 问题3: 执行超时
- 这是正常现象，免费版有时间限制
- 多刷新几次或升级到付费版

## 📊 监控

查看实时日志：
```powershell
wrangler tail
```

这就是最简单的部署方式！只需要维护一个worker.js文件，所有配置都在里面。
