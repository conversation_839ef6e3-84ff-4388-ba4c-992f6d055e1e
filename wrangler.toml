name = "temu-scraper-simple"
main = "worker.js"
compatibility_date = "2024-01-15"
compatibility_flags = ["nodejs_compat"]

# Cookie已硬编码到worker.js中，无需环境变量

# 可选：KV命名空间（如果需要存储数据）
# [[kv_namespaces]]
# binding = "TEMU_DATA"
# id = "your-kv-namespace-id"
# preview_id = "your-preview-kv-namespace-id"

# 可选：Cron触发器（定时执行）
# [triggers]
# crons = ["0 */6 * * *"]  # 每6小时执行一次

# 资源限制
[limits]
cpu_ms = 30000  # 30秒CPU时间限制（付费版）
