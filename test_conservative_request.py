#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保守的API请求测试
等待足够长的时间来避免429错误
"""

import requests
import json
import os
import time
from datetime import datetime

def load_cookies_from_file():
    """从cookie.json文件加载cookie信息"""
    try:
        cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        print(f"尝试从 {cookie_file_path} 加载cookie")
        
        if not os.path.exists(cookie_file_path):
            print(f"警告: cookie文件不存在: {cookie_file_path}")
            return None
            
        with open(cookie_file_path, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
            
        # 将cookie列表转换为字符串格式
        cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_data if cookie.get('name') and cookie.get('value')])
        print(f"成功加载cookie，包含 {len(cookies_data)} 个条目")
        return cookie_str
    except Exception as e:
        print(f"加载cookie文件时出错: {str(e)}")
        return None

def test_conservative_request():
    """保守的API请求测试"""
    print("="*60)
    print("保守的Temu API请求测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # 获取cookie
    cookies = load_cookies_from_file()
    if not cookies:
        print("❌ 无法获取cookie，测试失败")
        return False
    
    # 设置请求参数
    url = "https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList"
    mall_id = "634418212233370"  # 使用一个测试商店ID
    
    payload = {
        "mallId": mall_id,
        "mainGoodsIds": ["1"],
        "source_page_sn": "10013",
        "mall_id": mall_id,
        "main_goods_ids": ["1"],
        "filter_items": "",
        "page_number": 1,
        "page_size": 8,
        "list_id": "r7oe7gyw0vd5xo2z2qja2",
        "scene_code": "mall_rule",
        "page_sn": 10040,
        "page_el_sn": 201265,
        "source": 10018,
        "anti_content": "1"
    }
    
    headers = {
        "content-type": "application/json;charset=UTF-8",
        "Cookie": cookies,
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Referer": f"https://www.temu.com/mall/{mall_id}",
        "Origin": "https://www.temu.com"
    }
    
    print(f"请求URL: {url}")
    print(f"商店ID: {mall_id}")
    
    # 等待2分钟，让之前的请求限制过期
    print("\n等待2分钟，让请求限制过期...")
    for i in range(120, 0, -10):
        print(f"剩余等待时间: {i}秒", end="\r")
        time.sleep(10)
    print("\n等待完成，开始发送请求...")
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 请求成功！")
            try:
                result = response.json()
                print(f"响应数据结构: {list(result.keys()) if isinstance(result, dict) else type(result)}")
                
                # 检查是否有商店信息
                if "result" in result:
                    shop_info = result["result"]
                    if "mall_name" in shop_info:
                        print(f"商店名称: {shop_info['mall_name']}")
                    if "goods_sales_num" in shop_info:
                        print(f"商品销量: {shop_info['goods_sales_num']}")
                    
                    # 检查商品列表
                    if "data" in result["result"] and "goods_list" in result["result"]["data"]:
                        goods_list = result["result"]["data"]["goods_list"]
                        print(f"商品数量: {len(goods_list)}")
                        if goods_list:
                            first_product = goods_list[0]
                            print(f"第一个商品: {first_product.get('title', 'N/A')}")
                
                return True
            except Exception as e:
                print(f"解析响应JSON时出错: {str(e)}")
                print(f"响应内容: {response.text[:200]}...")
                return False
                
        elif response.status_code == 429:
            print("❌ 仍然是429错误：请求频率过高")
            print("建议：需要更长的等待时间或使用代理IP")
            return False
            
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

def main():
    """主函数"""
    success = test_conservative_request()
    
    if success:
        print("\n🎉 测试成功！现在可以运行主程序了")
        print("建议：使用更长的延迟时间来避免429错误")
    else:
        print("\n⚠️ 测试失败，建议:")
        print("1. 等待更长时间（可能需要几小时）")
        print("2. 使用代理IP")
        print("3. 联系网站管理员确认API使用权限")

if __name__ == "__main__":
    main()
