2025-04-09 14:48:49,686 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250409_144849.log
2025-04-09 14:48:49,686 [INFO] ==================================================
2025-04-09 14:48:49,686 [INFO] 开始运行Temu数据抓取
2025-04-09 14:48:49,725 [INFO] 时间: 2025-04-09 14:48:49
2025-04-09 14:48:49,726 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250409_144849.log
2025-04-09 14:48:49,726 [INFO] ==================================================
2025-04-09 14:48:49,726 [INFO] 
更新Temu Cookie...
2025-04-09 14:48:49,726 [INFO] 加载初始cookie文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json
2025-04-09 14:48:49,726 [INFO] 成功加载初始cookie，共19个
2025-04-09 14:48:49,726 [INFO] 开始访问Temu主页...
2025-04-09 14:48:51,155 [INFO] Temu主页访问状态码: 200
2025-04-09 14:48:51,155 [INFO] 成功更新cookie文件，共19个cookie
2025-04-09 14:48:51,161 [INFO] Cookie更新成功，继续数据抓取
2025-04-09 14:48:51,161 [INFO] 
获取商店ID列表...
2025-04-09 14:48:51,161 [INFO] 请求商店ID列表: http://172.25.165.28:8055/items/shop_data
2025-04-09 14:48:51,218 [INFO] 获取商店ID状态码: 200
2025-04-09 14:48:51,219 [INFO] 获取到的商店数据: {
  "data": [
    {
      "mall_id": "634418213326762"
    },
    {
      "mall_id": "634418210750968"
    },
    {
      "mall_id": "634418214390089"
    }
  ]
}...
2025-04-09 14:48:51,220 [INFO] 获取到的全部mall_id数量: 3
2025-04-09 14:48:51,220 [INFO] 去重后的mall_id数量: 3
2025-04-09 14:48:51,220 [INFO] 去除了 0 个重复的mall_id
2025-04-09 14:48:51,220 [INFO] 最终使用的商店ID列表: ['634418213326762', '634418214390089', '634418210750968']
2025-04-09 14:48:51,220 [INFO] 
==================================================
2025-04-09 14:48:51,221 [INFO] 正在处理商店 1/3: mall_id 634418213326762
2025-04-09 14:48:51,221 [INFO] ==================================================
2025-04-09 14:48:51,221 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418213326762
2025-04-09 14:48:51,221 [INFO] 请求payload: {"mallId": "634418213326762", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418213326762", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-04-09 14:48:51,221 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-04-09 14:48:51,229 [INFO] 成功加载cookie，包含 19 个条目
2025-04-09 14:48:51,230 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-04-09 14:48:52,955 [INFO] Temu API状态码: 200
2025-04-09 14:48:52,955 [INFO] Temu返回数据结构: ['success', 'error_code', 'errorCode', 'result']
2025-04-09 14:48:52,955 [INFO] 成功获取到8件商品，尽管page_size设置为1
2025-04-09 14:48:52,955 [INFO] 找到'result'键，包含店铺数据
2025-04-09 14:48:52,955 [INFO] 成功获取到8件商品
2025-04-09 14:48:52,955 [INFO] 处理商店数据...
2025-04-09 14:48:52,967 [INFO] 处理后的商店数据: {"update_time": "2025-04-09 14:48:52", "mall_id": "634418213326762", "mall_name": "Double Accessories Studio", "mall_logo": "https://img.kwcdn.com/supplier-public-tag/1fa3205448/d184f556-a99e-421d-b931-6269f98650f8_300x300.png", "goods_sales_num": 71000, "goods_num": 329, "review_num": 1090}
2025-04-09 14:48:52,967 [INFO] 保存商店数据: mall_id=634418213326762
2025-04-09 14:48:53,092 [INFO] 成功更新商店数据: 634418213326762
2025-04-09 14:48:53,157 [ERROR] 记录商店变更日志时发生异常: unsupported operand type(s) for -: 'int' and 'NoneType'
2025-04-09 14:48:53,166 [INFO] 处理产品数据...
2025-04-09 14:48:53,166 [INFO] 找到8个产品
2025-04-09 14:48:53,168 [INFO] 价格转换: 546美分 -> 5.46美元
2025-04-09 14:48:53,168 [INFO] 处理后的产品数据 1: {"update_time": "2025-04-09 14:48:53", "mall_id": "634418213326762", "goods_id": "601099716475969", "title": "订制蛋糕装饰、洗礼蛋糕装饰、上帝祝福蛋糕装饰、压克力蛋糕挂件、十字架蛋糕装饰、木质蛋糕装饰、金色、适合婚礼、派对、生日等场景", "image_url": "https://img.kwcdn.com/product/open/8e5a3c573b1b4c909f055e59c22bffa0-goods.jpeg", "sales_num": 738, "price": 5.46, "comment": 13}
2025-04-09 14:48:53,168 [INFO] 价格转换: 394美分 -> 3.94美元
2025-04-09 14:48:53,168 [INFO] 处理后的产品数据 2: {"update_time": "2025-04-09 14:48:53", "mall_id": "634418213326762", "goods_id": "601099718821759", "title": "定制金色镜子 席位卡 豪华婚礼席位卡 金色镜子 标签 激光切割名字 亚克力婚礼卡片 婚礼装饰 桌子 定制活动", "image_url": "https://img.kwcdn.com/product/open/7985b164015c406685339cf77bfede00-goods.jpeg", "sales_num": 446, "price": 3.94, "comment": 0}
2025-04-09 14:48:53,169 [INFO] 价格转换: 592美分 -> 5.92美元
2025-04-09 14:48:53,169 [INFO] 价格转换: 5107美分 -> 51.07美元
2025-04-09 14:48:53,170 [INFO] 价格转换: 532美分 -> 5.32美元
2025-04-09 14:48:53,170 [INFO] 价格转换: 468美分 -> 4.68美元
2025-04-09 14:48:53,170 [INFO] 价格转换: 475美分 -> 4.75美元
2025-04-09 14:48:53,171 [INFO] 价格转换: 486美分 -> 4.86美元
2025-04-09 14:48:53,171 [INFO] 为mall_id 634418213326762找到8个产品
2025-04-09 14:48:53,171 [INFO] 正在保存产品 1/8 (goods_id: 601099716475969)
2025-04-09 14:48:53,171 [INFO] 保存/更新产品数据: goods_id=601099716475969
2025-04-09 14:48:53,354 [INFO] 成功创建新产品数据: 601099716475969
2025-04-09 14:48:53,493 [INFO] 成功记录产品变更日志: product_data_id=784, 订单增长: 0, 评论增长: 0
2025-04-09 14:48:53,702 [INFO] 正在保存产品 2/8 (goods_id: 601099718821759)
2025-04-09 14:48:53,702 [INFO] 保存/更新产品数据: goods_id=601099718821759
2025-04-09 14:48:53,906 [INFO] 成功创建新产品数据: 601099718821759
2025-04-09 14:48:54,027 [INFO] 成功记录产品变更日志: product_data_id=785, 订单增长: 0, 评论增长: 0
2025-04-09 14:48:54,232 [INFO] 正在保存产品 3/8 (goods_id: 601099798454912)
2025-04-09 14:48:54,232 [INFO] 保存/更新产品数据: goods_id=601099798454912
2025-04-09 14:48:54,439 [INFO] 成功创建新产品数据: 601099798454912
2025-04-09 14:48:54,566 [INFO] 成功记录产品变更日志: product_data_id=786, 订单增长: 0, 评论增长: 0
2025-04-09 14:48:54,773 [INFO] 正在保存产品 4/8 (goods_id: 601099658086716)
2025-04-09 14:48:54,773 [INFO] 保存/更新产品数据: goods_id=601099658086716
2025-04-09 14:48:54,958 [INFO] 成功创建新产品数据: 601099658086716
2025-04-09 14:48:55,090 [INFO] 成功记录产品变更日志: product_data_id=787, 订单增长: 0, 评论增长: 0
2025-04-09 14:48:55,294 [INFO] 正在保存产品 5/8 (goods_id: 601099666917951)
2025-04-09 14:48:55,295 [INFO] 保存/更新产品数据: goods_id=601099666917951
2025-04-09 14:48:55,495 [INFO] 成功创建新产品数据: 601099666917951
2025-04-09 14:48:55,660 [INFO] 成功记录产品变更日志: product_data_id=788, 订单增长: 0, 评论增长: 0
2025-04-09 14:48:55,871 [INFO] 正在保存产品 6/8 (goods_id: 601099646014540)
2025-04-09 14:48:55,871 [INFO] 保存/更新产品数据: goods_id=601099646014540
2025-04-09 14:48:56,056 [INFO] 成功创建新产品数据: 601099646014540
2025-04-09 14:48:56,212 [INFO] 成功记录产品变更日志: product_data_id=789, 订单增长: 0, 评论增长: 0
2025-04-09 14:48:56,415 [INFO] 正在保存产品 7/8 (goods_id: 601099681670198)
2025-04-09 14:48:56,415 [INFO] 保存/更新产品数据: goods_id=601099681670198
2025-04-09 14:48:56,613 [INFO] 成功创建新产品数据: 601099681670198
2025-04-09 14:48:56,784 [INFO] 成功记录产品变更日志: product_data_id=790, 订单增长: 0, 评论增长: 0
2025-04-09 14:48:56,992 [INFO] 正在保存产品 8/8 (goods_id: 601099578673098)
2025-04-09 14:48:56,992 [INFO] 保存/更新产品数据: goods_id=601099578673098
2025-04-09 14:48:57,197 [INFO] 成功创建新产品数据: 601099578673098
2025-04-09 14:48:57,372 [INFO] 成功记录产品变更日志: product_data_id=791, 订单增长: 0, 评论增长: 0
2025-04-09 14:48:57,572 [INFO] 商店 634418213326762 处理完成: 成功保存 8/8 个产品
2025-04-09 14:48:58,573 [INFO] 
==================================================
2025-04-09 14:48:58,573 [INFO] 正在处理商店 2/3: mall_id 634418214390089
2025-04-09 14:48:58,573 [INFO] ==================================================
2025-04-09 14:48:58,573 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418214390089
2025-04-09 14:48:58,573 [INFO] 请求payload: {"mallId": "634418214390089", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418214390089", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-04-09 14:48:58,573 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-04-09 14:48:58,573 [INFO] 成功加载cookie，包含 19 个条目
2025-04-09 14:48:58,573 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-04-09 14:49:00,924 [INFO] Temu API状态码: 200
2025-04-09 14:49:00,925 [INFO] Temu返回数据结构: ['success', 'error_code', 'errorCode', 'result']
2025-04-09 14:49:00,925 [INFO] 成功获取到8件商品，尽管page_size设置为1
2025-04-09 14:49:00,925 [INFO] 找到'result'键，包含店铺数据
2025-04-09 14:49:00,925 [INFO] 成功获取到8件商品
2025-04-09 14:49:00,928 [INFO] 处理商店数据...
2025-04-09 14:49:00,928 [INFO] 处理后的商店数据: {"update_time": "2025-04-09 14:49:00", "mall_id": "634418214390089", "mall_name": "Dream Custom Studio", "mall_logo": "https://img.kwcdn.com/supplier-public-tag/1f19f102eb8/19efd7ef-2d75-4046-af12-060aca8f0a3a_300x300.jpeg", "goods_sales_num": 45000, "goods_num": 142, "review_num": 720}
2025-04-09 14:49:00,928 [INFO] 保存商店数据: mall_id=634418214390089
2025-04-09 14:49:01,056 [INFO] 成功更新商店数据: 634418214390089
2025-04-09 14:49:01,111 [ERROR] 记录商店变更日志时发生异常: unsupported operand type(s) for -: 'int' and 'NoneType'
2025-04-09 14:49:01,114 [INFO] 处理产品数据...
2025-04-09 14:49:01,114 [INFO] 找到8个产品
2025-04-09 14:49:01,115 [INFO] 价格转换: 583美分 -> 5.83美元
2025-04-09 14:49:01,115 [INFO] 处理后的产品数据 1: {"update_time": "2025-04-09 14:49:01", "mall_id": "634418214390089", "goods_id": "601099577918790", "title": "定制亚克力钥匙圈，时尚女孩设计 - 个人化姓名标签，生日、毕业礼物和旅行必需品的理想选择，可爱钥匙扣", "image_url": "https://img.kwcdn.com/product/open/2024-05-14/1715680112315-2d719dbe936149c296187cf6189a07bf-goods.jpeg", "sales_num": 850, "price": 5.83, "comment": 12}
2025-04-09 14:49:01,115 [INFO] 价格转换: 506美分 -> 5.06美元
2025-04-09 14:49:01,121 [INFO] 处理后的产品数据 2: {"update_time": "2025-04-09 14:49:01", "mall_id": "634418214390089", "goods_id": "601100565893926", "title": "定制BFF文字礼物，带照片拼贴的亚克力钥匙扣，父亲节和母亲节纪念品最佳选择", "image_url": "https://img.kwcdn.com/product/open/8723f6fc550d4f37ae3468ce1a87b7e8-goods.jpeg", "sales_num": 0, "price": 5.06, "comment": 0}
2025-04-09 14:49:01,121 [INFO] 价格转换: 597美分 -> 5.97美元
2025-04-09 14:49:01,122 [INFO] 价格转换: 495美分 -> 4.95美元
2025-04-09 14:49:01,122 [INFO] 价格转换: 437美分 -> 4.37美元
2025-04-09 14:49:01,122 [INFO] 价格转换: 523美分 -> 5.23美元
2025-04-09 14:49:01,123 [INFO] 价格转换: 581美分 -> 5.81美元
2025-04-09 14:49:01,123 [INFO] 价格转换: 506美分 -> 5.06美元
2025-04-09 14:49:01,123 [INFO] 为mall_id 634418214390089找到8个产品
2025-04-09 14:49:01,123 [INFO] 正在保存产品 1/8 (goods_id: 601099577918790)
2025-04-09 14:49:01,123 [INFO] 保存/更新产品数据: goods_id=601099577918790
2025-04-09 14:49:01,304 [INFO] 成功创建新产品数据: 601099577918790
2025-04-09 14:49:01,420 [INFO] 成功记录产品变更日志: product_data_id=792, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:01,629 [INFO] 正在保存产品 2/8 (goods_id: 601100565893926)
2025-04-09 14:49:01,629 [INFO] 保存/更新产品数据: goods_id=601100565893926
2025-04-09 14:49:01,836 [INFO] 成功创建新产品数据: 601100565893926
2025-04-09 14:49:01,952 [INFO] 成功记录产品变更日志: product_data_id=793, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:02,154 [INFO] 正在保存产品 3/8 (goods_id: 601099636408430)
2025-04-09 14:49:02,154 [INFO] 保存/更新产品数据: goods_id=601099636408430
2025-04-09 14:49:02,345 [INFO] 成功创建新产品数据: 601099636408430
2025-04-09 14:49:02,485 [INFO] 成功记录产品变更日志: product_data_id=794, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:02,688 [INFO] 正在保存产品 4/8 (goods_id: 601099726445248)
2025-04-09 14:49:02,688 [INFO] 保存/更新产品数据: goods_id=601099726445248
2025-04-09 14:49:02,906 [INFO] 成功创建新产品数据: 601099726445248
2025-04-09 14:49:03,040 [INFO] 成功记录产品变更日志: product_data_id=795, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:03,251 [INFO] 正在保存产品 5/8 (goods_id: 601100567594778)
2025-04-09 14:49:03,251 [INFO] 保存/更新产品数据: goods_id=601100567594778
2025-04-09 14:49:03,442 [INFO] 成功创建新产品数据: 601100567594778
2025-04-09 14:49:03,559 [INFO] 成功记录产品变更日志: product_data_id=796, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:03,761 [INFO] 正在保存产品 6/8 (goods_id: 601100566244067)
2025-04-09 14:49:03,761 [INFO] 保存/更新产品数据: goods_id=601100566244067
2025-04-09 14:49:03,954 [INFO] 成功创建新产品数据: 601100566244067
2025-04-09 14:49:04,077 [INFO] 成功记录产品变更日志: product_data_id=797, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:04,283 [INFO] 正在保存产品 7/8 (goods_id: 601099768649984)
2025-04-09 14:49:04,283 [INFO] 保存/更新产品数据: goods_id=601099768649984
2025-04-09 14:49:04,462 [INFO] 成功创建新产品数据: 601099768649984
2025-04-09 14:49:04,593 [INFO] 成功记录产品变更日志: product_data_id=798, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:04,797 [INFO] 正在保存产品 8/8 (goods_id: 601100560379277)
2025-04-09 14:49:04,797 [INFO] 保存/更新产品数据: goods_id=601100560379277
2025-04-09 14:49:04,983 [INFO] 成功创建新产品数据: 601100560379277
2025-04-09 14:49:05,093 [INFO] 成功记录产品变更日志: product_data_id=799, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:05,298 [INFO] 商店 634418214390089 处理完成: 成功保存 8/8 个产品
2025-04-09 14:49:06,299 [INFO] 
==================================================
2025-04-09 14:49:06,300 [INFO] 正在处理商店 3/3: mall_id 634418210750968
2025-04-09 14:49:06,300 [INFO] ==================================================
2025-04-09 14:49:06,300 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418210750968
2025-04-09 14:49:06,300 [INFO] 请求payload: {"mallId": "634418210750968", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418210750968", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-04-09 14:49:06,300 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-04-09 14:49:06,300 [INFO] 成功加载cookie，包含 19 个条目
2025-04-09 14:49:06,300 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-04-09 14:49:08,257 [INFO] Temu API状态码: 200
2025-04-09 14:49:08,257 [INFO] Temu返回数据结构: ['success', 'error_code', 'errorCode', 'result']
2025-04-09 14:49:08,260 [INFO] 成功获取到8件商品，尽管page_size设置为1
2025-04-09 14:49:08,260 [INFO] 找到'result'键，包含店铺数据
2025-04-09 14:49:08,260 [INFO] 成功获取到8件商品
2025-04-09 14:49:08,262 [INFO] 处理商店数据...
2025-04-09 14:49:08,262 [INFO] 处理后的商店数据: {"update_time": "2025-04-09 14:49:08", "mall_id": "634418210750968", "mall_name": "WoyiCo", "mall_logo": "https://img.kwcdn.com/supplier-public-tag/1e78ea0e3d/03f3539d-8028-4bc1-8c46-206a25daa5a4_300x300.png", "goods_sales_num": 100000, "goods_num": 209, "review_num": 515}
2025-04-09 14:49:08,262 [INFO] 保存商店数据: mall_id=634418210750968
2025-04-09 14:49:08,384 [INFO] 成功更新商店数据: 634418210750968
2025-04-09 14:49:08,450 [ERROR] 记录商店变更日志时发生异常: unsupported operand type(s) for -: 'int' and 'NoneType'
2025-04-09 14:49:08,454 [INFO] 处理产品数据...
2025-04-09 14:49:08,454 [INFO] 找到8个产品
2025-04-09 14:49:08,454 [INFO] 价格转换: 532美分 -> 5.32美元
2025-04-09 14:49:08,455 [INFO] 处理后的产品数据 1: {"update_time": "2025-04-09 14:49:08", "mall_id": "634418210750968", "goods_id": "601099573790697", "title": "定制热粉色亚克力名字项链，带金色点缀 - 可爱经典字体设计，适合日常穿着或送礼", "image_url": "https://img.kwcdn.com/product/fancy/28924059-35ee-49b6-8bd8-18100500a950.jpg", "sales_num": 7, "price": 5.32, "comment": 93}
2025-04-09 14:49:08,455 [INFO] 价格转换: 502美分 -> 5.02美元
2025-04-09 14:49:08,455 [INFO] 处理后的产品数据 2: {"update_time": "2025-04-09 14:49:08", "mall_id": "634418210750968", "goods_id": "601099680825855", "title": "【定制品】1件定制婚礼宾客姓名标签个性化英文字母座位标记亚克力名称标签定制礼物标签婚礼装扮配饰独特的庆祝礼物", "image_url": "https://img.kwcdn.com/product/fancy/f349e993-9ab2-4a50-a99e-afedf8c5e85c.jpg", "sales_num": 399, "price": 5.02, "comment": 1}
2025-04-09 14:49:08,456 [INFO] 价格转换: 1152美分 -> 11.52美元
2025-04-09 14:49:08,462 [INFO] 价格转换: 480美分 -> 4.8美元
2025-04-09 14:49:08,462 [INFO] 价格转换: 686美分 -> 6.86美元
2025-04-09 14:49:08,463 [INFO] 价格转换: 507美分 -> 5.07美元
2025-04-09 14:49:08,463 [INFO] 价格转换: 946美分 -> 9.46美元
2025-04-09 14:49:08,464 [INFO] 价格转换: 1330美分 -> 13.3美元
2025-04-09 14:49:08,465 [INFO] 为mall_id 634418210750968找到8个产品
2025-04-09 14:49:08,465 [INFO] 正在保存产品 1/8 (goods_id: 601099573790697)
2025-04-09 14:49:08,465 [INFO] 保存/更新产品数据: goods_id=601099573790697
2025-04-09 14:49:08,675 [INFO] 成功创建新产品数据: 601099573790697
2025-04-09 14:49:08,807 [INFO] 成功记录产品变更日志: product_data_id=800, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:09,009 [INFO] 正在保存产品 2/8 (goods_id: 601099680825855)
2025-04-09 14:49:09,009 [INFO] 保存/更新产品数据: goods_id=601099680825855
2025-04-09 14:49:09,190 [INFO] 成功创建新产品数据: 601099680825855
2025-04-09 14:49:09,367 [INFO] 成功记录产品变更日志: product_data_id=801, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:09,569 [INFO] 正在保存产品 3/8 (goods_id: 601099620411403)
2025-04-09 14:49:09,569 [INFO] 保存/更新产品数据: goods_id=601099620411403
2025-04-09 14:49:09,727 [INFO] 成功创建新产品数据: 601099620411403
2025-04-09 14:49:09,848 [INFO] 成功记录产品变更日志: product_data_id=802, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:10,059 [INFO] 正在保存产品 4/8 (goods_id: 601099549323448)
2025-04-09 14:49:10,059 [INFO] 保存/更新产品数据: goods_id=601099549323448
2025-04-09 14:49:10,283 [INFO] 成功创建新产品数据: 601099549323448
2025-04-09 14:49:10,415 [INFO] 成功记录产品变更日志: product_data_id=803, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:10,624 [INFO] 正在保存产品 5/8 (goods_id: 601099585661327)
2025-04-09 14:49:10,624 [INFO] 保存/更新产品数据: goods_id=601099585661327
2025-04-09 14:49:10,803 [INFO] 成功创建新产品数据: 601099585661327
2025-04-09 14:49:10,926 [INFO] 成功记录产品变更日志: product_data_id=804, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:11,130 [INFO] 正在保存产品 6/8 (goods_id: 601099573691636)
2025-04-09 14:49:11,130 [INFO] 保存/更新产品数据: goods_id=601099573691636
2025-04-09 14:49:11,316 [INFO] 成功创建新产品数据: 601099573691636
2025-04-09 14:49:11,439 [INFO] 成功记录产品变更日志: product_data_id=805, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:11,644 [INFO] 正在保存产品 7/8 (goods_id: 601099623136212)
2025-04-09 14:49:11,644 [INFO] 保存/更新产品数据: goods_id=601099623136212
2025-04-09 14:49:11,840 [INFO] 成功创建新产品数据: 601099623136212
2025-04-09 14:49:11,967 [INFO] 成功记录产品变更日志: product_data_id=806, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:12,169 [INFO] 正在保存产品 8/8 (goods_id: 601099627217746)
2025-04-09 14:49:12,169 [INFO] 保存/更新产品数据: goods_id=601099627217746
2025-04-09 14:49:12,376 [INFO] 成功创建新产品数据: 601099627217746
2025-04-09 14:49:12,496 [INFO] 成功记录产品变更日志: product_data_id=807, 订单增长: 0, 评论增长: 0
2025-04-09 14:49:12,697 [INFO] 商店 634418210750968 处理完成: 成功保存 8/8 个产品
2025-04-09 14:49:13,698 [INFO] 
==================================================
2025-04-09 14:49:13,698 [INFO] Temu数据抓取汇总
2025-04-09 14:49:13,698 [INFO] 处理时间: 2025-04-09 14:49:13
2025-04-09 14:49:13,698 [INFO] 处理商店总数: 3
2025-04-09 14:49:13,698 [INFO] 成功保存商店数: 3
2025-04-09 14:49:13,698 [INFO] 处理产品总数: 24
2025-04-09 14:49:13,698 [INFO] 成功保存产品数: 24
2025-04-09 14:49:13,698 [INFO] ==================================================
