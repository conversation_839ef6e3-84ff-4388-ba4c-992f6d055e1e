测试写入权限2025-06-19 11:16:46,861 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\test.log
2025-06-19 11:16:46,861 [INFO] ==================================================
2025-06-19 11:16:46,862 [INFO] 开始测试抓取商店数据: mall_id=634418212233370
2025-06-19 11:16:46,862 [INFO] ==================================================
2025-06-19 11:16:46,862 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418212233370
2025-06-19 11:16:46,862 [INFO] 请求payload: {"mallId": "634418212233370", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418212233370", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-06-19 11:16:46,863 [INFO] 随机延迟 2.26 秒
2025-06-19 11:16:49,125 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-06-19 11:16:49,125 [INFO] 成功加载cookie，包含 19 个条目
2025-06-19 11:16:49,125 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-06-19 11:16:49,707 [INFO] Temu API状态码: 429
2025-06-19 11:16:49,707 [ERROR] 获取mall_id为634418212233370的Temu数据时出错: 429
2025-06-19 11:16:49,707 [ERROR] 返回内容: {"error_code":406008,"error_msg":""}...
2025-06-19 11:16:49,713 [ERROR] 数据抓取失败!
2025-06-19 11:16:49,714 [INFO] ==================================================
2025-06-19 11:16:49,714 [INFO] 测试完成
2025-06-19 11:16:49,714 [INFO] ==================================================
2025-06-19 11:18:32,069 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\test.log
2025-06-19 11:18:32,069 [INFO] ==================================================
2025-06-19 11:18:32,069 [INFO] 开始测试抓取商店数据: mall_id=634418212233370
2025-06-19 11:18:32,069 [INFO] ==================================================
2025-06-19 11:18:32,070 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418212233370
2025-06-19 11:18:32,070 [INFO] 请求payload: {"mallId": "634418212233370", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418212233370", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-06-19 11:18:32,070 [INFO] 随机延迟 2.34 秒
2025-06-19 11:18:34,407 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-06-19 11:18:34,407 [INFO] 成功加载cookie，包含 20 个条目
2025-06-19 11:18:34,407 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-06-19 11:18:35,146 [INFO] Temu API状态码: 200
2025-06-19 11:18:35,147 [INFO] Temu返回数据结构: ['success', 'error_code', 'errorCode', 'result']
2025-06-19 11:18:35,147 [INFO] 成功获取到8件商品
2025-06-19 11:18:35,147 [INFO] 找到'result'键，包含店铺数据
2025-06-19 11:18:35,147 [INFO] 找到店铺信息: Personalizedforyou
2025-06-19 11:18:35,147 [INFO] 店铺数据摘要: {"mall_id": "634418212233370", "mall_name": "Personalizedforyou", "goods_sales_num": "15万", "goods_num": "226"}
2025-06-19 11:18:35,147 [INFO] 产品 1: {"goods_id": 601099713017646, "title": "个性化姓名项链，不锈钢名字首饰，带菲加罗链的定制礼物，量身定制的圣诞礼物。"}
2025-06-19 11:18:35,147 [INFO] 产品 2: {"goods_id": 601099556648080, "title": "个性化阿拉伯名字项链，定制简约风格书法手写激光切割名字牌项链，女性定制珠宝礼物，开斋节和斋月的礼物"}
2025-06-19 11:18:35,148 [INFO] 产品 3: {"goods_id": 601099552506738, "title": "个性化生日花卉图案姓名吊坠项链，母亲节礼物，情人节珠宝礼物"}
2025-06-19 11:18:35,159 [INFO] 完整数据已保存到 shop_data_634418212233370.json
2025-06-19 11:18:35,160 [INFO] 数据抓取成功!
2025-06-19 11:18:35,160 [INFO] ==================================================
2025-06-19 11:18:35,160 [INFO] 测试完成
2025-06-19 11:18:35,160 [INFO] ==================================================
