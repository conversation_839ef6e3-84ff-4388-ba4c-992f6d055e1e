<html lang="zh-Hans" data-theme="t3" translate="no"><head><script>"use strict";!(function(){try{var t=navigator.userAgent;/\s(temu|[a-z]h{2})_android_version\//i.test(t)&&Object.defineProperty(navigator,"userAgent",{value:t,writable:!1,configurable:!1})}catch(t){setTimeout(function(){throw t})}})();</script><script>
            window.__NPM_PACKAGE_CONFIG__ = {"localeSwitch":{"crossDrGray":{"salt":"crossDr","bucketCount":100,"defaultModel":"A","modelKeyArr":["A","B"],"models":{"A":{"whiteList":[],"blackList":[],"buckets":100},"B":{"whiteList":[],"blackList":[],"buckets":100}}}},"localInfo":{"reportDeviceInfo":true},"i18n":{"sumerReportRate":0.1}};
            window.__CDN_IMG__ = {"retryLimit":3,"backupDomainConfigMap":{"img.kwcdn.com":{"img-1.kwcdn.com":50,"img-2.kwcdn.com":50},"aimg.kwcdn.com":{"aimg-1.kwcdn.com":50,"aimg-2.kwcdn.com":50}},"retryHostConfig":{"static.kwcdn.com":["static.kwcdn.com","static-2.kwcdn.com","static-1.kwcdn.com","static.temucdn.com"],"img.kwcdn.com":["img.kwcdn.com","img-1.kwcdn.com","img-2.kwcdn.com","img.temucdn.com"],"aimg.kwcdn.com":["aimg.kwcdn.com","aimg-2.kwcdn.com","aimg-1.kwcdn.com","aimg.temucdn.com"],"rewimg-eu.kwcdn.com":["rewimg-eu.kwcdn.com","rewimg-eu-2.kwcdn.com","rewimg-eu-1.kwcdn.com","rewimg-eu-3.kwcdn.com"],"avatar-eu.kwcdn.com":["avatar-eu.kwcdn.com","avatar-eu-2.kwcdn.com","avatar-eu-1.kwcdn.com","avatar-eu-3.kwcdn.com"],"img-eu.kwcdn.com":["img-eu.kwcdn.com","img-eu-2.kwcdn.com","img-eu-1.kwcdn.com","img-eu-3.kwcdn.com"],"rewimg.kwcdn.com":["rewimg.kwcdn.com","rewimg-2.kwcdn.com","rewimg-1.kwcdn.com","rewimg-3.kwcdn.com"],"avatar.kwcdn.com":["avatar.kwcdn.com","avatar-2.kwcdn.com","avatar-1.kwcdn.com","avatar-3.kwcdn.com"],"commimg.kwcdn.com":["commimg.kwcdn.com","commimg-2.kwcdn.com","commimg-1.kwcdn.com","commimg-3.kwcdn.com"]},"sampleRate":0.01};
            window.__PRIVACY_CONFIG__ = {"openRegions":[210,76,69,98,141,186,13,20,163,162,191,192,167,79,53,90,96,54,68,180,181,64,108,122,52,32,50,113,114,151,91,5,203],"upaCtrlRegions":{"nrpt_211":[211,164,83,219,236,37,185],"nrpt":[211,164,83,219,236,37,185]},"sizeLimit":{"cookie":{"privacy_setting_detail":350}},"whiteReportSampleRate":0.01,"cookiePromptAuthCloseTime":5000,"setStorageDelayTime":5000,"closeLeoConfig":{"salt":"bgcp22628","bucketCount":100,"defaultModel":"A","modelKeyArr":["A","B","C"],"models":{"A":{"whiteList":["CmyieWUNPmCDSAClZHKiAg=="],"blackList":[],"buckets":0},"B":{"whiteList":["CnCGqGUKmP5aPgBUA+K1Ag==","Cmw+AWUMV/O50QBRBBJnAg==","Cmw+AWUMPKS50QBRA/xDAg==","Cm0QZGURT0CQtABSBC0EAg=="],"blackList":[],"buckets":100},"C":{"whiteList":["Cm1Kq2RnIGKS4gBRaHmIAg==","Cm0C+WRjeppEDgBPBuuAAg==","CmzS0GURh4uvoABiL3LtAg==","Cm3GomQj2u+8CwFBgYDHAg=="],"blackList":[],"buckets":100}}},"storageNonWhiteBlockGray":{"salt":"storageBlock","bucketCount":100,"defaultModel":"A","modelKeyArr":["A","B"],"models":{"A":{"whiteList":[],"blackList":[],"buckets":100},"B":{"whiteList":[],"blackList":[],"buckets":100}},"fullTimezone":["Asia/Shanghai"]},"delayImprTime":1500,"cookies":{"privacy_setting_np":1,"privacy_setting_detail":1,"region":1,"language":1,"language_locale":1,"currency":1,"timezone":1,"AccessToken":1,"isLogin":1,"user_uin":1,"_hal_tag":1,"account_history_list":1,"_bee":1,"_nano_fp":1,"_order_token":1,"visitor_token":1,"visitor_token_new":1,"visitor_login_ts":1,"isVisitor":1,"forterToken":1,"privacy_setting":1,"_u_pa":1,"dilx":1,"njrpl":1,"hfsc":1,"api_uid":1,"ETag":1,"install_token":1,"_titan_c":1,"__tclr":1,"g_state":1,"verifyAuthToken":1,"fblo_1779658199047189":1,"ftr_blst_1h":1,"klarna-shopping-browser-session-id":1,"ppjssdk.refreshToken":1,"_device_tag":1,"webp":2,"_ttc":{"type":3,"subType":["firstPAds",["ggAds","tblAds","obAds","idAds","opAds","pmAds"]]},"__cf_bm":1,"TS0132de8b":1,"cf_chl_rc_ni":1,"JSESSIONID":1,"cf_clearance":1,"TS017145d4":1,"cf_chl_rc_i":1,"cf_chl_XXXX":1,"ftr_ncd":1,"BIGipServerCentinel-Prod-Web-MethodDataCollectorWeb.app~Centinel-Prod-Web-MethodDataCollectorWeb_pool":1,"__cfruid":1,"cf_chl_rc_m":1,"TS016f9639":1,"WMONID":1,"img_sup":1,"chat_guest_access_token":1,"cookies.js":1},"removeList":["wcs_bt","_fwb","shipping_city","address_snapshot_id","sc_cache_valid","_ga","_ga_R8YHFZCMMX","gtm_logger_session","ppjssdk.codeVerifier","cookietest"],"sessionStorage":{"whiteList":["goods_ids_need_update","chunkTimeoutReload","prevURL","b64ffd8feffe7a6a","cart_pos","WISHLIST_DEL_GOODIDS","bgnb_all_return:scroll_to_list_limit","BGAdultConfirmNotification","x-session-id","has_confirm_stay_in_current_account","order_refresh_list","order_detail_show_from_orders","return_history_token","charge_detail_token","aftersale_list_token","order_receipt_token","refund_detail_token","bg_mail_token","payment_detail_token","emailCheckForbidden_op","express_list_token","express_token","express_slow_token","windowInnerHeight","orderpay_paypal_h5_session_id","3ds_callback_h5_checkout_id","TransferMarketQuery","CASHAPP_PARAMS","mobile-download-ui-style-height","mobile-download-ui-html","bg-cui-top-tab-html","bg-cui-top-tab-version","bg_3ds_channel_trans_id","bg_3ds_biz_type","bg_3ds_success_jump_url","bg_3ds_processing_jump_url","bg_3ds_fail_jump_url","bg_3ds_is_app","bg_3ds_parent_order_sn_list","bg_3ds_tade_pay_sn","pay_app_id","source_page_sn","3ds_error_payload","3ds_pay_source","bg_3ds_trade_pay_sn","3ds_skip_process_error","bg_3ds_order_cancel_jump_url","bgt_3ds_quick_callback_from_cart","bgt_3ds_pay_callback_pre_auth","bgt_3ds_tracking_params","web_pay_3ds_auth_params_used_map","bg_order_checkout_snapshot","pc_order_checkout_page_state","shoppingCartGoodsListStr","ErrorPageNotFirstRender","disabled-popup-ids","page-is-refresh","GUEST_LOGIN_POPUP_MAP","VISTOR_ORDER_TOKEN","VISTOR_ORDER_MIDDLE_TOKEN","order_reviews_token","CartGoodsAniSession","CartGoodsAniLocal","need_add_to_wish","sign_out_without_google_prompt","home_back_top_type","__LOCAL_TEST_LANGUAGE__","__LOCAL_MOCK_JSON__","pmm_trace_id_record","bgnb_buy_x_get_y_coupon:CLICKED_GOODS_IDS","bgnb_all_return:CLICKED_GOODS_IDS","bgnb_all_return:show_cart_limit","mobile-download-ui-version","save_address_success_scene","fallback_footerpay","fallback_staticconfig","afc_pick_goods_trans","forward_affiliate_detail","KOL_PERSONAl_BACK","affiliate_bind_paypal_page_scene","_x_return_mission_type","KOL_REMIND_MODAL","koc-refresh-on-regions-change","bgt_3ds_pay_channel","bgt_3ds_pc_trans_data","__sak","bgt_trade_info_context_snapshot","web_home_last_view",{"type":"regExp","value":"^jump_options_TP-[\\d-]{21}$"},{"type":"regExp","value":"^test-localStorage-\\d+"},"channel_merchant","chat_bind_info","web_pay_DoubleConfirmToast","bgt_3ds_quick_paypal_braintree","bg_3ds_common_jump_urls","cross_page_goods_fav","one_click_anchor_add_order","google.payments.api.storage.isreadytopay.result","AFFILIATE_ACTIVITY_BIND_PAYPAL_SCENE"],"removeList":["bg_3ds_market_query","BGGoodsDetailStatusNotification","forwardGoodsDetail","need_go_to_chat","goodsListAirTag","change_channel_prompt_flag","sku_num_promotion_shown_goods","8423ad807e13c1ca","comments_list",{"type":"regExp","value":"^bk_\\w{32}$"},"koc_close_update_session_key","goods_detail_recommend_list_last_view"]},"localStorage":{"whiteList":["__p_hit__","LOGIN_FROM_KEY_40001","shoppingCartFrequencyMap","LocalToLocalTipsKey","lastShowFreeGiftTime","sc_common_storage","refund_submit_success","search_view_storage_A10014","search_view_storage_A10015","cashRequestId","cashRequestExpiry","cart_toast_config","cart_tips_times","shoppingCartHasShowGuide","cart_event_card_time","cart_event_last_last_card_time","cart_icon_last_visit_time","sign_out_with_prompt","adg_card_exclusive_end_time","waiting_review_op_list","order_checkout_pay_success","3ds_checkout_id_pay_success_map","bgt_order_checkout_page_id","layer_ack_map","account_history_list","event_card_time","sc_g_n","SC_LOCAL_GUIDE_TYPE","cart_checkout_tip_time","cart_visit_times","jumping_machine_time","free_gift_invalid_notice","CART_TOAST_VO_1","sc_frequency_capping","last_prompt_time","ATTENDANCE_GIFT_BOX","one_click_pay_icon_click","one_click_show_tip","one_click_show_visit_count","one_click_pay_retain_pop","access_location_status","tp_browsing_history_close_time","ATTENDANCE_STICKY_COUPON","chat_promo_retain_dialog","goods","i18nextLng","i18next.translate.boo","h_dkrjl","h_wjrpl","_nano_fp","temu_monitor_build_version","VerifyAuthToken","temu_picks_sms_banner_last_show_timestamps","bgnb_all_return:IS_FIRST_ADD_CART","bgnb_all_return:1S_F1RST_CHECKOUT","mobile.bg.newbie.lowPriceItem.stressCountDownFirstStage","mobile.bg.newbie.lowPriceItem.LowerTheThresholdAni","mobile.bg.newbie.lowPriceItem.guideCheckoutDialog","chat_btn_tip_showed_times","bgnb_payment_data","bgnb_pay_success","bgnb_pay_processing","newbie.bgnb_order_checkout.failInfo","SystemRecyleStorageData","layout_tab","layout_message_view_tip","bgnb_pick_x_free_y_discontinued_toast","DAILY_GIFT_RETAIN_POPUP_FREQUENCY_LIMIT_KEY","KOL_GOODS_DIALOG_KEY","metricsAndPmmLogs","kuiper_dk19_login_refresh","__paypal_storage__","forterToken","tpc_toast_text_last_show_time","adbws_marker","ftr__gf","__test__","__test__localStorage__","test","add_order_retain_popup_loginTS","__simpleStorageInitTest","simpleStorage","google.payments.api.storage.isreadytopay.result","oauth_result","goods_item_delete_result","cookie_prompt_times","bgnb_free_items_daily_popups_day_limit_key","age_confirmation_notice","bgt_3ds_pay_callback_pre_auth","bgt_3ds_quick_callback_from_cart","show_intro_app_pop","renewal_token_time","localOrderTipsShowHistoryList","login_verify_result","amp-exp-$default_instance-WnH5az","SWITCH_RETAIN_ID",{"type":"regExp","value":"^eruda-.+"},{"type":"regExp","value":"^test-localStorage-\\d+"},{"type":"regExp","value":"^feh--[a-zA-Z0-9]{2,8}$"},{"type":"regExp","value":"^\\d{13}$"},"ShoppingCartLocalStorageKeyMap","order_union_data","FloatCartLocalStorageKeyMap","gth_ui","one_click_pay","newbie.bgnb_pay","cart_need_additional_time","last_visit","goods_coupon_tips_show_status","coupon_tips_show_frequency_control","becDeviceInfoRecorded","reported_i18n_keys","low_stock_tips_show_frequency_control"],"removeList":["BGGoodsDetailStatusNotification","search_behavior_storage_A10016","last_from_affiliate_guide","operate_cart_toast_type_time","HAT_TRICK_GROUPSN_STORAGE","HAT_TRICK_SELECTED_GOODS","new_pick_goods_ani_storage","ltvFishPond","goodsAddCartTipsList","goodsAddCartTipsTime","page_bottom_tip_display_status","wish_tips_localstorage_key","retain_benefit_popup","float_stay_tips","goodsListAirTag","order_checkout_isShowDelGoodTips","dk3_lead_download_end_time","revisit_localstorage_key_abc","user_banner_local_count_abc","piggy_share_sheet_ani","pending_card_orders_module_display_pc","pending_card_order_detail_module_display_pc","order_detail_pending_card_key","bgt_orders_pending_card_display","chat_topfaq_time_stamp","discount_guide_tips_time","guide_card_time","scroll_title_type_list_appeared","scroll_title_type_first_time","nf_farm","showScrollMore_hi2eYp","showScrollMore_express","new_user_middle_page","koc_coupon_banner_login_mark","top_bar_map","fun_widgets_map","LS_TURNTABLE_LOGIN","USER_POST_RECORDS","affililate_activity_scroller_showed","_bg_client_error_detail_","show_total_amount_tips","backOperation","lotery_turntable_ani_reward_show","lotery_turntable_login_mark","_lottery_res_title","save_chrome_goods_key_ds","__SUPER_SAVING_SEARCH_HISTORY_LIST__","END_TIME","retain_already_show_round","social_retain_already_show_round","coupon_retain_already_show_round","retain_already_show_type","stn_daily_cart","exclusive_deal","wishlist_float_show_time","show_urgeModel",{"type":"regExp","value":"^p[c-e]{2}_lottery_turn_table_start_timestamp$"},{"type":"regExp","value":"^p[c-e]{2}_login_source$"},"search_order_history","SEARCH_ORDER_KEY","delete_order_success","package_history_list","debug","tp_similar_card_closed_time","scLocalQueryTipsTime","goodsListSimilarSceneShow"]},"indexedDB":{"whiteConfig":{"page-info":{"entries":["_bee","api_uid","region","language","language_locale","currency","timezone"]},"sw-offline-log":{"error-logger":["/^\\d{13}$/"],"metrics":["/^\\d{13}$/"],"pmm-defined":["/^\\d{13}$/"]},"workbox-expiration":{"cache-entries":["/^(header|footer|full|skeleton)\\|.*$/","/^(header)\\|.*$/","/^(footer)\\|.*$/"]},"scdb":{"entries":["sc_cache"]}},"removeConfig":{"page-info":{"entries":["user_uin"]},"/^0(\\.\\d+)?$/":{"test":["/^\\d+$/"]}}},"shareChannel":{"Threads":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"facebook_feeds":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"facebook_group":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"facebook_h5_feeds":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"facebook_pc_feeds":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"facebook_story":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"facebook_wap":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"ins_chat":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"ins_feeds":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"ins_story":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"line_chat":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"line_h5_chat":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"messenger_chat":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"messenger_h5_chat":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"pinterest_pc_feeds":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"signal":{"regions":[12,29,37,45,59,83,100,128,159,160,164,197,211,219,236]},"signal_chat":{"regions":[12,29,37,45,59,83,100,128,159,160,164,197,211,219,236]},"signal_h5_chat":{"regions":[12,29,37,45,59,83,100,128,159,160,164,197,211,219,236]},"telegram":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"telegram_chat":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"telegram_h5_chat":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"threads_chat":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"twitter_chat":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"twitter_feeds":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"twitter_h5_feeds":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"twitter_pc_feeds":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"whatsapp_chat":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]},"whatsapp_h5_chat":{"regions":[3,4,5,9,10,12,13,14,16,20,26,29,31,32,35,37,42,45,49,50,52,53,54,57,59,61,64,68,69,75,76,77,79,83,84,89,90,91,96,97,98,100,101,102,105,106,108,112,113,114,116,119,120,122,126,128,130,132,134,135,141,144,147,151,152,153,156,159,160,162,163,164,165,167,174,175,180,181,184,185,186,187,191,192,197,201,203,208,209,210,211,212,213,217,219,236]}}};
            window.__SENSITIVE_PARAMS__ = [];
            window.__CMT_AMPLIFY_RATE__ = 1;
            window.__ERROR_SAMPLE_RATE__ = 1;
            window.__METRICS_DOUBLE_REPORT_GRAY__ = {"salt":"k8s_tklb","openPageSN":[10012,10005,10132,10032,10009,10037,10125],"domain":"thtka-us.temu.com","bucketCount":100,"defaultModel":"A","modelKeyArr":["A","B"],"models":{"A":{"whiteList":[],"blackList":[],"buckets":100},"B":{"whiteList":[],"blackList":[],"buckets":100}}};
            
            window.__REGION_CONFIG__ = {"us":{"id":211,"site":100,"dr":"udp","ldp":{"thtk":"thtka-us","pftk":"pftka-us","matk":"matka-us"}},"ca":{"id":37,"site":101,"dr":"us","ldp":"ca"},"au":{"id":12,"site":103,"dr":"us","ldp":"au"},"nz":{"id":144,"site":104,"dr":"us","ldp":"nz"},"uk":{"id":210,"site":102,"dr":"eu"},"de":{"id":76,"site":105,"dr":"eu"},"fr":{"id":69,"site":106,"dr":"eu"},"it":{"id":98,"site":107,"dr":"eu"},"nl":{"id":141,"site":108,"dr":"eu"},"es":{"id":186,"site":109,"dr":"eu"},"mx":{"id":128,"site":110,"dr":"us"},"at":{"id":13,"site":143,"dr":"eu"},"be":{"id":20,"site":142,"dr":"eu"},"pt":{"id":163,"site":111,"dr":"eu"},"pl":{"id":162,"site":112,"dr":"eu"},"se":{"id":191,"site":113,"dr":"eu"},"ch":{"id":192,"site":114,"dr":"eu"},"ro":{"id":167,"site":140,"dr":"eu"},"gr":{"id":79,"site":115,"dr":"eu"},"cz":{"id":53,"site":137,"dr":"eu"},"hu":{"id":90,"site":138,"dr":"eu"},"ie":{"id":96,"site":116,"dr":"eu"},"dk":{"id":54,"site":139,"dr":"eu"},"fi":{"id":68,"site":144,"dr":"eu"},"sk":{"id":180,"site":145,"dr":"eu"},"si":{"id":181,"site":147,"dr":"eu"},"ee":{"id":64,"site":149,"dr":"eu"},"lv":{"id":108,"site":150,"dr":"eu"},"mt":{"id":122,"site":151,"dr":"eu"},"cy":{"id":52,"site":117,"dr":"eu"},"bg":{"id":32,"site":141,"dr":"eu"},"hr":{"id":50,"site":146,"dr":"eu"},"lt":{"id":113,"site":148,"dr":"eu"},"lu":{"id":114,"site":152,"dr":"eu"},"jp":{"id":100,"site":118,"dr":"us","ldp":"jp"},"kr":{"id":185,"site":119,"dr":"us","ldp":"kr"},"sa":{"id":174,"site":120,"dr":"eu","ldp":"qa"},"ae":{"id":209,"site":122,"dr":"eu","ldp":"qa"},"kw":{"id":105,"site":123,"dr":"eu","ldp":"qa"},"no":{"id":151,"site":124,"dr":"eu"},"sg":{"id":179,"site":121,"dr":"us","ldp":"sg"},"cl":{"id":42,"site":125,"dr":"us","ldp":"br"},"br":{"id":29,"site":132,"dr":"us","ldp":"br"},"ph":{"id":160,"site":127,"dr":"us","ldp":"jp"},"il":{"id":97,"site":135,"dr":"eu"},"my":{"id":119,"site":126,"dr":"us","ldp":"sg"},"qa":{"id":165,"site":130,"dr":"eu","ldp":"qa"},"bh":{"id":16,"site":134,"dr":"eu","ldp":"qa"},"om":{"id":152,"site":133,"dr":"eu","ldp":"qa"},"tw":{"id":194,"site":128,"dr":"us","ldp":"jp"},"th":{"id":197,"site":129,"dr":"us","ldp":"sg"},"lb":{"id":109,"dr":"eu","ldp":"qa"},"jo":{"id":101,"site":131,"dr":"eu","ldp":"qa"},"za":{"id":184,"site":136,"dr":"eu","ldp":"za"},"rs":{"id":175,"site":153,"dr":"eu"},"md":{"id":130,"site":154,"dr":"eu"},"me":{"id":134,"site":155,"dr":"eu"},"is":{"id":91,"site":156,"dr":"eu"},"ad":{"id":5,"site":157,"dr":"eu"},"ba":{"id":26,"site":158,"dr":"eu"},"al":{"id":3,"site":159,"dr":"eu"},"mk":{"id":116,"site":192,"dr":"eu"},"xk":{"id":235,"site":161,"dr":"eu"},"kz":{"id":102,"site":162,"dr":"eu"},"co":{"id":45,"site":164,"dr":"us"},"az":{"id":14,"site":167,"dr":"eu"},"ua":{"id":208,"site":168,"dr":"eu"},"uy":{"id":212,"site":169,"dr":"us","ldp":"br"},"mu":{"id":126,"site":170,"dr":"eu","ldp":"za"},"pe":{"id":159,"site":163,"dr":"us","ldp":"br"},"ge":{"id":75,"site":165,"dr":"eu"},"am":{"id":10,"site":166,"dr":"eu"},"ma":{"id":135,"site":171,"dr":"eu"},"do":{"id":57,"site":172,"dr":"us"},"tr":{"id":203,"site":174,"dr":"eu"},"cr":{"id":49,"site":173,"dr":"us"},"gu":{"id":83,"site":100,"dr":"udp","ldp":{"thtk":"thtka-us","pftk":"pftka-us","matk":"matka-us"}},"mp":{"id":236,"site":100,"dr":"udp","ldp":{"thtk":"thtka-us","pftk":"pftka-us","matk":"matka-us"}},"pr":{"id":164,"site":100,"dr":"udp","ldp":{"thtk":"thtka-us","pftk":"pftka-us","matk":"matka-us"}},"vi":{"id":219,"site":100,"dr":"udp","ldp":{"thtk":"thtka-us","pftk":"pftka-us","matk":"matka-us"}},"pa":{"id":156,"site":176,"dr":"us"},"dz":{"id":4,"site":175,"dr":"eu"},"ec":{"id":59,"site":178,"dr":"us"},"tt":{"id":201,"site":179,"dr":"us"},"gt":{"id":84,"site":180,"dr":"us"},"uz":{"id":213,"site":181,"dr":"eu"},"ke":{"id":103,"site":177,"dr":"eu","ldp":"za"},"hn":{"id":89,"site":182,"dr":"us"},"sv":{"id":61,"site":183,"dr":"us"},"pk":{"id":153,"site":184,"dr":"eu"},"lk":{"id":187,"site":185,"dr":"eu"},"mn":{"id":132,"site":186,"dr":"eu"},"vn":{"id":217,"site":187,"dr":"us","ldp":"sg"},"bn":{"id":31,"site":188,"dr":"us","ldp":"sg"},"ar":{"id":9,"site":189,"dr":"us","ldp":"br"},"ng":{"id":147,"site":190,"dr":"eu","ldp":"za"},"kh":{"id":35,"site":191,"dr":"us","ldp":"sg"},"kg":{"id":106,"site":194,"dr":"eu"},"mv":{"id":120,"site":196,"dr":"eu","ldp":"qa"},"li":{"id":112,"site":197,"dr":"eu"},"gh":{"id":77,"site":198,"dr":"eu","ldp":"za"}};
            
            window.__DOC_SOURCE__ = "csrPath";
            window.__PageContext__ = {"pagePath":"bgn_no_access","buildId":"0kj61_1749130642065","modern":false};</script><script>"use strict";!(function e(){var t=window.__reportDefined__;t?t({groupId:100665,amplifyRate:1,tags:{event:"enter"}}):setTimeout(e)})();</script><script>"use strict";!(function(){var e,r,t,n,o,u,i,c,a,s,l;function d(e){var r=document.createElement("a");return r.href=e,r.host}function f(e,r){if(r)for(var t in r)e[t]=r[t]}function m(e,r,t){return f(e,r),t&&f(e,t),e}function v(){var o=[];return{add:function(e){o.push(e)},emit:function(e){for(var r=o.slice(),t=0,n=r.length;t<n;t++)r[t](e)},clear:function(){o=[]}}}function p(e){Promise.resolve().then(e)}function h(n){return new Promise(function(t){p(function e(){var r=document.querySelector('link[rel="stylesheet"][href="'.concat(n,'"]'));r?t(r):setTimeout(e)})})}function y(e,r,t){var n=new Error(r);n.errorCode=e,n.payload=m({message:r},t),n.type=501,setTimeout(function(){throw n},0)}function g(e,r){if(r[e])return e;for(var t in r)if(-1<r[t].indexOf(e))return t}function E(e){return/^(\d+(\.|$)){4}/.test(e)}function T(){var e,r,t;return i||(e=(null==(e=window.__CDN_IMG__)?void 0:e.retryHostConfig)||c,n||o||(r=e,t={},Object.keys(r).forEach(function(e){t[e]=r[e].filter(function(e){return!E(e)})}),e=t),i=e)}function _(e,r,t){var n=d(e),n=r.indexOf(n),n=r[(n+1)%r.length];return n?(r=e,e=n,n=t,(t=document.createElement("a")).href=r,E(t.host=e)?(t.protocol="http",t.search?t.search+="&host="+encodeURIComponent(n):t.search="host="+encodeURIComponent(n)):t.protocol="https",t.href):""}function L(e){return r=d(e),e=T(),(t=g(r,e))?e[t].filter(function(e){return e!==r}):[];var r,t}function w(e){return e.complete?e.naturalHeight<=1&&e.naturalWidth<=1?"errored":"loaded":"loading"}function S(e,r){return crypto.subtle.digest((function(e){switch(e){case"sha256":return"SHA-256";case"sha384":return"SHA-384";case"sha512":return"SHA-512";default:return"SHA-384"}})(r),e)}function k(i,c,a){var t;c&&a&&"fetch"in window&&(t=a.split("-")[0],fetch(c).then(function(n){var o,e;200===n.status&&(e=n,(r?e.text().then(function(e){return(new TextEncoder).encode(e).buffer}):e.arrayBuffer()).then(function(e){return S(o=e,r=t).then(function(e){e=window.btoa(String.fromCharCode.apply(null,new Uint8Array(e)));return"".concat(r,"-").concat(e)});var r}).then(function(e){var t,r;e!==a&&(r=o,r=(new TextDecoder).decode(r),o=null,t={el_type:i,resource:c,el_integrity:a,fetch_integrity:e,res_length:r.length,start_frag:r.slice(0,120),end_frag:r.slice(-120)},n.headers.forEach(function(e,r){t["header_".concat(r)]=e}),y(70200,"integrity not match",t))}).catch(function(){}))}).catch(function(){}))}function x(r,t){s||(s={resourceType:r,errorType:t},a.then(function(e){e({groupId:100665,amplifyRate:1,tags:{event:"error",resourceType:r,errorType:t}})}))}function P(e,r,t){var n,o,i;return 0===e.index?t(m({},e,{url:r.initialUrl}),r):(o=r.retryHosts.shift())?(n=r.initialUrl,o=o,(i=document.createElement("a")).href=n,i.host=o,n=i.href,r.retryHosts.length||(r=m({},r,{integrity:void 0})),t(m({},e,{url:n}),r)):"reachLimit"}function H(e,r){return P(e,r,C)}function C(e,r){var t,n=r.initialEl,r=r.integrity,o=e.index,i=e.url,c=e.emitErrorType,o=0===o&&n,a=o?n:(i=i,r=r,(t=document.createElement("link")).href=i,t.rel="stylesheet",r&&(t.integrity=r),t.crossOrigin="anonymous",t),u={options:e,el:a,state:"loading",timeoutOrErrored:!1,Timeout:v(),Error:v(),Load:v(),clear:function(){(function(e){e.removeEventListener("load",f),e.removeEventListener("error",d),clearTimeout(s)})(u.el),clearTimeout(s),u.Error.clear(),u.Load.clear(),clearTimeout(l)},cancel:function(){u.el!==n&&(u.el.href="",u.el.remove())}},s=0,l=0;function d(){clearTimeout(s),u.state="errored",u.Error.emit()}function f(){clearTimeout(s),u.state="loaded",u.Load.emit()}function m(e){e.addEventListener("load",f),e.addEventListener("error",d),s=setTimeout(function(){u.Timeout.emit()},5e3)}function h(e){var r=(function(e){if(!e.parentNode)return"errored";if(!e.sheet)return"loading";try{return 0<e.sheet.cssRules.length?"loaded":"errored"}catch(e){return"errored"}})(e);switch(r){case"errored":d();break;case"loading":m(e);break;case"loaded":f();break;default:}}return o?p(function(){h(a)}):l=setTimeout(function(){var e;m(e=a),n&&n.parentNode?n.insertAdjacentElement("afterend",e):document.head.appendChild(e)},"error"===c?100:0),u}function R(e,r){return P(e,r,j)}function j(e,r){n=e.url,r=r.integrity,(t=document.createElement("script")).src=n,t.crossOrigin="anonymous",r&&(t.integrity=r);var t,n,o=t,i={options:e,el:o,state:"loading",timeoutOrErrored:!1,Timeout:v(),Error:v(),Load:v(),clear:function(){(function(e){e.removeEventListener("load",s),e.removeEventListener("error",u),clearTimeout(c)})(i.el),i.Timeout.clear(),i.Error.clear(),i.Load.clear(),clearTimeout(a)},cancel:function(){o.remove()}},c=0,a=0;function u(){clearTimeout(c),i.state="errored",i.Error.emit(),i.el.remove()}function s(){clearTimeout(c),i.state="loaded",i.Load.emit()}function l(e){e.addEventListener("load",s),e.addEventListener("error",u),c=setTimeout(function(){i.Timeout.emit()},5e3)}return n="error"===e.emitErrorType?100:0,a=setTimeout(function(){var e;l(e=o),document.head.appendChild(e)},n),i}function N(r,t){var e;return 0===r.index?I(m({},r,{url:t.initialUrl}),t):t.groupHost?r.index>((null==(e=window.__CDN_IMG__)?void 0:e.retryLimit)||3)?0<t.initialUrl.indexOf("?")&&!t.alreadyTriedNoQuery?(t.alreadyTriedNoQuery=!0,n(t.initialUrl.split("?")[0])):"reachLimit":n(_(r.emitProcess.el.src,t.retryHosts,t.groupHost)):"reachLimit";function n(e){return e?I(m({},r,{url:e}),t):"reachLimit"}}function I(e,r){var t=r.initialEl,r=e.index,n=e.url,o=e.emitErrorType,i=0===r&&t,c=i?t:new Image,a={options:e,el:c,state:"loading",timeoutOrErrored:!1,Timeout:v(),Error:v(),Load:v(),clear:function(){(function(e){e.removeEventListener("load",f),e.removeEventListener("error",l),clearTimeout(u)})(a.el),a.Timeout.clear(),a.Error.clear(),a.Load.clear(),clearTimeout(s)},cancel:function(){c!==t&&(c.src="",c.remove())}},u=0,s=0;function l(){clearTimeout(u),a.state="errored",a.Error.emit()}function d(){clearTimeout(u),a.state="loaded",a.Load.emit()}function f(){("loaded"===w(c)?d:l)()}function m(e){e.addEventListener("load",f),e.addEventListener("error",l),u=setTimeout(function(){a.Timeout.emit()},7e3)}return i?p(function(){var e=c,r=w(e);switch(r){case"errored":l();break;case"loading":m(e);break;case"loaded":d();break;default:}}):s=setTimeout(function(){m(a.el),a.el.src=n},"error"===o?400*r:0),a}function U(t,n){var o=[],i={kind:"loading"},c=!1,a=v(),u=v(),e=v();function s(){e.clear(),u.clear(),a.clear(),o.forEach(function(e){return e.clear()})}function l(){o.every(function(e){return"errored"===e.state})&&c&&(i={kind:"errored"},e.emit(),s())}function d(e,r){e.timeoutOrErrored||(0===e.options.index&&a.emit(r),f({index:e.options.index+1,emitProcess:e,emitErrorType:r}),e.timeoutOrErrored=!0),"error"===r&&l()}function f(e){var r=n(e,t);"reachLimit"===r?(c=!0,l()):(r.Error.add(function(){d(r,"error")}),r.Timeout.add(function(){d(r,"timeout")}),r.Load.add(function(){var e;e=r,u.emit(e),i={kind:"loaded",ret:e},s()}),o.push(r))}return p(function(){f({index:0})}),{InitialError:a,FinalError:e,FinalLoad:u,clear:s,cancel:function(){"loaded"!==i.kind&&o.forEach(function(e){e.clear(),e.cancel()})},getProcs:function(){return o}}}function O(e){return e=e,(r=document.createElement("a")).href=e,r.pathname+r.search;var r}function b(t,i,c,a){return new Promise(function(e,r){var n=U({initialUrl:i,initialEl:a,integrity:c,retryHosts:L(i)},H),o="";n.InitialError.add(function(e){"firstScreen"===t&&x("css",e),e&&(o=e,y(u.css[o][0],"css_link_error",{resource:i,href:i,errorType:e}),c&&k("link",i,c))}),n.FinalError.add(function(){o&&y(u.css[o][1],"css_link_retry_fail",{resource:i,href:i,retryTimes:n.getProcs().length-1}),r()}),n.FinalLoad.add(function(r){var t=r.options.url;o&&y(u.css[o][2],"css_link_retry_succ",{resource:i,href:i,succHref:t,retryTimes:r.options.index}),n.getProcs().forEach(function(e){e!==r&&(e.el.href=t,e.el.remove())}),e(r.options.url)})})}function A(e){var r=O(e),t="firstScreen",n=h(e).then(function(e){return b(t,e.href,e.integrity,e)}),o={id:r,status:"loading",resourceType:"css",initiatorType:t,initiatorUrl:e,loadPromise:n};n.then(function(){o.status="loaded"}).catch(function(){o.status="errored"}),l[r]=o}function F(e){var o,i,c,r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",t=2<arguments.length&&void 0!==arguments[2]&&arguments[2],n=O(e),t=t?"firstScreen":"dynamicImport",r=(o=t,i=e,c=r,new Promise(function(r,e){var t=U({initialUrl:i,retryHosts:L(i),integrity:c},R),n="";t.InitialError.add(function(e){"firstScreen"===o&&x("js",e),e&&(n=e,y(u.js[n][0],"script_tag_error",{resource:i,src:i,errorType:e}),c&&k("script",i,c))}),t.FinalError.add(function(){n&&y(u.js[n][1],"script_tag_retry_fail",{resource:i,src:i,retryTimes:t.getProcs().length-1}),e()}),t.FinalLoad.add(function(e){n&&y(u.js[n][2],"script_tag_retry_succ",{resource:i,src:i,succSrc:e.options.url,retryTimes:e.options.index}),r(e.options.url)})})),a={id:n,status:"loading",resourceType:"js",initiatorType:t,initiatorUrl:e,loadPromise:r};return r.then(function(){a.status="loaded"}).catch(function(){a.status="errored"}),l[n]=a,r}"Promise"in window&&(e="__XRenderResourcesLoader__",window[e]||(r=/\s(temu|[a-z]h{2})_ios_version\//i.test(navigator.userAgent),t=/\s(temu|[a-z]h{2})_android_version\//i.test(navigator.userAgent),n=r||t,o=/iphone|ipad|ipod/i.test(navigator.userAgent),u={js:{error:[70013,70014,70015],timeout:[70017,70018,70019]},css:{error:[70003,70004,70005],timeout:[70006,70007,70008]}},i=null,c={},a=new Promise(function(t){!(function e(){var r=window.__reportDefined__;r?t(r):setTimeout(e)})()}),s=null,l={},window[e]={handleCSSRetry:A,handleCSSsRetry:function(e){for(var r=0;r<e.length;r++)A(e[r])},loadCSS:function(e,r){var t=2<arguments.length&&void 0!==arguments[2]&&arguments[2],n=O(e),r=b(t=t?"firstScreen":"dynamicImport",e,r),o={id:n,status:"loading",resourceType:"css",initiatorType:t,initiatorUrl:e,loadPromise:r};return r.then(function(){o.status="loaded"}).catch(function(){o.status="errored"}),l[n]=o,r},loadScripts:function(e,r){for(var t=2<arguments.length&&void 0!==arguments[2]&&arguments[2],n=[],o=0;o<e.length;o++)n.push(F(e[o],r[o],t));return n},isFSCSSAllLoaded:function(){var r=l,t=0,n=0;return Object.keys(r).forEach(function(e){e=r[e];"firstScreen"===e.initiatorType&&"css"===e.resourceType&&(t+=1,"loaded"===e.status&&(n+=1))}),n===t},getAnyFirstScreenResourceErroredInfo:function(){return s},loadScript:F,handleImgRetry:function(e){return o=e,(e=new Promise(function(e,r){var t=g(d(o.src),T()),n=U({initialUrl:o.src,initialEl:o,groupHost:t,retryHosts:t?(T()[t]||[]).slice():[]},N);n.InitialError.add(function(e){}),n.FinalLoad.add(function(r){n.getProcs().forEach(function(e){e!==r&&e.el!==o&&(e.el.src="")}),e(r.options.url)}),n.FinalError.add(function(){r()})})).catch(function(){}),e;var o}}))})();;</script><meta charset="utf-8"><script retain-in-offline="true">!function(t){var e=t._plt=t._plt||[];e.push(["tm","ps",+new Date]),(t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.setTimeout)(function(){e.push(["tm","fraf",+new Date])})}(window);!function(){var e,t,n,i,r={passive:!0,capture:!0},a=new Date,o=function(){i=[],t=-1,e=null,f(addEventListener)},c=function(i,r){e||(e=r,t=i,n=new Date,f(removeEventListener),u())},u=function(){if(t>=0&&t<n-a){var r={entryType:"first-input",name:e.type,target:e.target,cancelable:e.cancelable,startTime:e.timeStamp,processingStart:e.timeStamp+t};i.forEach((function(e){e(r)})),i=[]}},s=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var n=function(){c(e,t),a()},i=function(){a()},a=function(){removeEventListener("pointerup",n,r),removeEventListener("pointercancel",i,r)};addEventListener("pointerup",n,r),addEventListener("pointercancel",i,r)}(t,e):c(t,e)}},f=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach((function(t){return e(t,s,r)}))},p="hidden"===document.visibilityState?0:1/0;addEventListener("visibilitychange",(function e(t){"hidden"===document.visibilityState&&(p=t.timeStamp,removeEventListener("visibilitychange",e,!0))}),!0);o(),self.webVitals={firstInputPolyfill:function(e){i.push(e),u()},resetFirstInputPolyfill:o,get firstHiddenTime(){return p}}}();</script><link rel="dns-prefetch" href="//static.kwcdn.com"><link rel="dns-prefetch" href="//aimg.kwcdn.com"><meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no,viewport-fit=cover"><meta name="format-detection" content="telephone=no"><meta http-equiv="Cache-Control" content="no-cache,no-store,must-revalidate"><meta http-equiv="Pragma" content="no-cache"><meta http-equiv="Expires" content="0"><title>Temu | 没有互联网连接</title><script>var pinbridge=function(){function e(o){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(o)}var o={Ios:/\b(iphone|ipad|ipod)/i,IosVersion:/os (\d+)_(\d+)_?(\d+)?/i,Android:/Android/i,AndroidVersion:/Android (\d+).?(\d+)?.?(\d+)?/i,AndroidNative:/\s(temu|[a-z]h{2})_android_version\//i,IosNative:/\s(temu|[a-z]h{2})_ios_version\//i,IosApiRequest:/BundleID\/com.einnovation.temu/i,Mobile:/Android|webOS|iPhone|iPad|iPod/i,AndroidNativeVersion:/((temu|[a-z]h{2})_android_version)\/([^\s]+)\s*/i,IosNativeVersion:/((temu|[a-z]h{2})_ios_version|AppVersion)\/([^\s]+)\s*/i,MecoWebViewCore:/(MecoCore|WebKernel)\/(\d)/i,Crawler:/\+http|Chrome-Lighthouse|Google-InspectionTool|GoogleOther/},t={Messenger:/\bFB[\w_]+\/(Messenger|MESSENGER)/,Facebook:/\bFB[\w_]+\//,Twitter:/\bTwitter/i,Line:/\bLine\//i,Instagram:/\bInstagram/i,Whatsapp:/\bWhatsApp/i,Snapchat:/Snapchat/i,Tiktok:/musical_ly/i,Pinterest:/Pinterest/i},i=/\bMozilla/i,r={Unknown:"unknown",Browser:"browser",NativeIOS:"ios",NativeAndroid:"android",Messenger:"messenger",Facebook:"facebook",Twitter:"twitter",Line:"line",Instagram:"instagram",Whatsapp:"whatsapp",Snapchat:"snapchat",Tiktok:"tiktok",Pinterest:"pinterest"};function a(e,o){[e,o].forEach((function(e){if("string"!=typeof e)throw new TypeError("Invalid argument expected string")}));for(var t=function(e){return e.split(".").map((function(e){return parseInt(e,10)||0}))},i=t(e),r=t(o),a=Math.max(i.length,r.length),n=0;n<a;++n){var s=i[n]||0,c=r[n]||0;if(s!==c)return s-c}return 0}var n={"AMAnalytics.send":"TMAnalytics.sendTracePoint","AMApplication.activationTime":"TMApplication.activationAppTime","AMApplication.check":"TMApplication.checkApp",AMBridgeAPI:"TMBridge","AMBridgeAPI.check":"TMBridge.exist","AMDevice.deviceInfo":"TMDevice.currentDeviceInfo","AMDevice.getAccessibilityState":"TMDevice.getDeviceAccessibilityState","AMDevice.getBatteryInfo":"TMDevice.getDeviceBatteryInfo","AMDevice.getBrightness":"TMDevice.getScreenBrightness","AMDevice.getCameraAvailable":"TMDevice.getDeviceCameraAvailable","AMDevice.getMemoryInfo":"TMDevice.getDeviceMemoryInfo","AMDevice.isLowEndDevice":"TMDevice.lowEnd","AMDevice.model":"TMDevice.deviceModel","AMDevice.setBrightness":"TMDevice.setScreenBrightness","AMDevice.setH5LowEndDeviceInfo":"TMDevice.setLowEndInfo","AMDevice.vibrateLong":"TMDevice.vibrateDeviceLong","AMDevice.vibrateShort":"TMDevice.vibrateDeviceShort","AMLinking.openURL":"TMLinking.open","AMLocation.check":"TMLocation.checkLoc","AMLocation.get":"TMLocation.getLoc","AMStorage.get":"TMStorage.getStorage","AMStorage.getString":"TMStorage.getStorageString","AMStorage.getSync":"TMStorage.getSyncStorage","AMStorage.gets":"TMStorage.getsStorage","AMStorage.set":"TMStorage.setStorage","AMStorage.setPasteboard":"TMStorage.setStoragePasteboard","AMStorage.setString":"TMStorage.setStorageString","AMStorage.setSync":"TMStorage.setSyncStorage","AMStorage.sets":"TMStorage.setsStorage","AMUser.info":"TMLogin.getUserInfo","AMUserNotification.checkNotify":"TMUserNotification.checkPushPermit","AMUserNotification.disable":"TMUserNotification.disablePushPermit","AMUserNotification.enableNotify":"TMUserNotification.enablePushPermit","AMUserNotification.query":"TMUserNotification.queryPushPermit","AMUserNotification.register":"TMUserNotification.registerPushPermit","AMUserNotification.start":"TMUserNotification.startPushPermit","AMUserNotification.unregister":"TMUserNotification.unregisterPushPermit",AMVideo:"TMVideoPicker","AMVideo.get":"TMVideoPicker.pick",BGAudioPlayer:"TMAudioPlayer","BGAudioPlayer.preLoad":"TMAudioPlayer.preLoadSource","BGNavigator.backToGoodsList":"TMNavigator.backToPageForPageSource","BGNavigator.pageSourceStack":"TMNavigator.routerPageSourceStack","BGPopupManager.requestPopupAndShow":"TMModal.requestAndShow",BGShoppingBagApi:"TMShoppingCartApi","BGShoppingBagApi.addShoppingBag":"TMShoppingCartApi.addShoppingCart","BGShoppingBagApi.batchAddShoppingBag":"TMShoppingCartApi.batchAddShoppingCart","JSActionSheet.show":"TMActionSheet.showSheet","JSAlert.showAlert":"TMAlert.showTMAlert","JSAlert.toast":"TMAlert.showToast","JSAppStatus.isAppForeground":"TMAppStatus.isAppForegroundState","JSBGCommonManager.openSystemReview":"TMCommonManager.openApplicationReview",JSBGCookiePreferences:"TMCookiePreferences","TMCookiePreferences.setAuthInfo":"TMCookiePreferences.setAuthInfo",JSBGLogin:"TMLogin",JSBGLoginUtility:"TMLoginUtility",JSBGPay:"TMPay","JSBGShare.share":"TMShare.temuShare","JSBGShare.shareAvailableChannels":"TMShare.temuShareAvailableChannels","JSBGShare.sharePanelShow":"TMShare.temuSharePanelShow",JSBGUserSetting:"TMUserSetting",JSBGWidget:"TMWidget",JSBadge:"TMMessages",JSBrowserType:"TMBrowserType","JSCommonPicker.show":"TMCommonPicker.showPicker","JSDatePicker.show":"TMDatePicker.showPicker",JSDownloader:"TMFetcher","JSDownloader.download":"TMFetcher.fetch",JSFile:"TMFile",JSGoodsGiftPopupBridge:"TMGoodsGiftPopupBridge",JSHardwareControl:"TMHardwareControl","JSKeyboard.hideKeyboard":"TMKeyboard.hideSystemKeyboard","JSKeyboard.showKeyboard":"TMKeyboard.showSystemKeyboard","JSKeyboardAdjustMode.apply":"TMKeyboardAdjustMode.applyAdjustMode","JSKeyboardListener.create":"TMKeyboardListener.createListener","JSKeyboardListener.destroy":"TMKeyboardListener.destroyListener","JSLegoBundleManager.preloadLDS":"TMOtterBundleManager.preloadBundle",JSLocalizations:"TMLocalizations",JSLogger:"TMLog",JSMapApi:"TMMapApi",JSMediaPresent:"TMMediaPresent",JSNavigation:"TMNavigation","JSNavigation.addPageContext":"TMNavigation.addRouterPageContext","JSNavigation.back":"TMNavigation.backToPage","JSNavigation.dismissMask":"TMNavigation.dismissMaskPage","JSNavigation.dismissModal":"TMNavigation.dismissModalPage","JSNavigation.forward":"TMNavigation.forwardToPage","JSNavigation.getSelectedTab":"TMNavigation.getSelectedIndexTab","JSNavigation.mask":"TMNavigation.maskToPage","JSNavigation.modal":"TMNavigation.modalToPage","JSNavigation.pageContext":"TMNavigation.getRouterPageContext","JSNavigation.pageSource":"TMNavigation.getRouterPageSource","JSNavigation.referPageContext":"TMNavigation.getRouterReferPageContext","JSNavigation.removePage":"TMNavigation.deletePage","JSNavigation.replace":"TMNavigation.replacePage","JSNavigation.reset":"TMNavigation.backToHome","JSNavigation.selectTab":"TMNavigation.selectIndexTab","JSNavigation.setPageContext":"TMNavigation.setRouterPageContext","JSNavigation.setup":"TMNavigation.setupPage",JSNetwork:"TMNetwork","JSNetwork.getTimeInfo":"TMNetwork.timeInfo","JSNetwork.info":"TMNetwork.networkInfo",JSNotification:"TMNotification",JSOrderApi:"TMOrderApi",JSOrderPay:"TMOrderPay",JSOrderSearchWebViewBridge:"TMOrderSearchWebViewBridge",JSPayManagement:"TMPayManagement",JSPermission:"TMPermission","JSPermission.checkCameraPermission":"TMPermission.checkAppCameraPermission","JSPhoto.getV2":"TMImagePicker.pick","JSPhoto.getV3":"TMImagePicker.pick",JSRecovery:"TMRecovery","JSReporter.apiReport":"TMMetrics.apiMetrics","JSReporter.customReport":"TMMetrics.customMetrics","JSReporter.errorReport":"TMMetrics.errorMetrics","JSReporter.frontLogReport":"TMMetrics.frontLogMetrics","JSReporter.webPageReport":"TMMetrics.webPageMetrics",JSRiskControl:"TMRiskControl","JSSearch.getQueryHistory":"TMSearch.getTMHistory","JSSearch.getSearchActions":"TMSearch.getTMActions","JSSearch.setQueryHistory":"TMSearch.setTMHistory",JSSend:"TMSend",JSSku:"TMSku",JSSoundPool:"TMSoundPool","JSSoundPool.pauseBgMusic":"TMSoundPool.pauseBackgroundMusic","JSSoundPool.playBgMusic":"TMSoundPool.playBackgroundMusic","JSSoundPool.preload":"TMSoundPool.preloadSource","JSSoundPool.resumeBgMusic":"TMSoundPool.resumeBackgroundMusic","JSSoundPool.stopBgMusic":"TMSoundPool.stopBackgroundMusic","JSSoundPool.unload":"TMSoundPool.unloadSource",JSSubjects:"TMSceneGroup","JSSwipe.setSwipeBackInterceptor":"TMSwipe.setSwipeInterceptor",JSSystemSetting:"TMSystemSetting",JSTitanPush:"TMTcpLinkPush",JSUIControl:"TMUIControl","JSUIControl.checkState":"TMUIControl.checkUIState","JSUIControl.isInMultiWindowMode":"TMUIControl.isMultiWindow","JSUIControl.setBackButton":"TMUIControl.setBackInterceptor","JSUIControl.setRollingAlpha":"TMUIControl.setPageRollingAlpha","JSUIControl.supportCustomNavigationBar":"TMUIControl.supportImmerseNavigationBar",JSUniPopup:"TMModal","JSUniPopup.getPopupData":"TMModal.getData","JSUniPopup.showHighLayer":"TMModal.showModal","JSUniPopup.updatePopupData":"TMModal.updateData","JSUpgrade.executeAppUpgrade":"TMUpgrade.invokeAppUpgrade",JSUploader:"TMUploader","JSUploader.fileUpload":"TMUploader.uploadFile","JSUploader.imageUpload":"TMUploader.uploadImage",JSUserStorage:"TMUserStorage",JSWebApm:"TMWebApm",JSWebRegion:"TMWebRegion",WebScene:"TMScene","WebScene.forbidNativeHideLoading":"TMScene.forbidHideLoading","WebScene.getPageShownType":"TMScene.getPageShowType","WebScene.getTiming":"TMScene.getNativeTimeInfo","WebScene.getUnoContext":"TMScene.getWebContext","WebScene.getUnoExperimentList":"TMScene.getExperimentList","WebScene.onWebMounted":"TMScene.onWebReady","WebScene.showRetryPage":"TMScene.showRetryUI"},s=Object.keys(n).reduce((function(e,o){return e[n[o]]||(e[n[o]]=o),e}),{"TMImagePicker.pick":"JSPhoto.getV3"}),c=function(){var e,n,s=navigator.userAgent,c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(o.AndroidNative.test(e))return r.NativeAndroid;if(o.IosNative.test(e)||o.IosApiRequest.test(e))return r.NativeIOS;for(var a in t)if(t[a].test(e))return r[a];return i.test(e)?r.Browser:r.Unknown}(s),g=function(e,t){var i=t===r.NativeAndroid?o.AndroidNativeVersion:o.IosNativeVersion,a=e.match(i);return a&&a[3]||""}(s,c);return{jsApiV2:a(g,"3.8.0")>=0||window._JSApiRename||(null===(e=window.webkit)||void 0===e||null===(e=e.messageHandlers)||void 0===e?void 0:e._JSApiRename),jsBridgeV2:a(g,"3.45.0")>=0||window._JSBridgeRename||(null===(n=window.webkit)||void 0===n||null===(n=n.messageHandlers)||void 0===n?void 0:n._JSBridgeRename)}},g=function(e,o){var t="".concat(e,".").concat(o);if(c().jsApiV2){if(n[t])return n[t].split(".");if(n[e])return[n[e],o]}else{if(s[t])return s[t].split(".");if(s[e])return[s[e],o]}return[e,o]};var d=function(){try{if(top.pinbridge)return top.pinbridge}catch(e){setTimeout((function(){throw e}))}var o=c().jsBridgeV2?{IOS_SCHEME:"tmbridge:///",ANDROID_BRIDGE:"__tm_android_bridge",CALL_ID_PREFIX:"__tm_bridge_id_"}:{IOS_SCHEME:"pinbridge:///",ANDROID_BRIDGE:"_fastJsN",CALL_ID_PREFIX:"__aimi_function_"},t=o.ANDROID_BRIDGE,i=o.IOS_SCHEME,r=o.CALL_ID_PREFIX,a=0,n={},s=function o(t){if("object"!==e(t))return t;var i={};for(var s in"[object Array]"===Object.prototype.toString.call(t)&&(i=[]),t)if(t.hasOwnProperty(s)){var c=t[s];if("function"==typeof c){var g=++a;n[g]={func:c},i[s]=r+g}else"object"===e(c)&&null!==c?i[s]=o(c):i[s]=c}return i},d=function(e,o,r,a){var n;"function"!=typeof(null===(n=window[t])||void 0===n?void 0:n.callNative)?(r=encodeURIComponent(JSON.stringify(r)),function(e){var o;null!==(o=window.webkit)&&void 0!==o&&null!==(o=o.messageHandlers)&&void 0!==o&&o.hybridAPIMessageHandler&&window.webkit.messageHandlers.hybridAPIMessageHandler.postMessage({url:e})}("".concat(i,"request?t=").concat(e,"&m=").concat(o,"&p=").concat(r,"&c=").concat(a))):window[t].callNative(e,o,r?JSON.stringify(r):null,a)},S=function(e,o,t){t=t||{};var i=n[e];if(i){var r=i.func,a=i.onSuccess,s=i.onError;r?r(o,t):0===o?a&&a(t):s&&s(t,o)}};return window.pinbridge={check:function(e,o){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){},i=arguments.length>3?arguments[3]:void 0;e&&o?window.pinbridge.callNative("AMBridgeAPI","check",{module_name:e,method_name:o},(function(e){return t(e&&e.exist)}),(function(){return i?i():t(!1)})):t(!1)},callNative:function(e,o,t,i,r){t=t||{};var c=g(e,o),S=++a;if(n[S]={onSuccess:i,onError:r},t=s(t),["AMBridgeAPI.check","TMBridge.exist"].includes("".concat(e,".").concat(o))){var M=g(t.module_name,t.method_name);t.module_name=M[0],t.method_name=M[1]}return d(c[0],c[1],t,S)},callbackFromNative:S,callback:S,removeCallback:function(e){delete n[e]}},window.tmbridge=window.pinbridge,window.pinbridge}();return d}();</script><script retain-in-offline="true">document.documentElement.setAttribute('data-theme', 't3');document.documentElement.removeAttribute('dir');</script><script>window.leoConfig={};</script><script>!function(){function e(e,r){(null==r||r>e.length)&&(r=e.length);for(var n=0,t=Array(r);n<r;n++)t[n]=e[n];return t}function r(e,r,n){return(r=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var t=n.call(e,r||"default");if("object"!=typeof t)return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(r))in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}function n(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),n.push.apply(n,t)}return n}function t(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?n(Object(o),!0).forEach((function(n){r(e,n,o[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):n(Object(o)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(o,r))}))}return e}function o(e,r){if(null==e)return{};var n,t,o=function(e,r){if(null==e)return{};var n={};for(var t in e)if({}.hasOwnProperty.call(e,t)){if(r.includes(t))continue;n[t]=e[t]}return n}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(t=0;t<i.length;t++)n=i[t],r.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function i(r){return function(r){if(Array.isArray(r))return e(r)}(r)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(r)||function(r,n){if(r){if("string"==typeof r)return e(r,n);var t={}.toString.call(r).slice(8,-1);return"Object"===t&&r.constructor&&(t=r.constructor.name),"Map"===t||"Set"===t?Array.from(r):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?e(r,n):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function u(e){return e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent)}function s(e){var r=document.cookie?document.cookie.split("; "):[],n={};return r.some((function(r){var t=r.split("="),o=t.slice(1).join("="),i=u(t[0]);return o=u(o),n[i]=o,e===i})),e?n[e]||"":n}String.prototype.includes||(String.prototype.includes=function(e,r){return"number"!=typeof r&&(r=0),!(r+e.length>this.length)&&-1!==this.indexOf(e,r)});var c=function(){for(var e,r=[],n=0;n<256;n++){e=n;for(var t=0;t<8;t++)e=1&e?3988292384^e>>>1:e>>>1;r[n]=e}return r}();function f(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;e=function(e){for(var r="",n=0;n<e.length;n++){var t=e.charCodeAt(n);t<128?r+=String.fromCharCode(t):t<2048?r+=String.fromCharCode(192|t>>6)+String.fromCharCode(128|63&t):t<55296||t>=57344?r+=String.fromCharCode(224|t>>12)+String.fromCharCode(128|t>>6&63)+String.fromCharCode(128|63&t):(t=65536+((1023&t)<<10|1023&e.charCodeAt(++n)),r+=String.fromCharCode(240|t>>18)+String.fromCharCode(128|t>>12&63)+String.fromCharCode(128|t>>6&63)+String.fromCharCode(128|63&t))}return r}(e),r=~r;for(var n=0;n<e.length;n++)r=r>>>8^c[255&(r^e.charCodeAt(n))];return~r>>>0}var d={unknown:0,wifi:1,"slow-2g":2,"2g":2,"3g":3,"4g":4},l=d.unknown;function p(){var e="object"===("undefined"==typeof navigator?"undefined":a(navigator))&&navigator.connection&&("wifi"===navigator.connection.type?navigator.connection.type:navigator.connection.effectiveType);return d[e]||l}var g={Ios:/\b(iphone|ipad|ipod)/i,IosVersion:/os (\d+)_(\d+)_?(\d+)?/i,Android:/Android/i,AndroidVersion:/Android (\d+).?(\d+)?.?(\d+)?/i,AndroidNative:/\s(temu|[a-z]h{2})_android_version\//i,IosNative:/\s(temu|[a-z]h{2})_ios_version\//i,IosApiRequest:/BundleID\/com.einnovation.temu/i,Mobile:/Android|webOS|iPhone|iPad|iPod/i,AndroidNativeVersion:/((temu|[a-z]h{2})_android_version)\/([^\s]+)\s*/i,IosNativeVersion:/((temu|[a-z]h{2})_ios_version|AppVersion)\/([^\s]+)\s*/i,MecoWebViewCore:/(MecoCore|WebKernel)\/(\d)/i,Crawler:/\+http|Chrome-Lighthouse|Google-InspectionTool|GoogleOther/},v={Messenger:/\bFB[\w_]+\/(Messenger|MESSENGER)/,Facebook:/\bFB[\w_]+\//,Twitter:/\bTwitter/i,Line:/\bLine\//i,Instagram:/\bInstagram/i,Whatsapp:/\bWhatsApp/i,Snapchat:/Snapchat/i,Tiktok:/musical_ly/i,Pinterest:/Pinterest/i},m=/\bMozilla/i,_={Unknown:"unknown",Browser:"browser",NativeIOS:"ios",NativeAndroid:"android",Messenger:"messenger",Facebook:"facebook",Twitter:"twitter",Line:"line",Instagram:"instagram",Whatsapp:"whatsapp",Snapchat:"snapchat",Tiktok:"tiktok",Pinterest:"pinterest"},h="android",w="ios",y="unknown";function b(e){return g.Android.test(e)?h:g.Ios.test(e)?w:y}function S(e){return g.Crawler.test(function(e){return window.__HEADER_USERAGENT__?window.__HEADER_USERAGENT__:e||window.navigator.userAgent||""}(e))}var O=["token","uin","oauth_code","_order_ticket","PASSID","PASS_ID"],A=[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",r=[];(arguments.length>1&&void 0!==arguments[1]?arguments[1]:[]).forEach((function(e){r.push(e+"=",e+'":')}));var n=new RegExp("("+r.join("|")+')\\s*"?([^;,&"]+)"?',"gi");return e.replace(n,(function(e,r,n){return""+r+n.length}))},function(e){return function(){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").replace(/\w+([-+.]\w+)*(@|%40)\w+([-.]\w+)*\.\w+([-.]\w+)*/g,e)}(e,"**email**")}];function R(e){if(!e)return e;try{return A.reduce((function(e,r){return r(e,O)}),e)}catch(r){return console.error("replaceSensitive error:",r),e}}var E=["name","message","stack"];function P(e){return"object"===a(e)&&null!==e}function I(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return P(e)?t({name:e.name,message:e.message,stack:e.stack},o(e,E)):{message:e}}var C,N,M=(C=function(){var e=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(g.AndroidNative.test(e))return _.NativeAndroid;if(g.IosNative.test(e)||g.IosApiRequest.test(e))return _.NativeIOS;for(var r in v)if(v[r].test(e))return _[r];return m.test(e)?_.Browser:_.Unknown}(navigator.userAgent),r=e===_.NativeAndroid||e===_.NativeIOS,n=r?function(e,r){var n=r===_.NativeAndroid?g.AndroidNativeVersion:g.IosNativeVersion,t=e.match(n);return t&&t[3]||""}(navigator.userAgent,e):"";return{isNativePlatform:r,platform:e,version:n}},N=null,function(){if(!N){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];N=C.apply(null,r)}return N}),k="us",j=window.__REGION_CONFIG__,T=Object.keys(j).reduce((function(e,n){return t(t({},e),{},r({},n,j[n].id))}),{}),x=Object.keys(T).reduce((function(e,n){return t(t({},e),{},r({},T[n],n))}),{}),L=function(e){return function(e){return e||location.hostname}(e).includes("temudemo")},z=function(e){var r,n=null===(r=j[x[e]])||void 0===r?void 0:r.ldp;if("object"===a(n)){if(n.pftk)return n.pftk+".temu.com";if(n.third)return"pftk-"+n.third+".temu.com";if(n.fourth)return n.fourth+".pftk.temu.com";n=void 0}return(n||function(e){var r;return(null===(r=j[x[e]])||void 0===r?void 0:r.dr)||""}(e)||k)+".pftk.temu.com"},U=/Minified React error #\d+;/i;function D(e){var r=e&&e.message;return r?r.error&&"string"==typeof r.error.message?r.error.message:r:"No error message"}function V(e,r){var n=r.stacktrace||r.stack||"",t=function(e){if(e){if("number"==typeof e.framesToPop)return e.framesToPop;if(U.test(e.message))return 1}return 0}(r);try{return e(n,t)}catch(e){}return[]}var G=function(){return G=Object.assign||function(e){for(var r,n=1,t=arguments.length;n<t;n++)for(var o in r=arguments[n])Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o]);return e},G.apply(this,arguments)},F=/\(error: (.*)\)/,H=/captureMessage|captureException/;var q="?";function J(e,r,n,t){var o={filename:e,function:r,in_app:!0};return void 0!==n&&(o.lineno=n),void 0!==t&&(o.colno=t),o}var W=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,$=/\((\S*)(?::(\d+))(?::(\d+))\)/,B=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Y=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,X=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:[-a-z]+):.*?):(\d+)(?::(\d+))?\)?\s*$/i,K=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=e.sort((function(e,r){return e[0]-r[0]})).map((function(e){return e[1]}));return function(e,r){void 0===r&&(r=0);for(var t=[],o=e.split("\\n"),i=r;i<o.length;i++){var a=o[i];if(!(a.length>1024)){var u=F.test(a)?a.replace(F,"$1"):a;if(!u.match(/\S*Error: /)){for(var s=0,c=n;s<c.length;s++){var f=(0,c[s])(u);if(f){t.push(f);break}}if(t.length>=50)break}}}return function(e){if(!e.length)return[];var r=(e||[]).slice(0);return/sentryWrapped/.test(r[r.length-1].function||"")&&r.pop(),r.reverse(),H.test(r[r.length-1].function||"")&&(r.pop(),H.test(r[r.length-1].function||"")&&r.pop()),r.slice(0,50).map((function(e){return G(G({},e),{filename:e.filename||r[r.length-1].filename,function:e.function||"?"})}))}(t)}}.apply(void 0,[[30,function(e){var r=W.exec(e);if(r){if(r[2]&&0===r[2].indexOf("eval")){var n=$.exec(r[2]);n&&(r[2]=n[1],r[3]=n[2],r[4]=n[3])}var t=Z(r[1]||q,r[2]),o=t[0];return J(t[1],o,r[3]?+r[3]:void 0,r[4]?+r[4]:void 0)}}],[50,function(e){var r,n=B.exec(e);if(n){if(n[3]&&n[3].indexOf(" > eval")>-1){var t=Y.exec(n[3]);t&&(n[1]=n[1]||"eval",n[3]=t[1],n[4]=t[2],n[5]="")}var o=n[3],i=n[1]||q;return i=(r=Z(i,o))[0],J(o=r[1],i,n[4]?+n[4]:void 0,n[5]?+n[5]:void 0)}}],[40,function(e){var r=X.exec(e);return r?J(r[2],r[1]||q,+r[3],r[4]?+r[4]:void 0):void 0}]]),Z=function(e,r){var n=-1!==e.indexOf("safari-extension"),t=-1!==e.indexOf("safari-web-extension");return n||t?[-1!==e.indexOf("@")?e.split("@")[0]:q,n?"safari-extension:".concat(r):"safari-web-extension:".concat(r)]:[e,r]},Q=5,ee={API_ERROR:500,RESOURCE_ERROR:501,CUSTOM_ERROR:502,NORMAL_LOG:600},re={},ne={};function te(e){return(r=ee,Object.keys(r).map((function(e){return r[e]}))).indexOf(e)>-1?e:ee.CUSTOM_ERROR;var r}var oe=function(e){return(e||re).pmmHost||"https://"+z(s("region"))},ie=Object.keys(T),ae=new RegExp("^/?(w/)?(("+ie.join("|")+")(-\\w+(-\\w{4})?)?/)?((csr|stc)/)?");function ue(){var e;return null!==(e=window.__PageContext__)&&void 0!==e&&e.pagePath?"/"+window.__PageContext__.pagePath+".html":window.location.pathname.replace(ae,"/$1")}var se=function(e){return e?e.replace(ae,"/$1"):ue()};function ce(e){return null==e||e!=e?"":String(e)}function fe(e){return P(e)?Object.keys(e).reduce((function(r,n){var t=e[n];return P(t)||"function"==typeof t||(r[n]=ce(t)),r}),{}):{}}function de(e){if(e){var r=e.name,n=e.message,t=e.stack,o=void 0===t?"":t;return("string"==typeof o?0===o.indexOf(r)?o:r+": "+n+"\\n"+o:JSON.stringify(o))||"no info"}return""}function le(){return(e=M().version)?+e.split(".").map((function(e){return+e>9?e:"0"+e})).join(""):0;var e}function pe(){return L(window.location.hostname)?"TESTING":"PROD"}var ge={};function ve(){return ge}var me=["message","name","errorCode","errorMsg","url","extraInfo","user_id","network","payload","type","page","httpMethod","module","category"],_e=["payload","error_msg","errorMsg","error_message","errorCode","src","url"];function he(e,n){var i,a,u=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=arguments.length>3?arguments[3]:void 0,c=arguments.length>4?arguments[4]:void 0,d=e.message,l=e.errorCode,p=void 0===l?"555555":l,v=e.errorMsg,m=e.url,y=e.extraInfo,S=void 0===y?{}:y,O=(e.user_id,e.network),A=e.payload,E=e.type,P=void 0===E?ee.CUSTOM_ERROR:E,I=e.page,C=e.httpMethod,N=e.module,k=e.category,j=void 0===k?Q:k,T=o(e,me),z=te(P),U=S.payload,G=S.error_msg,F=S.errorMsg,H=S.error_message,q=S.errorCode,J=S.src,W=S.url,$=o(S,_e),B=c.region,Y=c.language,X=c.language_locale,Z=c.currency,re=c.timezone,oe=R(c.href),ie=c.pagePath,ae=R(ve().href),ue=ve().pagePath,se=fe(t(t(t(t(t({unique_tag:c.timestamp+"_"+(null===(i=c.api_uid)||void 0===i?void 0:i.slice(4,20)),error_logger:1,pageId:u.pageId,lang:Y,language_locale:X,currency:Z,timezone:re,initial_pageUrl:ae===oe?"":ae,initial_pagePath:ue===ie?"":ue,docSource:window.__DOC_SOURCE__},ne),A),U),T),$)),de=function(e,r){var n=r.timestamp,o=Math.pow(10,5)+Math.floor(Math.random()*(Math.pow(10,6)-Math.pow(10,5)));return t(t({},(i=e,{biz_side:i.bizSide||"consumer-platform-fe",app:i.appId})),{},{level:2,version:le(),report_time_ms:n,rand_num:o,crc32:f(n+"-"+o)});var i}(n,c);de.common_tags=function(e,n){var o=M(),i=o.platform,a=o.isNativePlatform,u=function(e){return(L?r(r({},_.NativeAndroid,"1"),_.NativeIOS,"3"):r(r({},_.NativeAndroid,"3"),_.NativeIOS,"5"))[e]||"-1"}(i),s=function(e){var r=b(e),n="";if(r===w&&(n=g.IosVersion),r===h&&(n=g.AndroidVersion),n){var t=e.match(n);return(t?[t[1],t[2],t[3]].map((function(e){return e?parseInt(e,10):0})):[]).join(".")}return""}(navigator.userAgent),c=b(navigator.userAgent);return fe(t({p:i,runningPlatform:i,runningAppId:u,env:pe(),b:"",mid:"",osV:c+"_"+s},!a&&{pid:n.ETag||n._bee,did:n.api_uid}))}(0,c);var ge,he,we,ye=n.extraTags,be=void 0===ye?{}:ye,Se=(he=V(K,ge=e),we={type:ge&&ge.name,value:D(ge)},he.length&&(we.stacktrace={frames:he}),void 0===we.type&&""===we.value&&(we.value="Unrecoverable error caught"),we),Oe={};Se&&(Oe={releaseVersion:(window.__PageContext__||{}).buildId,stackType:"js",stack:R(JSON.stringify(Se))});var Ae=fe(t(t(t(t({network:O,serverIp:"",url:R(m||W||J||c.href),errorCode:q||p,errorMsg:ce(v||G||F||H||d||"no info").substr(0,150),module:N||"0001",pagePath:ie,pageUrl:oe,page:R(I||""),log_version:"0.0.2",user_agent:window.navigator.userAgent,app_version:le(),httpMethod:C,region:(x[B]||"unknown").toUpperCase()},void 0!==(null===(a=window.__PageContext__)||void 0===a?void 0:a.modern)&&{modern:window.__PageContext__.modern?1:0}),Oe),function(e){return{pageSn:e.pageSN,pageName:e.pageName,referPageId:e.referPageId,referPageName:e.referPageName,referPageSn:e.referPageSN}}(u)),be));return de.datas=[t({category:j,type:z,timestamp:c.timestamp,tags:Ae,extras:se},s)],de}function we(){return window.__ERROR_SAMPLE_RATE__&&Math.random()>window.__ERROR_SAMPLE_RATE__||!M().isNativePlatform&&!s("region")}function ye(e,r){!function(e){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.datas[0].tags;console.groupCollapsed("%c[error-logger"+(r?"-inline":"")+"]: ","color: red;background: yellow",n.errorMsg),console.log("page(stack): ",n.page),console.log("detail: ",e),console.groupEnd()}(e,arguments.length>2&&void 0!==arguments[2]&&arguments[2])}function be(e,r){var n;if(!we()){var t=function(e,r){return oe(r)+(e===ee.NORMAL_LOG?"/pmm/api/pmm/front_log":"/pmm/api/pmm/front_err")}(null==e||null===(n=e.datas[0])||void 0===n?void 0:n.type,r),o=new XMLHttpRequest;o.open("POST",t,!0),o.setRequestHeader("Content-Type","text/plain;charset=UTF-8"),o.withCredentials=!0,o.send(JSON.stringify(e))}}var Se=/\s(temu|[a-z]h{2})_ios_version\//i.test(navigator.userAgent),Oe=/\s(temu|[a-z]h{2})_android_version\//i.test(navigator.userAgent),Ae=Oe||Se,Re={1:1,2:.5,4:.25,5:.2,10:.1,100:.01};var Ee=function(){var e=1,r=1;if(window.__CMT_AMPLIFY_RATE__){var n=window.__CMT_AMPLIFY_RATE__;Re[n]?(e=+n,r=Re[n]):console.warn("monitor sample rate ".concat(n," invalid, support 1,2,4,5,10,100"))}return{amplifyRate:e,sampleRate:r}}().amplifyRate;function Pe(e,r){return Object.keys(e).reduce((function(n,t){return r(n,t,e[t]),n}),{})}function Ie(e){return void 0===e?e:String(e)}var Ce=window.location.href.indexOf("temudemo.com")>=0;function Ne(){if(!Ae)return"-1";if(Ce){if(Se)return"3";if(Oe)return"1"}else{if(Se)return"235";if(Oe)return"234"}return"-1"}function Me(e,r,n,t,o){var i=function(e,r,n){var t=Math.pow(10,5)+Math.floor(Math.random()*(Math.pow(10,6)-Math.pow(10,5))),o=Date.now(),i=s();return{version:n?0:le(),report_time_ms:o,rand_num:t,crc32:f(o+"-"+t),biz_side:r.bizSide||"consumer-platform-fe",app:String(r.appId||"100581"),common_tags:G({runningAppId:Ne(),env:pe()},!Ae&&{pid:i.ETag||i._bee,did:i.api_uid}),datas:e}}(e,n,t);o?console.log("pmm log: ",i,r):function(e,r){if(Ae){var n=function(){return window.pinbridge&&window.pinbridge.callNative("JSReporter","customReport",r)};window.pinbridge?n():setTimeout((function(){n()}))}else{var t=new XMLHttpRequest;t.open("POST",e,!0),t.setRequestHeader("Content-Type","text/plain;charset=UTF-8"),t.withCredentials=!0,t.send(JSON.stringify(r))}}(r,i)}function ke(e,r,n,t){void 0===n&&(n=!1),void 0===t&&(t=!1),Array.isArray(e)||(e=[e]);var o=[];e.forEach((function(e){var r=e.groupId,n=e.amplifyRate,t=void 0===n?Ee:n,i=e.tags,a=void 0===i?{}:i,u=e.fields,c=void 0===u?{}:u,f=e.longFields,d=void 0===f?{}:f;if(function(e,r){return!(!r||window.__CRAWLER_BY_TITAN__||S(navigator.userAgent)||!Ae&&(Math.random()>e||!s("region")))}(t>0?1/t:t,r)){var l=s();["language_locale"].forEach((function(e){c[e]||(c[e]=l[e]||"0")})),["language","currency","timezone"].forEach((function(e){a[e]||(a[e]=l[e]||"empty")})),a.pn||(a.pn=ue()),a.platform||(a.platform=M().platform);var p=Pe(a,(function(e,r,n){return e["custom_".concat(r)]=Ie(n)})),g=Pe(d,(function(e,r,n){e[r]={values:[parseInt(String(n),10)||0]}})),v=Pe(c,(function(e,r,n){return e[r]=Ie(n)})),m={category:4,type:400,id_raw_value:String(r),timestamp:Date.now(),tags:G(G({},p),{region:(x[l.region]||"unknown").toUpperCase()}),lvalues:g,extras:v,api_ratio:t};o.push(m)}})),o.length>0&&Me(o,"".concat(oe(r),"/pmm/api/pmm/defined"),r,n,t)}var je="user_uin";function Te(e){var r,n=e.appInfo,o=e.pageInfo,a=e.commonParams,u=e.sensitiveParams,c=e.isLocal,f=n.bizSide,d=n.appId;f&&d||console.error("Error: initInlineLogger must need bizSize, appId"),function(){re=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}}(n),function(e){var r=(e=Array.isArray(e)?e:[e]).filter((function(e){return"string"==typeof e})),n=e.filter((function(e){return"function"==typeof e}));O=O.concat(r).filter((function(e,r,n){return n.indexOf(e)===r})),A.push.apply(A,i(n))}((window.__SENSITIVE_PARAMS__||[]).concat(u)),r=n.reportPath,ge={href:window.location.href,pagePath:se(r)};var l=function(e){var r=e.message,n=e.filename,o=e.lineno,i=e.colno,a=e.error,u=a&&a.stack,s=I(a);v(t(t({},s),{},{errorMsg:(u?"html_error_js_":"html_error_")+(s.message||r),page:u?de(a)+"\\n::("+o+":"+i+")":r+", "+n+", "+o+", "+i+", "+a}))},g=function(e){var r,n=null===(r=e.reason)||void 0===r?void 0:r.message;v(t(t({},I(e.reason)),{},{errorMsg:"html_error_unhandledrejection_"+(n||""),page:de(e.reason)}))};function v(e){var r=p(),i=function(e){var r=s(),n=r.region,t=r.language,o=r.language_locale,i=r.currency,a=r.timezone,u=r.api_uid,c=r.ETag,f=r._bee;return{href:window.location.href,pagePath:se(e.reportPath),api_uid:u,ETag:c,_bee:f,region:n,language:t,language_locale:o,currency:i,timezone:a,timestamp:Date.now()}}(n);!function(e){if(m)return e(m);var r=s(je);if(M().isNativePlatform&&window.pinbridge){var n=function(n){m=n.uin||r||"0",e(m)},t=function(){e(m=r)};return window.pinbridge.callNative("AMUser","info",null,n,t)}e(m=r)}((function(u){var s=t(t({},e),{},{network:String(r),user_id:u});s.payload=t({inline:1},s.payload||{}),function(e){for(var r=arguments.length,n=new Array(r>1?r-1:0),t=1;t<r;t++)n[t-1]=arguments[t];n.forEach((function(r){Object.keys(r).forEach((function(n){e[n]=r[n]}))}))}(s,"function"==typeof a?a(s):a),function(e,r){if(arguments.length>2&&void 0!==arguments[2]&&arguments[2])ye(e,r,!0);else if(!we())if(M().isNativePlatform&&window.pinbridge){var n,t="errorReport";(null==e||null===(n=e.datas[0])||void 0===n?void 0:n.type)===ee.NORMAL_LOG&&(t="frontLogReport"),window.pinbridge.callNative("JSReporter",t,e)}else be(e,r)}(he(s,n,o,null,i),n,c)}))}window.__RESET_ERROR_LISTENER__=function(){window.removeEventListener("unhandledrejection",g),window.removeEventListener("error",l)},window.addEventListener("error",l),window.addEventListener("unhandledrejection",g),window.addEventListener("error",(function(e){if("[object Event]"===Object.prototype.toString.call(e)){var r=e.target;if(r&&r.nodeName)v({errorMsg:"html_error_"+r.nodeName.toLowerCase(),page:"Error: "+r.outerHTML,type:ee.RESOURCE_ERROR,url:R(String(r.src||r.href||""))});else{var n="Error: "+JSON.stringify(e);v({errorMsg:"html_error_"+(null==e?void 0:e.message),page:n})}}}),!0);var m=""}window.initInlineLogger=function(e){var r=e.appInfo,n=e.pageInfo,t=e.commonParams,o=void 0===t?{}:t,i=e.sensitiveParams,a=void 0===i?[]:i,u=e.isLocal,s=void 0!==u&&u;Te({appInfo:r,pageInfo:n,commonParams:o,sensitiveParams:a,isLocal:s}),function(e){var r=e.appInfo,n=e.isLocal;window.__reportDefined__=function(e,t,o){return ke(e,t||r,o,n)}}({appInfo:r,isLocal:s})}}();window.pmmAppInfo={"appId":"100608","testAppId":"100590","bizSide":"consumer-platform-fe","reportPath":"/csr/bgn_no_access.html"};initInlineLogger({ appInfo: window.pmmAppInfo });</script><script>document.documentElement.translate = false;</script><script>
(function() {
var userAgent = navigator.userAgent;
if(/\s(temu|[a-z]h{2})_(ios|android)_version\//i.test(userAgent)) {
    window.pinbridge && window.pinbridge.callNative('JSUIControl', 'hideLoading');
}
})();</script><script retain-in-offline="true">
      if (window.__XRenderResourcesLoader__) {
        window.__XRenderResourcesLoader__.handleCSSsRetry(["https://static.kwcdn.com/m-assets/assets/css/biz_vendors-74dbec79b348133dbb99.css","https://static.kwcdn.com/m-assets/assets/css/bgn_no_access-515bf7cc3f5ec99df1fa.css"]);
      }
      </script><link rel="stylesheet" href="https://static.kwcdn.com/m-assets/assets/css/biz_vendors-74dbec79b348133dbb99.css" crossorigin="anonymous"><link rel="stylesheet" href="https://static.kwcdn.com/m-assets/assets/css/bgn_no_access-515bf7cc3f5ec99df1fa.css" crossorigin="anonymous"><script>(function() {
          function insert(n, m) {
            for (var e = document.createDocumentFragment(), t = 0; t < n.length; t++) {
              var o = document.createElement("script");
              o.src = n[t];
              o.crossOrigin = "anonymous";
              if (m[t]) {
                o.integrity = m[t];
              }
              o.async = !1;
              e.appendChild(o);
            }
            document.head.appendChild(e);
          }
          function loadScript(n, m) {
            if (window.__XRenderResourcesLoader__) {
              window.__XRenderResourcesLoader__.loadScripts(n, m, true);
            } else {
              insert(n, m);
            }
          }
          function scriptDelayLoad(n, m) {
            (window.requestAnimationFrame || window.webkitRequestAnimationFrame || setTimeout)(function() {
                loadScript(n, m)
            })
          }
          function start() {
            loadScript(
            ["https://static.kwcdn.com/m-assets/assets/js/react_webpack_runtime_9083d71bdb4dc3eec913.js","https://static.kwcdn.com/m-assets/assets/js/biz_vendors_f4e75aac6cb036ead7ab.js","https://static.kwcdn.com/m-assets/assets/js/vendors_bfa2989f99da5d7ba367.js","https://static.kwcdn.com/m-assets/assets/js/bgn_no_access_0e4981134c97478a8ee4.js"],
            [null,null,null,null]
            );
          }

          document.addEventListener('DOMContentLoaded', start);
      }());</script><script>!function(){String.prototype.includes||(String.prototype.includes=function(e,n){return"number"!=typeof n&&(n=0),!(n+e.length>this.length)&&-1!==this.indexOf(e,n)});var e=/\s(temu|[a-z]h{2})_ios_version\//i.test(navigator.userAgent),n=/\s(temu|[a-z]h{2})_android_version\//i.test(navigator.userAgent)||e;function t(e,n){if(!n)return e;for(var t in n)e[t]=n[t];return e}function o(e,n){var o,r,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=new Error(n);a.errorCode=e,a.payload=(t(o={message:n},i),t(o,r),o),a.type=501,setTimeout((function(){throw a}),0)}var r=navigator.userAgent,i=/\s(temu|[a-z]h{2})_ios_version\//i.test(r),a=/\s(temu|[a-z]h{2})_android_version\//i.test(r)||i,d=r.indexOf("pkg_type/mini")>-1;function s(){var e="__js_error",t=location.search;t?t+="&"+e:t="?"+e,function(e){function t(){location.replace(e)}n&&window.pinbridge?window.pinbridge.callNative("WebScene","reload",{url:e},(function(){}),t):t()}(location.protocol+"//"+location.host+location.pathname+t+location.hash)}var c=null;function u(){c||(a?(window.pinbridge&&window.pinbridge.callNative("JSUIControl","showLoading"),c=!0):((c=document.createElement("div")).classList.add("g-error-loading"),function(){var e=document.createElement("style");e.type="text/css",e.id="g-error-loading-style";var n=document.createTextNode('\n  .g-error-loading{\n    position:absolute;\n    width: 26px;\n    height: 26px;\n    margin:auto;\n    left:0;\n    right:0;\n    top:0;\n    bottom:0;\n    background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADMAAAAzCAMAAAANf8AYAAAA6lBMVEVHcEx/f39+fn58fHyYmJiOjo5+fn6oqKh8fHx8fHx8fHx8fHx8fHy9vb3Z2dl8fHx8fHx8fHx8fHx+fn58fHx8fHx8fHx/f3+fn5+fn598fHzd3d18fHx8fHzk5OTb29vCwsKsrKzk5OSbm5uwsLB8fHy5ubm5ubng4OCbm5vk5OTg4OCZmZnIyMiOjo7T09PT09OwsLCfn5+wsLCwsLDNzc3T09PT09PT09Pk5OSOjo6wsLDCwsKwsLCfn5+fn5+fn5+wsLCwsLCFhYWMjIzb29vk5OR8fHzCwsLT09OwsLCfn5+Ojo7k5OQEpuvdAAAAR3RSTlMAXSn8CewbBOQS7qf4Eil90bU6U5y/bEPjhY/E89yTFtImbh+mwoY02TfsTWNE/JU5Q8r2b2rxf7780Ofrvbqm9VbX5o3zsj+Qs8wAAAKTSURBVEjHxZRZe6owEIYjIKC44S7uWndttVarbU+3i0ZF/v/fOZkkKAjxqVf9LjRO8maSLzMiJFAsG0M3KpfAiciNjI4x1n+1Uj2dB4PcH4oqJBQTJ4ohTK6Ge6LLpckyWQowsTgZ5gVMHtYVAow3HFCJLsxdMJIMw6LoQibM1hU/04NRRhMxUgLm0z4mR0clsdcWzNdUD6PAQ2HzyvtQi6hH7puClzghBd/FkDPs5qhI91dZ7ZCYUoPfFpvM9uRCzPMu2KR7aRn39KxGVfip04WqAWODMwW6uWzBVEl2nTvPQa0qaerPqQZpHqJ4kThaMixvnShpA5CIzte4eRRL5pGMFF66Bewi5/0kk8fq4reGLbP+HquzsCJqJnKVQA9qRbC1JyzehBXWDmpeN8ObK2bohoT+QEXXevnCFl9rvdwfmaJTYjM+Sxd1iRY9nnQv8f7gEv2/qEePcrx1sa+YgvLm6ZJSqse5rtxn9BJ1FfkL07VOtZEMn6m8V0JnJkPHcX5CmYpt2+VpIJyqOqBtaF2XbdDjyBdsNrYUccaheR5tpo/uOdYZOxyZhDtd5tBnxa2UDie2YEHqeeU9n/bVH5Dur3xyqsLj7CrOTwrutd7vN56yuzscDgvy3f1gTJnHG0AM2bE2e6I5zQAfSYIcvulwRG/17jowdMYdtvcEkD1JOGjtWuRQySVAT2zdtGyX22en+RWUISAbMmrtdrsW+e4Ds+QPqnVDmuXZTYN2IDjcK0B34upprgF5QB4GPQFzGAiZB0DWSR+jfQPzT4TM6clWyMegBU30dS1NVbtg6BMJE1EHZuiSaS+vuNCscgP8DNjw2hZ23TyFQhjUXii/ado3grzd2Oh9wvRv/XeYrWaiqf+Ql69wUsDZfAAAAABJRU5ErkJggg==");\n    background-size:100%;\n    animation:cuiSpinner .9s linear infinite\n  }\n  @keyframes cuiSpinner{\n    0% {\n      transform:rotateZ(0deg)\n    }\n    50% {\n      transform:rotateZ(180deg)\n    }\n    100% {\n      transform:rotateZ(360deg)\n    }\n  }\n');e.appendChild(n),document.head.appendChild(e)}(),document.body.appendChild(c)))}function l(){if(a)window.pinbridge&&window.pinbridge.callNative("JSUIControl","hideLoading"),c=!1;else{if(c){var e,n;null===(e=c.parentElement)||void 0===e||e.removeChild(c);var t=document.querySelector(".g-error-loading");t&&(null===(n=t.parentElement)||void 0===n||n.removeChild(t)),(r=document.querySelector("#g-error-loading-style"))&&(null===(o=r.parentElement)||void 0===o||o.removeChild(r))}c=null}var o,r}(function(){try{return window.self!==window.top}catch(e){return!0}})()||function(){if(!window.__REMOVE_PAGE_JS_RESOURCE_LISTENER__){var e,n,t,r=function(){document.removeEventListener("click",r),window.__REMOVE_PAGE_JS_RESOURCE_LISTENER__||(e=setTimeout((function(){u()}),500),n=setTimeout((function(){t=setTimeout((function(){!function(){if(a&&window.pinbridge){if(d)return void window.pinbridge.callNative("TMAlert","showToast",{message:"Oops! Something went wrong. An unexpected error occurred. Please refresh the page.",type:1});window.pinbridge.callNative("JSAlert","showAlert",{title:"Oops! Something went wrong",text:"An unexpected error occurred. Please refresh the page.",ok_label:"Refresh the page",canceled_on_touch_outside:!1,show_close_button:!0},(function(e){0===e.index&&s()}))}else window.confirm("Oops! Something went wrong. An unexpected error occurred. Please refresh the page.")&&s()}()}),100),l(),o(70001,"PageResourceErrorEvent")}),1e4))};document.addEventListener("click",r),window.addEventListener("removePageJSResourceErrorEventListener",(function(){document.removeEventListener("click",r),clearTimeout(e),clearTimeout(n),clearTimeout(t),l()}))}}()}();
</script><script retain-in-offline="true">!function(t){var n=t._plt=t._plt||[];n.push(["tm","fp",+new Date]),document.addEventListener("DOMContentLoaded",function(){n.push(["tm","dr",+new Date])})}(window);</script><script>window.__InitialLanguage__='zh-Hans';
        window.__InitialI18nStore__={"zh-Hans":{"bgn_no_access":{"pageTitle":"Temu | 没有互联网连接","desc":"无法连接到互联网。请切换到其他网络。","title":"没有网络连接"},"global":{"failFetchUser":"无法获取用户信息","paramError":"页面参数错误","Back":"后退","systemBusy":"系统繁忙，请稍后重试","confirmButtonLabel":"确定","reload":"重新加载","loadErrorText":"你的网络有问题。"},"bec-fe.bg-cui-empty":{"server_problem":"有问题。","high_volume_of_visitors":"我们目前有大量访客","check_network":"请检查您的网络连接。","try_again":"再试一次","empty":"查询无结果","no_connection":"无连接","wait_and_try_again":"请稍等片刻，然后重试。"},"bec-fe.svg-icons-h5":{"aria-label":{"NavBack":"后退","NavShare":"分享","PopupClose":"关闭","CommonSearchThin":"搜索","ProductListMore":"更多","CommonQuestion":"问题","CommonDelete":"删除","AlertsClose":"关闭"}},"bec-fe.cookie-prompt-new":{"ptAds":"Pinterest 广告","bgAds":"Bing 广告","apAds":"苹果广告","systemBusy":"系统繁忙，请稍后重试。","wd_title":"隐私和 cookie 政策","fbEvt":"Facebook 活动","fbAds":"Facebook ads","fbsAnlys":"Google Firebase 分析","opAds":"Opera ads","ttAds":"TikTok 广告","ggAds":"Google 广告","idAds":"Index ads","customize_button_text":"自定义 Cookie","adj":"调整","reject_button_text":"全部拒绝","scAds":"Snapchat 广告","accept_button_text":"全都接受","stAds":"Startio ads","pmAds":"Pubmatic ads","obAds":"Outbrain 广告","vgAds":"Vungle ads","Collapse":"折叠","omit_text":"查看更多","Expand":"展开","tblAds":"Taboola ads","rich_text":[{"text":"<span>我们使用 Cookie 和类似技术为您提供服务、提供最佳体验、改进和宣传服务、确保服务对用户安全可靠以及衡量广告活动的有效性。如果您选择“全部接受 (Accept All)”，即表示您同意我们和我们的合作伙伴在您的设备上存储 Cookie 和类似技术用于广告目的。您也可以点击下方的“自定义 Cookie”或随时在您的隐私设置中“拒绝所有 (Reject All)”非必要的 Cookie或选择您希望接受或禁用的 Cookie 类型。有关详细信息，请参阅我们的</span><span><a href=\"cookiePrivacyLink\">Cookie 和类似技术政策</a>。</span>","isHtml":true,"cookiePrivacyLink":"/cookie-and-similar-technologies-policy.html?title=Temu%20%7C%20Cookie%20and%20Similar%20Technologies%20Policy"}],"view_detail":{"wd_title":"Cookie 偏好","rich_text":[{"text":"<span>您可以更改您的 Cookie 设置以接受或拒绝以下某些 Cookie 和类似技术。请记住，您可以随时在隐私设置中自定义 Cookie 设置。要了解更多有关我们使用的 Cookie 的信息，请参阅我们的 </span><span><a href=\"cookiePrivacyLink\">Cookie 和类似技术政策</a>。</span>","isHtml":true,"cookiePrivacyLink":"/cookie-and-similar-technologies-policy.html?title=Temu%20%7C%20Cookie%20and%20Similar%20Technologies%20Policy"}],"accept_button_text":"确认我的选择","essential_cookie":{"title":"必要 Cookie","rich_text":"始终开启","inner_name":"essential","content_text_list":[{"text":"<span>这些 Cookie 是我们提供服务所必需的。例如，我们使用 Cookie 来显示与您的语言和地区相关的产品、确保服务的安全、区分用户以及支持我们网站和应用程序的各种功能。有关详细信息，请参阅我们的</span><span><a href=\"cookiePrivacyLink\">Cookie 和类似技术政策</a>。</span>","isHtml":true,"cookiePrivacyLink":"/cookie-and-similar-technologies-policy.html?title=Temu%20%7C%20Cookie%20and%20Similar%20Technologies%20Policy"}]},"advertising_cookie":{"title":"广告 Cookie","content_text_list":[{"text":"<span>这些 Cookie 用于向您提供个性化广告，包括适合您所在地区和语言的广告。这些 Cookie 还用于跟踪向您展示的广告，并协助进行广告衡量和报告。我们同时使用第一方广告 Cookie 和第三方 Cookie。广告 Cookie 是可选的。有关详细信息，请参阅我们的</span><span><a href=\"cookiePrivacyLink\">Cookie 和类似技术政策</a>。</span>","isHtml":true,"cookiePrivacyLink":"/cookie-and-similar-technologies-policy.html?title=Temu%20%7C%20Cookie%20and%20Similar%20Technologies%20Policy"}],"inner_name":"advertising"},"first_party_advertising_cookie":{"title":"第一方广告 Cookie","inner_name":"firstPAds"},"third_party_advertising_cookie":{"title":"第三方广告Cookie","content_text_list":[{"text":"<span>定制第三方广告 Cookie：您可以允许第三方向您展示广告，并测算和优化这些广告的效果。</span>","isHtml":true}],"inner_name":"thirdPAds"},"systemBusy":"系统繁忙，请稍后重试。"}},"bec-fe.bottom-sheet":{"cancel":"取消","back":"后退","close":"关闭"}}};
        window.__SumerCdnInfo__=undefined;
        window.__InitialI18nStoreLoaded__=true;document.dispatchEvent(new Event('__InitialI18nStoreLoaded__'));</script><script>!function(){String.prototype.includes||(String.prototype.includes=function(e,t){return"number"!=typeof t&&(t=0),!(t+e.length>this.length)&&-1!==this.indexOf(e,t)});var e=/\s(temu|[a-z]h{2})_ios_version\//i.test(navigator.userAgent),t=/\s(temu|[a-z]h{2})_android_version\//i.test(navigator.userAgent)||e;function o(e,t){if(!t)return e;for(var o in t)e[o]=t[o];return e}function n(e,t){var n,i,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=new Error(t);a.errorCode=e,a.payload=(o(n={message:t},r),o(n,i),n),a.type=501,setTimeout((function(){throw a}),0)}!function(){var e="chunkTimeoutReload",o=+window.__CHUNK_RELOAD_TIMOUT__||5e3,i="__torl";try{if(t?location.href.indexOf(i)>0:sessionStorage.getItem(e))return n(70002,"chunkTimeoutReloaded"),void(!t&&sessionStorage.removeItem(e))}catch(e){}setTimeout((function(){if(!document.querySelector("#main")){if(!t)try{sessionStorage.setItem(e,1)}catch(e){}var o=location.search;o+=o?"&":"?",o+="__csr=1&"+i,function(e){function o(){location.replace(e)}t&&window.pinbridge?window.pinbridge.callNative("WebScene","reload",{url:e},(function(){}),o):o()}(location.protocol+"//"+location.host+location.pathname+o+location.hash)}}),o)}()}();
</script><script retain-in-offline="true">!function(t){var n=t._plt=t._plt||[];n.push(["tm","fp",+new Date]),document.addEventListener("DOMContentLoaded",function(){n.push(["tm","dr",+new Date])})}(window);</script><script src="https://static.kwcdn.com/m-assets/assets/js/react_webpack_runtime_9083d71bdb4dc3eec913.js" crossorigin="anonymous"></script><script src="https://static.kwcdn.com/m-assets/assets/js/biz_vendors_f4e75aac6cb036ead7ab.js" crossorigin="anonymous"></script><script src="https://static.kwcdn.com/m-assets/assets/js/vendors_bfa2989f99da5d7ba367.js" crossorigin="anonymous"></script><script src="https://static.kwcdn.com/m-assets/assets/js/bgn_no_access_0e4981134c97478a8ee4.js" crossorigin="anonymous"></script></head><body><div id="main"><script retain-in-offline="true">"use strict";!function(){var i,o,e,t="__CUI_IMAGE_FAST_SHOW_SCRIPT__";function n(e,t){for(var n=0;n<e.length;n++)t(e[n])}function r(e){return e.complete?e.naturalHeight<=1&&e.naturalWidth<=1?"error":"succ":"loading"}function c(e){e.dataset.state="succ",e.getAttribute("data-did-mount")||e.setAttribute("data-load-finish-time",Date.now())}function l(e){e=e.target;e.removeEventListener("load",l),"succ"===r(e)&&c(e)}function d(t){var e,n,d,a;t.__handled__||(t.__handled__=!0,!t.src)||"1"!==t.getAttribute("data-cui-image")||t.handledByInline||t.getAttribute("data-did-mount")||(t.setAttribute("data-load-start-time",String(Date.now())),"succ"===(e=r(t))?c(t):"loading"===e&&(!o&&window[i]&&window[i].handleImgRetry?(n=!(t.handledByInline=!0),d=function(){n||(t.dataset.state="fail",t.dispatchEvent(new Event("finalerror")))},a=function(){n||(c(t),t.dispatchEvent(new Event("finalload")))},window[i].handleImgRetry(t).then(function(e){n||(t.src=e,"succ"===r(t)?a():(t.addEventListener("load",a),t.addEventListener("error",d)))},d),t.cancelInline=function(){n=!0,delete t.handledByInline,delete t.cancelInline}):t.addEventListener("load",l)))}function a(){o=!0,e.disconnect(),e=null}window[t]||(window[t]=!0,o=!(i="__XRenderResourcesLoader__"),t=document.documentElement,(e=new MutationObserver(function(e){e.forEach(function(e){"childList"===e.type&&n(e.addedNodes,function(e){1===e.nodeType&&("IMG"===e.tagName?d(e):n(e.querySelectorAll("img"),d))})})})).observe(t,{childList:!0,subtree:!0}),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",a):a())}();</script><div class="container-_SXW7"><script>

</script><div class="emptyStates-2UzfS emptyWWrap-FlHGD"><div class="image-sbUHa"><svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M875.1 359.5l7.3 4.5c8.8 5.5 17.4 11.2 25.9 17.1l8.5 6 7 4.9-9.9 13.9-7-4.9c-8.3-5.9-16.6-11.5-25.1-17.1l-8.5-5.4-7.3-4.6 9.1-14.4z m-727-1l9.5 14.1-7 4.8c-8.8 6-17.9 12.3-27.5 19.2l-5.8 4.2-6.9 5-10-13.8 6.9-5c9.9-7.2 19.3-13.8 28.3-20l12.5-8.5z m644.5-42.5l7.8 3.6c9.4 4.3 18.7 8.9 27.9 13.6l9.2 4.8 7.5 4-8 15.1-7.5-4c-8.9-4.8-18-9.3-27.1-13.7l-9.2-4.3-7.7-3.6 7.1-15.5z m-563.7-4.1l7.6 15.2-7.6 3.8c-9.4 4.7-18.9 9.8-28.3 15.2l-7.2 4.2-7.3 4.3-8.7-14.7 7.4-4.4c9.8-5.7 19.5-11.1 29.2-16.1l7.3-3.7 7.6-3.8z m476.2-28.2l8.1 2.5c9.9 3.1 19.8 6.3 29.6 9.8l9.7 3.6 8 3-6 16-8-3c-9.5-3.5-19-6.9-28.6-10l-9.7-3.1-8.1-2.5 5-16.3z m-388.6-5l4.9 16.4-8.2 2.4c-10.4 3.1-20.6 6.5-30.6 10.2l-7.5 2.8-8 3.1-6.1-15.9 7.9-3.1c10.3-4 20.7-7.6 31.4-11l8-2.5 8.2-2.4z m314.9-12.4l8.3 1.6c5.2 1 10.3 2 15.5 3.2l7.7 1.7 8.3 1.9-3.8 16.6-8.3-1.9c-5-1.1-10-2.2-15-3.2l-7.6-1.5-8.4-1.7 3.3-16.7z m-223.1-5.6l2.2 17-8.4 1.1c-11 1.5-21.7 3.2-32.1 5.1l-7.8 1.5-8.4 1.6-3.3-16.7 8.3-1.7c10.6-2.1 21.5-4 32.6-5.6l8.4-1.1 8.5-1.2z" fill="#cdcdcd" stroke="none" stroke-width="8.533333333333333"></path><path d="M779.6 508.9l7.1 4.8c9.4 6.3 18.5 12.9 27.2 19.6l6.4 5.1 6.6 5.4-10.7 13.2-6.6-5.3c-8.2-6.6-16.7-13-25.7-19.3l-6.7-4.6-7.1-4.7 9.5-14.2z m-542.8 4.5l9.9 13.9-6.9 4.9c-6.5 4.6-12.9 9.4-19.1 14.3l-6 5-6.6 5.4-10.9-13.1 6.6-5.4c6.3-5.2 12.7-10.3 19.4-15.2l6.7-4.9 6.9-4.9z m82.3-45.2l7 15.6-7.8 3.5c-9.8 4.4-19.5 9.2-28.9 14.2l-7 3.9-7.5 4.1-8.3-14.9 7.5-4.2c9.7-5.4 19.6-10.5 29.6-15.2l7.6-3.5 7.8-3.5z m377.3-2.5l7.9 3.3c10.3 4.4 20.3 9.1 30.2 14.1l7.4 3.9 7.5 3.9-8 15.1-7.5-4c-9.4-5-19.1-9.7-28.9-14l-7.5-3.3-7.8-3.4 6.7-15.6z m-288-26.2l3.9 16.6-8.3 2c-10.5 2.5-20.8 5.3-31 8.5l-7.7 2.5-8.1 2.6-5.3-16.2 8.1-2.7c10.5-3.5 21.2-6.6 32-9.3l8.1-2 8.3-2z m198.4-1.3l8.3 1.9c10.8 2.4 21.6 5.2 32.3 8.4l8 2.4 8.1 2.6-5.1 16.2-8.2-2.5c-10.3-3.2-20.6-6.1-31-8.7l-7.8-1.8-8.3-1.9 3.7-16.6z" fill="#cdcdcd" stroke="none" stroke-width="8.533333333333333"></path><path d="M690.5 661.1l6.8 5c9.9 7.3 19.1 15.1 27.8 23.6l4.2 4.2 6 6.1-12.2 11.9-6-6.1c-7.8-8-16.3-15.5-25.3-22.5l-4.6-3.4-6.9-5.1 10.2-13.7z m-345.1-6.4l9.5 14.1-7.1 4.8c-9.4 6.3-18.4 13.3-26.8 20.8l-4.2 3.8-6.2 5.9-11.7-12.5 6.3-5.8c8.9-8.3 18.3-15.9 28.1-22.9l5-3.5 7.1-4.7z m259.2-33.7l8.1 2.5c11.5 3.5 22.9 7.8 33.9 12.9l5.5 2.5 7.7 3.7-7.4 15.4-7.7-3.7c-10.2-4.9-20.8-9.2-31.6-12.7l-5.4-1.7-8.1-2.5 5-16.4z m-171.2-1.8l4 16.6-8.3 2c-11 2.6-21.9 6-32.4 10.2l-5.3 2.1-7.9 3.3-6.5-15.8 7.8-3.3c11.2-4.7 22.6-8.5 34.4-11.6l5.9-1.5 8.3-2z" fill="#cdcdcd" stroke="none" stroke-width="8.533333333333333"></path><path d="M512 181.8c-1.7 0-3.4 0.1-5.1 0.2-42.1 2.8-74 39.2-71.2 81.3l24.8 373.8c1.8 27.1 24.3 48.2 51.5 48.2 27.2 0 49.7-21.1 51.5-48.2l24.8-373.8c0.1-1.7 0.2-3.4 0.1-5 0-42.2-34.2-76.4-76.4-76.5z m0 17.1c32.8 0 59.4 26.6 59.4 59.4 0 1.3 0 2.6-0.2 3.9l-24.8 373.8c-1.2 18.1-16.3 32.2-34.4 32.2-18.2 0-33.2-14.1-34.4-32.2l-24.8-373.8c-2.2-32.7 22.6-61 55.3-63.2 1.3-0.1 2.6-0.1 3.9-0.1z" fill="#cdcdcd" stroke="none" stroke-width="8.533333333333333"></path><path d="M512 731.2c-30.6 0-55.5 24.8-55.5 55.5 0 30.6 24.8 55.5 55.5 55.5 30.6 0 55.5-24.8 55.5-55.5 0-30.6-24.8-55.5-55.5-55.5z m0 17.1c21.2 0 38.4 17.2 38.4 38.4 0 21.2-17.2 38.4-38.4 38.4-21.2 0-38.4-17.2-38.4-38.4 0-21.2 17.2-38.4 38.4-38.4z" fill="#cdcdcd" stroke="none" stroke-width="8.533333333333333"></path></svg></div><div class="title-2Hu7q title-3OBKm">没有网络连接</div><div class="desc-2mVRY desc-3G_es">无法连接到互联网。请切换到其他网络。</div><div class="buttonWrapper-3xGS-"></div></div><script retain-in-offline="true">!function(e,t){var n=e._plt=e._plt||[];n.fs=n.fs||!0;var i=+new Date;n.push(["tm","fsn",i]);var r=t.getElementsByTagName("img"),m=[],s=function(){for(var e=t.documentElement.clientHeight,r=t.documentElement.clientWidth,s=0;s<m.length;s++){var o=m[s],a=o.img.getBoundingClientRect(),g=a.top||0,h=a.left||0;g+o.img.height>0&&e>g&&h+o.img.width>0&&r>h&&a.width&&a.height&&(i=Math.max(o.time,i))}n.push(["tm","fs",i]),t.dispatchEvent(new Event("FsImgsLoaded"))},o=0;function a(){m.length&&o<m.length||s()}for(var g=0;g<r.length;g++){var h=r[g];!h.complete&&h.src&&function(){var e={img:h},t=function t(){this.removeEventListener("load",t,!1),this.removeEventListener("error",t,!1),e.time=+new Date,o++,a()};h.addEventListener("load",t,!1),h.addEventListener("error",t,!1),m.push(e)}()}a(),e.__fsImgItems=m,e.__fsImgSrcs=m.map(function(e){return e.img.src})}(window,document);</script></div><script>window.__ServerRenderSuccess__=undefined;</script></div><script>
(function() {
var userAgent = navigator.userAgent;
if(/\s(temu|[a-z]h{2})_(ios|android)_version\//i.test(userAgent)) {
    window.pinbridge && window.pinbridge.callNative('JSUIControl', 'hideLoading');
}
})();</script></body></html>