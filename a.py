import requests
import json
from datetime import datetime
import time
import traceback
import pytz
import os
import logging
import random  # 添加random模块用于随机延迟
# import subprocess  # 不再需要，已改为自动化处理

# 导入配置
from config import (
    TEMU_API_CONFIG,
    BACKEND_API_CONFIG,
    TIMEZONE_CONFIG
)

# 全局变量用于存储浏览器获取的信息
BROWSER_USER_AGENT = None
BROWSER_VERIFY_TOKEN = None

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"temu_crawler_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logging.info(f"日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

# 配置
BASE_API_URL = "http://172.25.165.28:8055"
API_TOKEN = "OppexW5M7FRYT3VQHT3EQx8x3Ly6k2ZM"

# 新增随机延迟函数
def random_delay(min_delay=1, max_delay=3):
    """随机延迟一段时间，模拟人类行为"""
    delay = min_delay + random.random() * (max_delay - min_delay)
    logging.info(f"随机延迟 {delay:.2f} 秒")
    time.sleep(delay)
    return delay

# 辅助函数 - 确保在被调用前定义
def convert_to_int(value):
    """将各种格式的值转换为整数"""
    if value is None:
        return 0
    
    if isinstance(value, int):
        return value
    
    if isinstance(value, float):
        return int(value)
    
    if isinstance(value, str):
        return extract_number_from_string(value)
    
    return 0

def extract_number_from_string(text):
    """从字符串中提取数字，处理带有'万'等单位的情况"""
    if not text or not isinstance(text, str):
        return 0
    
    try:
        # 处理带有"万"的情况
        if "万" in text:
            # 提取数字部分
            import re
            numbers = re.findall(r'[\d.]+', text)
            if numbers:
                # 将"万"转换为实际数字（乘以10000）
                return int(float(numbers[0]) * 10000)
        
        # 处理普通数字
        import re
        numbers = re.findall(r'\d+', text)
        if numbers:
            return int(numbers[0])
        
        return 0
    except Exception as e:
        logging.error(f"从字符串'{text}'提取数字时出错: {str(e)}")
        return 0

# 获取香港时区的当前时间
def get_hk_time():
    """获取香港时区的当前时间"""
    hk_timezone = pytz.timezone(TIMEZONE_CONFIG['DEFAULT_TIMEZONE'])
    return datetime.now(hk_timezone).strftime('%Y-%m-%d %H:%M:%S')

def get_headers():
    """返回带有认证令牌的API请求头"""
    return {
        "Authorization": "Bearer " + BACKEND_API_CONFIG['API_TOKEN'],
        "Content-Type": "application/json"
    }

# 从cookie.json文件加载cookie
def load_cookies_from_file():
    """从cookie.json文件加载cookie信息"""
    try:
        cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        logging.info(f"尝试从 {cookie_file_path} 加载cookie")

        if not os.path.exists(cookie_file_path):
            logging.warning(f"警告: cookie文件不存在: {cookie_file_path}")
            return None

        with open(cookie_file_path, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)

        # 将cookie列表转换为字符串格式
        cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_data if cookie.get('name') and cookie.get('value')])

        # 设置默认User-Agent
        global BROWSER_USER_AGENT, BROWSER_VERIFY_TOKEN
        BROWSER_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36"

        # 尝试从cookie中获取verifyAuthToken
        BROWSER_VERIFY_TOKEN = None
        for cookie in cookies_data:
            if cookie['name'] == 'verifyAuthToken':
                BROWSER_VERIFY_TOKEN = cookie['value']
                break

        logging.info(f"成功加载cookie，包含 {len(cookies_data)} 个条目")
        if BROWSER_VERIFY_TOKEN:
            logging.info(f"🔑 获取到verifyAuthToken: {BROWSER_VERIFY_TOKEN[:20]}...")

        return cookie_str
    except Exception as e:
        logging.error(f"加载cookie文件时出错: {str(e)}")
        traceback.print_exc()
        return None

# 加载cookie
# 删除这两行，不要在全局初始化 cookie
# TEMU_COOKIES = load_cookies_from_file()
# if not TEMU_COOKIES:
#     logging.info("使用默认cookie")
#     TEMU_COOKIES = "none"

def update_cookies_if_needed():
    """提示用户手动更新cookie.json文件"""
    try:
        logging.warning("⚠️ Cookie可能已过期，需要手动更新...")

        print("\n" + "="*60)
        print("🔄 Cookie需要更新！")
        print("请手动完成以下步骤：")
        print("1. 手动获取新的cookie")
        print("2. 将新的cookie保存为 cookie.json 文件")
        print("3. 确保文件格式正确后，重新运行程序")
        print("="*60)

        logging.info("程序将退出，请更新cookie.json后重新运行")
        return False  # 返回False，让程序退出

    except Exception as e:
        logging.error(f"❌ 处理cookie更新时出错: {str(e)}")
        traceback.print_exc()
        return False

def get_temu_headers():
    """返回Temu API请求的请求头（增强版）"""
    # 每次调用时重新加载cookie
    fresh_cookies = load_cookies_from_file()
    if not fresh_cookies:
        logging.warning("警告: 无法获取cookie，尝试更新cookie")

        # 尝试更新cookie
        if update_cookies_if_needed():
            logging.info("Cookie更新成功，重新加载cookie")
            fresh_cookies = load_cookies_from_file()
            if not fresh_cookies:
                logging.error("Cookie更新后仍然无法获取有效cookie")
                return None
        else:
            logging.error("Cookie更新失败")
            return None

    # 构建增强的headers
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
        "content-type": "application/json;charset=UTF-8",
        "origin": "https://www.temu.com",
        "priority": "u=1, i",
        "referer": "https://www.temu.com/",
        "sec-ch-ua": '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "Cookie": fresh_cookies
    }

    # 添加从浏览器获取的额外信息
    global BROWSER_USER_AGENT, BROWSER_VERIFY_TOKEN
    if 'BROWSER_USER_AGENT' in globals() and BROWSER_USER_AGENT:
        headers["user-agent"] = BROWSER_USER_AGENT
    else:
        headers["user-agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36"

    if 'BROWSER_VERIFY_TOKEN' in globals() and BROWSER_VERIFY_TOKEN:
        headers["verifyauthtoken"] = BROWSER_VERIFY_TOKEN
        logging.info("✅ 添加verifyAuthToken到headers")

    logging.info(f"生成增强Temu请求头: {len(headers)} 个字段")
    return headers

def fetch_temu_shop_data(mall_id):
    """从Temu API获取商店和产品数据"""
    url = TEMU_API_CONFIG['BASE_URL'] + TEMU_API_CONFIG['API_PATH']
    payload = {
        "mallId": mall_id,
        "mainGoodsIds": ["1"],
        "source_page_sn": "10013",
        "mall_id": mall_id,
        "main_goods_ids": ["1"],
        "filter_items": "",
        "page_number": 1,
        "page_size": TEMU_API_CONFIG['PAGE_SIZE'],
        "list_id": "r7oe7gyw0vd5xo2z2qja2",
        "scene_code": "mall_rule",
        "page_sn": 10040,
        "page_el_sn": 201265,
        "source": 10018,
        "anti_content": "1"
    }
    
    logging.info(f"请求Temu数据: {url}, mall_id: {mall_id}")
    
    # 添加随机延迟，模拟人类行为
    random_delay(TEMU_API_CONFIG['REQUEST_DELAY_MIN'], TEMU_API_CONFIG['REQUEST_DELAY_MAX'])
    
    headers = get_temu_headers()
    if not headers:
        logging.error("无法获取有效的请求头，跳过请求")
        return None
        
    try:
        response = requests.post(url, headers=headers, json=payload)
        logging.info(f"Temu API状态码: {response.status_code}")

        if response.status_code != 200:
            logging.error(f"获取mall_id为{mall_id}的Temu数据时出错: {response.status_code}")
            logging.error(f"返回内容: {response.text[:500]}...")

            # 如果是429错误（请求频率过高），使用指数退避策略重试
            if response.status_code == 429:
                max_retries = 3
                base_delay = 60  # 基础延迟60秒

                for retry_count in range(max_retries):
                    delay = base_delay * (2 ** retry_count)  # 指数退避：60, 120, 240秒
                    logging.warning(f"遇到429错误（请求频率过高），第{retry_count + 1}次重试，等待{delay}秒...")
                    time.sleep(delay)

                    # 重试请求
                    logging.info(f"第{retry_count + 1}次重试请求...")
                    retry_response = requests.post(url, headers=headers, json=payload)
                    logging.info(f"第{retry_count + 1}次重试后的Temu API状态码: {retry_response.status_code}")

                    if retry_response.status_code == 200:
                        result = retry_response.json()
                        logging.info(f"第{retry_count + 1}次重试成功！")
                        return result
                    elif retry_response.status_code != 429:
                        # 如果不是429错误，停止重试
                        logging.error(f"重试后遇到其他错误: {retry_response.status_code}")
                        break

                logging.error(f"经过{max_retries}次重试仍然失败，跳过此商店")
                return None

            # 如果是401或403错误，可能是cookie过期，尝试更新cookie
            elif response.status_code in [401, 403]:
                logging.warning("可能是cookie过期，尝试更新cookie...")
                if update_cookies_if_needed():
                    logging.info("Cookie更新成功，重试请求...")
                    # 重新获取headers并重试
                    new_headers = get_temu_headers()
                    if new_headers:
                        retry_response = requests.post(url, headers=new_headers, json=payload)
                        logging.info(f"重试后的Temu API状态码: {retry_response.status_code}")
                        if retry_response.status_code == 200:
                            result = retry_response.json()
                            return result
                        else:
                            logging.error(f"重试后仍然失败: {retry_response.status_code}")
                            return None
                else:
                    logging.error("Cookie更新失败")

            return None
        
        result = response.json()
        return result
    except Exception as e:
        logging.error(f"获取Temu数据时发生异常: {str(e)}")
        traceback.print_exc()
        return None

def save_product_data(product_data):
    """将产品数据保存到数据库，如果已存在则更新"""
    if not product_data:
        logging.warning("警告: 产品数据为空，跳过保存")
        return False
    
    goods_id = product_data.get('goods_id')
    mall_id = product_data.get('mall_id')  # 确保从产品数据中获取mall_id
    
    if not goods_id:
        logging.error("错误: 产品数据缺少goods_id，跳过保存")
        return False
    
    logging.info(f"保存/更新产品数据: goods_id={goods_id}")
    
    try:
        # 首先获取shop_data的id
        shop_check_url = f"{BASE_API_URL}/items/shop_data?filter[mall_id][_eq]={mall_id}"
        shop_response = requests.get(shop_check_url, headers=get_headers())
        
        if shop_response.status_code != 200:
            logging.error(f"获取shop_data id时出错: {shop_response.status_code}")
            return False
            
        shop_data_result = shop_response.json()
        if not shop_data_result.get('data') or len(shop_data_result['data']) == 0:
            logging.error(f"未找到对应的shop_data记录: {mall_id}")
            return False
            
        shop_data_id = shop_data_result['data'][0]['id']
        
        # 添加shop_data外键
        product_data['shop_data'] = shop_data_id
        
        # 删除mall_id字段，因为数据库中没有这个字段
        if 'mall_id' in product_data:
            del product_data['mall_id']
        
        # 检查该goods_id是否已存在
        check_url = f"{BASE_API_URL}/items/product_data?filter[goods_id][_eq]={goods_id}"
        check_response = requests.get(check_url, headers=get_headers())
        
        if check_response.status_code != 200:
            logging.error(f"检查产品数据时出错: {check_response.status_code}")
            return False
        
        existing_data = check_response.json()
        
        # 获取旧数据用于日志记录
        old_data = None
        if existing_data and existing_data.get('data') and len(existing_data['data']) > 0:
            old_data = existing_data['data'][0]
            product_id = old_data.get('id')
            
            # 更新现有记录
            update_url = f"{BASE_API_URL}/items/product_data/{product_id}"
            response = requests.patch(update_url, headers=get_headers(), json=product_data)
            
            if response.status_code in [200, 204]:
                logging.info(f"成功更新产品数据: {goods_id}")
                # 记录变更日志
                save_product_log(old_data, product_data)
                return True
            else:
                logging.error(f"更新产品数据时出错: {response.status_code}")
                logging.error(f"返回内容: {response.text}")
                return False
        else:
            # 创建新记录
            create_url = f"{BASE_API_URL}/items/product_data"
            response = requests.post(create_url, headers=get_headers(), json=product_data)
            
            if response.status_code in [200, 201, 204]:
                logging.info(f"成功创建新产品数据: {goods_id}")
                
                # 获取新创建的记录ID
                new_product_id = None
                if response.status_code == 200:
                    new_product_data = response.json()
                    if 'data' in new_product_data and 'id' in new_product_data['data']:
                        new_product_id = new_product_data['data']['id']
                
                # 对于新记录，也记录日志
                if new_product_id:
                    # 为新记录添加ID
                    product_data['id'] = new_product_id
                save_product_log(None, product_data)
                return True
            else:
                logging.error(f"创建产品数据时出错: {response.status_code}")
                logging.error(f"返回内容: {response.text}")
                return False
    except Exception as e:
        logging.error(f"保存产品数据时发生异常: {str(e)}")
        traceback.print_exc()
        return False

def save_shop_log(old_data, new_data):
    """记录商店数据变更日志"""
    if not new_data:
        return False
    
    try:
        # 获取shop_data的id
        shop_data_id = new_data.get('id')
        if not shop_data_id:
            mall_id = new_data.get('mall_id')
            if not mall_id:
                logging.error("错误: 无法获取商店ID，跳过日志记录")
                return False
                
            # 查询shop_data的id
            check_url = f"{BASE_API_URL}/items/shop_data?filter[mall_id][_eq]={mall_id}"
            check_response = requests.get(check_url, headers=get_headers())
            
            if check_response.status_code != 200:
                logging.error(f"获取shop_data id时出错: {check_response.status_code}")
                return False
                
            shop_data_result = check_response.json()
            if not shop_data_result.get('data') or len(shop_data_result['data']) == 0:
                logging.error(f"未找到对应的shop_data记录: {mall_id}")
                return False
                
            shop_data_id = shop_data_result['data'][0]['id']
        
        # 获取当前的销售数量
        new_sales = new_data.get('goods_sales_num', 0)
        
        # 计算变化数据
        order_increase = 0
        growth_rate = 0
        
        if old_data:
            # 如果有旧数据，直接用旧数据计算
            old_sales = old_data.get('goods_sales_num', 0)
            order_increase = new_sales - old_sales
            if old_sales > 0:
                growth_rate = round((order_increase / old_sales) * 100, 2)
        else:
            # 如果没有旧数据，尝试从最近的日志中获取上次的销售数据
            log_query_url = f"{BASE_API_URL}/items/shop_log?filter[shop_data][_eq]={shop_data_id}&sort=-log_time&limit=1"
            log_response = requests.get(log_query_url, headers=get_headers())
            
            if log_response.status_code == 200:
                log_data = log_response.json()
                if log_data.get('data') and len(log_data['data']) > 0:
                    last_log = log_data['data'][0]
                    # 直接从上一条日志中获取销售数据
                    if 'sales_num' in last_log:
                        old_sales = last_log.get('sales_num', 0)
                        order_increase = new_sales - old_sales
                        if old_sales > 0:
                            growth_rate = round((order_increase / old_sales) * 100, 2)
                        logging.info(f"从日志获取历史销售数据: {old_sales}，计算订单增长: {order_increase}")
                    else:
                        logging.warning("警告: 日志中没有sales_num字段，无法计算订单增长")
        
        # 构建日志数据
        log_data = {
            "log_time": new_data.get('update_time'),
            "order_increase": order_increase,
            "growth_rate": growth_rate,
            "sales_num": new_sales,  # 添加销售数量字段
            "shop_data": shop_data_id  # 添加外键关联
        }
        
        # 保存日志
        log_url = f"{BASE_API_URL}/items/shop_log"
        response = requests.post(log_url, headers=get_headers(), json=log_data)
        
        if response.status_code in [200, 201, 204]:
            logging.info(f"成功记录商店变更日志: shop_data_id={shop_data_id}, 订单增长: {order_increase}, 增长率: {growth_rate}%, 当前销售量: {new_sales}")
            return True
        else:
            logging.error(f"记录商店变更日志时出错: {response.status_code}")
            logging.error(f"返回内容: {response.text}")
            return False
    except Exception as e:
        logging.error(f"记录商店变更日志时发生异常: {str(e)}")
        traceback.print_exc()
        return False

def save_product_log(old_data, new_data):
    """记录产品数据变更日志"""
    if not new_data:
        return False
    
    try:
        # 获取product_data的id
        product_data_id = new_data.get('id')
        if not product_data_id:
            goods_id = new_data.get('goods_id')
            if not goods_id:
                logging.error("错误: 无法获取产品ID，跳过日志记录")
                return False
                
            # 查询product_data的id
            check_url = f"{BASE_API_URL}/items/product_data?filter[goods_id][_eq]={goods_id}"
            check_response = requests.get(check_url, headers=get_headers())
            
            if check_response.status_code != 200:
                logging.error(f"获取product_data id时出错: {check_response.status_code}")
                return False
                
            product_data_result = check_response.json()
            if not product_data_result.get('data') or len(product_data_result['data']) == 0:
                logging.error(f"未找到对应的product_data记录: {goods_id}")
                return False
                
            product_data_id = product_data_result['data'][0]['id']
        
        # 获取当前的销售和评论数量
        new_sales = new_data.get('sales_num', 0)
        new_comments = new_data.get('comment', 0)
        
        # 计算变化数据
        order_increase = 0
        order_growth_rate = 0
        comment_increase = 0
        comment_growth_rate = 0
        
        if old_data:
            # 如果有旧数据，直接用旧数据计算
            old_sales = old_data.get('sales_num', 0)
            old_comments = old_data.get('comment', 0)
            
            order_increase = new_sales - old_sales
            if old_sales > 0:
                order_growth_rate = round((order_increase / old_sales) * 100, 2)
            
            comment_increase = new_comments - old_comments
            if old_comments > 0:
                comment_growth_rate = round((comment_increase / old_comments) * 100, 2)
        else:
            # 如果没有旧数据，尝试从最近的日志中获取上次的销售和评论数据
            log_query_url = f"{BASE_API_URL}/items/product_log?filter[product_data][_eq]={product_data_id}&sort=-log_time&limit=1"
            log_response = requests.get(log_query_url, headers=get_headers())
            
            if log_response.status_code == 200:
                log_data = log_response.json()
                if log_data.get('data') and len(log_data['data']) > 0:
                    last_log = log_data['data'][0]
                    # 直接从上一条日志中获取销售和评论数据
                    if 'sales_num' in last_log and 'comment_num' in last_log:
                        old_sales = last_log.get('sales_num', 0)
                        old_comments = last_log.get('comment_num', 0)
                        
                        order_increase = new_sales - old_sales
                        if old_sales > 0:
                            order_growth_rate = round((order_increase / old_sales) * 100, 2)
                        
                        comment_increase = new_comments - old_comments
                        if old_comments > 0:
                            comment_growth_rate = round((comment_increase / old_comments) * 100, 2)
                        
                        logging.info(f"从日志获取历史数据: 销售量={old_sales}, 评论数={old_comments}")
                    else:
                        logging.warning("警告: 日志中没有sales_num或comment_num字段，无法计算增长率")
        
        # 构建日志数据
        log_data = {
            "log_time": new_data.get('update_time'),
            "order_increase": order_increase,
            "order_growth_rate": order_growth_rate,
            "comment_increase": comment_increase,
            "comment_growth_rate": comment_growth_rate,
            "sales_num": new_sales,  # 添加销售数量字段
            "comment_num": new_comments,  # 添加评论数量字段
            "product_data": product_data_id  # 添加外键关联
        }
        
        # 保存日志
        log_url = f"{BASE_API_URL}/items/product_log"
        response = requests.post(log_url, headers=get_headers(), json=log_data)
        
        if response.status_code in [200, 201, 204]:
            logging.info(f"成功记录产品变更日志: product_data_id={product_data_id}, 订单增长: {order_increase}, 评论增长: {comment_increase}, 当前销售量: {new_sales}, 当前评论数: {new_comments}")
            return True
        else:
            logging.error(f"记录产品变更日志时出错: {response.status_code}")
            logging.error(f"返回内容: {response.text}")
            return False
    except Exception as e:
        logging.error(f"记录产品变更日志时发生异常: {str(e)}")
        traceback.print_exc()
        return False

def fetch_shop_ids():
    """从数据库获取所有商店ID并去重"""
    url = f"{BASE_API_URL}/items/shop_data"
    params = {
        "fields": "mall_id"
    }
    
    logging.info(f"请求商店ID列表: {url}")
    try:
        response = requests.get(url, headers=get_headers(), params=params)
        logging.info(f"获取商店ID状态码: {response.status_code}")
        
        if response.status_code != 200:
            logging.error(f"获取商店ID时出错: {response.status_code}")
            logging.error(f"返回内容: {response.text}")
            return []
        
        data = response.json()
        logging.info(f"获取到的商店数据: {json.dumps(data, ensure_ascii=False, indent=2)[:500]}...")
        
        # 从响应数据中提取全部mall_id
        all_shop_ids = [item.get('mall_id') for item in data.get('data', []) if item.get('mall_id')]
        logging.info(f"获取到的全部mall_id数量: {len(all_shop_ids)}")
        
        # 使用集合进行去重
        unique_shop_ids = list(set(all_shop_ids))
        logging.info(f"去重后的mall_id数量: {len(unique_shop_ids)}")
        logging.info(f"去除了 {len(all_shop_ids) - len(unique_shop_ids)} 个重复的mall_id")
        
        # 记录重复的mall_id
        if len(all_shop_ids) > len(unique_shop_ids):
            duplicates = {}
            for shop_id in all_shop_ids:
                if shop_id in duplicates:
                    duplicates[shop_id] += 1
                else:
                    duplicates[shop_id] = 1
            
            repeated_ids = {shop_id: count for shop_id, count in duplicates.items() if count > 1}
            if repeated_ids:
                logging.info(f"以下mall_id有重复记录: {json.dumps(repeated_ids, ensure_ascii=False)}")
        
        logging.info(f"最终使用的商店ID列表: {unique_shop_ids}")
        return unique_shop_ids
    except Exception as e:
        logging.error(f"获取商店ID时发生异常: {str(e)}")
        traceback.print_exc()
        return []

def process_shop_data(result):
    """处理并提取商店数据"""
    current_time = get_hk_time()  # 使用香港时区
    
    logging.info("处理商店数据...")
    
    # 提取商店数据的路径可能不同，尝试多种可能的结构
    shop_data = None
    
    if "result" in result:
        shop_info = result["result"]
        
        # 检查必要字段是否存在
        if "mall_id" in shop_info and "mall_name" in shop_info:
            # 检查必要数据不为空
            if shop_info["mall_id"] and shop_info["mall_name"]:
                # 将数值字段转换为整数
                goods_sales_num = convert_to_int(shop_info.get("goods_sales_num", "0"))
                goods_num = convert_to_int(shop_info.get("goods_num", "0"))
                review_num = convert_to_int(shop_info.get("review_num", "0"))
                
                shop_data = {
                    "update_time": current_time,
                    "mall_id": str(shop_info.get("mall_id", "")),
                    "mall_name": shop_info.get("mall_name", ""),
                    "mall_logo": shop_info.get("mall_logo", ""),
                    "goods_sales_num": goods_sales_num,
                    "goods_num": goods_num,
                    "review_num": review_num
                }
            else:
                logging.warning("警告: 商店数据缺少必要信息（mall_id或mall_name为空）")
        else:
            logging.warning("警告: 商店数据缺少必要字段（mall_id或mall_name）")
    else:
        # 尝试其他可能的数据结构
        if "data" in result and "mall_info" in result["data"]:
            shop_info = result["data"]["mall_info"]
            
            # 检查必要字段是否存在
            if "mall_id" in shop_info and "mall_name" in shop_info:
                # 检查必要数据不为空
                if shop_info["mall_id"] and shop_info["mall_name"]:
                    # 将数值字段转换为整数
                    goods_sales_num = convert_to_int(shop_info.get("goods_sales_num", "0"))
                    goods_num = convert_to_int(shop_info.get("goods_num", "0"))
                    review_num = convert_to_int(shop_info.get("review_num", "0"))
                    
                    shop_data = {
                        "update_time": current_time,
                        "mall_id": str(shop_info.get("mall_id", "")),
                        "mall_name": shop_info.get("mall_name", ""),
                        "mall_logo": shop_info.get("mall_logo", ""),
                        "goods_sales_num": goods_sales_num,
                        "goods_num": goods_num,
                        "review_num": review_num
                    }
                else:
                    logging.warning("警告: 商店数据缺少必要信息（mall_id或mall_name为空）")
            else:
                logging.warning("警告: 商店数据缺少必要字段（mall_id或mall_name）")
    
    # 最终检查，确保所有字段均有值，避免空数据
    if shop_data:
        # 验证所有数据字段
        for key, value in shop_data.items():
            if value is None or (isinstance(value, str) and value == ""):
                logging.warning(f"警告: 字段 {key} 值为空，设置为默认值")
                if key == "update_time":
                    shop_data[key] = current_time
                elif key == "mall_id" or key == "mall_name":
                    logging.error(f"错误: 必要字段 {key} 为空，无法保存商店数据")
                    return None
                elif key in ["goods_sales_num", "goods_num", "review_num"]:
                    shop_data[key] = 0  # 对于数值类型字段，默认值为0
                else:
                    shop_data[key] = ""  # 其他字段默认为空字符串
        
        logging.info(f"处理后的商店数据: {json.dumps(shop_data, ensure_ascii=False)}")
        return shop_data
    else:
        logging.error("错误: 无法从API响应中提取商店数据")
        return None

def save_shop_data(shop_data):
    """将商店数据保存到数据库"""
    if not shop_data:
        logging.warning("警告: 商店数据为空，跳过保存")
        return False
    
    mall_id = shop_data.get('mall_id')
    if not mall_id:
        logging.error("错误: 商店数据缺少mall_id，跳过保存")
        return False
    
    logging.info(f"保存商店数据: mall_id={mall_id}")
    try:
        # 检查该mall_id是否已存在
        check_url = f"{BASE_API_URL}/items/shop_data?filter[mall_id][_eq]={mall_id}"
        check_response = requests.get(check_url, headers=get_headers())
        
        if check_response.status_code != 200:
            logging.error(f"检查商店数据时出错: {check_response.status_code}")
            return False
        
        existing_data = check_response.json()
        
        # 获取旧数据用于日志记录
        old_data = None
        if existing_data and existing_data.get('data') and len(existing_data['data']) > 0:
            old_data = existing_data['data'][0]
            shop_id = old_data.get('id')
            
            # 更新现有记录
            update_url = f"{BASE_API_URL}/items/shop_data/{shop_id}"
            response = requests.patch(update_url, headers=get_headers(), json=shop_data)
            
            if response.status_code in [200, 204]:
                logging.info(f"成功更新商店数据: {mall_id}")
                # 记录变更日志
                save_shop_log(old_data, shop_data)
                return True
            else:
                logging.error(f"更新商店数据时出错: {response.status_code}")
                logging.error(f"返回内容: {response.text}")
                return False
        else:
            # 创建新记录
            create_url = f"{BASE_API_URL}/items/shop_data"
            response = requests.post(create_url, headers=get_headers(), json=shop_data)
            
            if response.status_code in [200, 201, 204]:
                logging.info(f"成功创建新商店数据: {mall_id}")
                
                # 获取新创建的记录ID
                new_shop_id = None
                if response.status_code == 200:
                    new_shop_data = response.json()
                    if 'data' in new_shop_data and 'id' in new_shop_data['data']:
                        new_shop_id = new_shop_data['data']['id']
                
                # 对于新记录，也记录日志
                if new_shop_id:
                    # 为新记录添加ID
                    shop_data['id'] = new_shop_id
                save_shop_log(None, shop_data)
                return True
            else:
                logging.error(f"创建商店数据时出错: {response.status_code}")
                logging.error(f"返回内容: {response.text}")
                return False
    except Exception as e:
        logging.error(f"保存商店数据时发生异常: {str(e)}")
        traceback.print_exc()
        return False

def process_products(result, mall_id):
    """处理和提取产品数据"""
    current_time = get_hk_time()  # 使用香港时区
    
    logging.info("处理产品数据...")
    products_data = []
    
    # 尝试从多种可能的结构中提取产品列表
    goods_list = None
    
    if "result" in result and "data" in result["result"] and "goods_list" in result["result"]["data"]:
        goods_list = result["result"]["data"]["goods_list"]
    elif "data" in result and "goods_list" in result["data"]:
        goods_list = result["data"]["goods_list"]
    
    if goods_list:
        logging.info(f"找到{len(goods_list)}个产品")
        
        for index, product in enumerate(goods_list):
            try:
                # 检查必要字段
                if "goods_id" not in product or not product["goods_id"]:
                    logging.warning(f"警告: 产品 #{index+1} 缺少goods_id，跳过")
                    continue
                
                # 提取价格，处理可能的不同结构，并转换为美元格式
                price = 0
                if "price_info" in product and "price" in product["price_info"]:
                    # 将价格从美分转换为美元（除以100）
                    price_cents = product["price_info"]["price"]
                    price = price_cents / 100.0
                    logging.info(f"价格转换: {price_cents}美分 -> {price}美元")
                
                # 提取图片URL
                image_url = ""
                if "image" in product and "url" in product["image"]:
                    image_url = product["image"]["url"]
                elif "thumb_url" in product:
                    image_url = product["thumb_url"]
                
                # 提取销售数量并转换为整数
                sales_num = 0
                if "sales_tip" in product:
                    sales_tip = product["sales_tip"]
                    if isinstance(sales_tip, str):
                        sales_num = extract_number_from_string(sales_tip)
                elif "sales_tip_text" in product and len(product["sales_tip_text"]) > 0:
                    sales_num = extract_number_from_string(product["sales_tip_text"][0])
                
                # 提取评论数并转换为整数
                comment_num = 0
                if "comment" in product:
                    comment_data = product["comment"]
                    if isinstance(comment_data, dict) and "comment_num_tips" in comment_data:
                        comment_num = extract_number_from_string(comment_data["comment_num_tips"])
                
                # 构建产品数据
                product_data = {
                    "update_time": current_time,
                    "mall_id": mall_id,
                    "goods_id": str(product.get("goods_id", "")),
                    "title": product.get("title", ""),
                    "image_url": image_url,
                    "sales_num": sales_num,
                    "price": price,  # 已转换为美元的价格
                    "comment": comment_num
                }
                
                # 验证所有字段都有值
                for key, value in product_data.items():
                    if value is None or (isinstance(value, str) and value == ""):
                        logging.warning(f"警告: 产品 #{index+1} 字段 {key} 值为空，设置为默认值")
                        if key == "update_time":
                            product_data[key] = current_time
                        elif key == "goods_id" or key == "mall_id":
                            # 这些是必要字段，如果为空，跳过此产品
                            logging.error(f"错误: 产品 #{index+1} 必要字段 {key} 为空，跳过此产品")
                            break
                        elif key in ["sales_num", "price", "comment"]:
                            product_data[key] = 0  # 对于数值类型字段，默认值为0
                        else:
                            product_data[key] = ""  # 其他字段默认为空字符串
                
                if product_data["goods_id"] and product_data["mall_id"]:  # 确保必要字段不为空
                    if index < 2:  # 只打印前两个产品的详细信息，避免日志过长
                        logging.info(f"处理后的产品数据 {index+1}: {json.dumps(product_data, ensure_ascii=False)}")
                    
                    products_data.append(product_data)
            except Exception as e:
                logging.error(f"处理产品时出错: {str(e)}")
                logging.error(f"问题产品数据: {json.dumps(product, ensure_ascii=False)}")
                traceback.print_exc()
    else:
        logging.warning("无法在响应中找到商品列表")
        logging.info(f"数据结构: {json.dumps(list(result.keys()), ensure_ascii=False)}")
    
    return products_data



def main():
    """运行数据抓取过程的主函数"""
    logging.info("="*50)
    logging.info("开始运行Temu数据抓取")
    logging.info(f"时间: {get_hk_time()}")
    logging.info(f"日志文件: {log_file}")
    logging.info("="*50)
    
    # 首先检查并更新cookie（如果需要）
    logging.info("\n检查Cookie状态...")
    if not update_cookies_if_needed():
        logging.warning("Cookie检查/更新过程中出现问题，但继续尝试数据抓取")
    else:
        logging.info("Cookie检查/更新完成")
    
    # 获取所有商店ID
    logging.info("\n获取商店ID列表...")
    shop_ids = fetch_shop_ids()
    
    # 如果没有找到商店ID，使用默认测试ID
    if not shop_ids:
        shop_ids = [TEMU_API_CONFIG['DEFAULT_MALL_ID']]
        logging.info(f"没有找到商店ID，使用测试ID: {shop_ids}")
    
    total_shops = len(shop_ids)
    total_products = 0
    successful_shops = 0
    successful_products = 0
    
    # 添加批次处理和休眠策略
    batch_size = TEMU_API_CONFIG['BATCH_SIZE']  # 每批处理的商店数量
    batch_delay_min = TEMU_API_CONFIG['BATCH_DELAY_MIN']  # 批次间最小延迟（秒）
    batch_delay_max = TEMU_API_CONFIG['BATCH_DELAY_MAX']  # 批次间最大延迟（秒）
    
    for batch_index in range(0, len(shop_ids), batch_size):
        batch_shops = shop_ids[batch_index:batch_index + batch_size]
        logging.info(f"\n开始处理第 {batch_index//batch_size + 1} 批商店，共 {len(batch_shops)} 个")
        
        for index, mall_id in enumerate(batch_shops, 1):
            shop_index = batch_index + index
            logging.info("\n" + "="*50)
            logging.info(f"正在处理商店 {shop_index}/{total_shops}: mall_id {mall_id}")
            logging.info("="*50)
            
            try:
                # 从Temu API获取数据
                result = fetch_temu_shop_data(mall_id)
                if not result:
                    logging.info(f"由于API错误，跳过mall_id为{mall_id}的商店")
                    continue
                
                # 处理并保存商店数据
                shop_data = process_shop_data(result)
                if shop_data and save_shop_data(shop_data):
                    successful_shops += 1
                
                # 处理并保存产品数据
                products = process_products(result, mall_id)
                logging.info(f"为mall_id {mall_id}找到{len(products)}个产品")
                
                saved_products = 0
                for i, product in enumerate(products, 1):
                    logging.info(f"正在保存产品 {i}/{len(products)} (goods_id: {product['goods_id']})")
                    if save_product_data(product):
                        saved_products += 1
                        successful_products += 1
                    total_products += 1
                    
                    # 每处理一个产品后添加短暂随机延迟
                    if i < len(products):
                        random_delay(TEMU_API_CONFIG['PRODUCT_DELAY_MIN'], TEMU_API_CONFIG['PRODUCT_DELAY_MAX'])
                
                logging.info(f"商店 {mall_id} 处理完成: 成功保存 {saved_products}/{len(products)} 个产品")
                
                # 在商店之间添加延迟，避免Temu API的速率限制
                # 使用配置中的延迟值并添加随机因子
                base_delay = TEMU_API_CONFIG['SHOP_DELAY']
                actual_delay = base_delay + random.random() * 2
                logging.info(f"商店间延迟 {actual_delay:.2f} 秒")
                time.sleep(actual_delay)
                
            except Exception as e:
                logging.info(f"处理商店 {mall_id} 时发生异常: {str(e)}")
                traceback.print_exc()
        
        # 批次间添加较长的随机延迟，避免被检测为机器人
        if batch_index + batch_size < len(shop_ids):
            batch_delay = random.uniform(batch_delay_min, batch_delay_max)
            logging.info(f"\n批次间休息 {batch_delay:.2f} 秒，降低请求频率...")
            time.sleep(batch_delay)
    
    # 输出汇总信息
    logging.info("\n" + "="*50)
    logging.info("Temu数据抓取汇总")
    logging.info(f"处理时间: {get_hk_time()}")  # 使用香港时区
    logging.info(f"处理商店总数: {total_shops}")
    logging.info(f"成功保存商店数: {successful_shops}")
    logging.info(f"处理产品总数: {total_products}")
    logging.info(f"成功保存产品数: {successful_products}")
    logging.info("="*50)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logging.error(f"程序执行过程中发生未捕获的异常: {str(e)}")
        traceback.print_exc()