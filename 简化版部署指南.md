# 简化版Temu爬虫 - Cloudflare Workers部署指南

## 🎯 简化版特性

✅ **固定店铺**: 只爬取两个指定店铺 (634418221704901, 634418221321199)  
✅ **无数据库**: 不连接任何数据库，数据直接显示在网页上  
✅ **即时查看**: 访问网址即可看到最新爬取结果  
✅ **美观界面**: 响应式HTML页面，支持手机和电脑查看  
✅ **测试友好**: 专门用于测试Cloudflare是否有爬虫限制  

## 🚀 快速部署（3步完成）

### 第一步：安装工具
```powershell
npm install -g wrangler
wrangler login
```

### 第二步：设置Cookie
```powershell
# 转换Cookie为base64格式
node cookie-converter.js

# 设置环境变量（粘贴上一步生成的base64字符串）
wrangler secret put TEMU_COOKIES
```

### 第三步：部署
```powershell
wrangler deploy
```

## 📱 使用方法

部署成功后，您会得到一个URL，例如：
`https://temu-scraper-simple.your-subdomain.workers.dev`

### 可用端点：

1. **主页面** (推荐)
   ```
   https://your-worker-url.workers.dev/
   ```
   - 自动爬取两个店铺数据
   - 美观的HTML页面展示
   - 包含商店信息和产品列表
   - 支持刷新获取最新数据

2. **API数据**
   ```
   https://your-worker-url.workers.dev/api
   ```
   - 返回JSON格式的原始数据
   - 适合程序调用

3. **健康检查**
   ```
   https://your-worker-url.workers.dev/health
   ```
   - 检查服务状态
   - 显示配置信息

4. **Cookie测试**
   ```
   https://your-worker-url.workers.dev/test-cookie
   ```
   - 测试Cookie是否正常
   - 检查认证状态

## 🎨 页面展示效果

访问主页面后，您将看到：

- **📊 汇总信息**: 处理时间、商店数量、产品总数
- **🏪 商店详情**: 每个商店的基本信息（名称、销售总数、商品数量、评论数）
- **📦 产品列表**: 每个商品的详细信息
  - 商品图片
  - 商品标题
  - 价格（美元）
  - 销量和评论数
  - 商品ID

## ⚡ 性能优化

简化版针对Cloudflare Workers进行了优化：

- **减少延迟**: 请求间隔从60秒减少到3-5秒
- **固定目标**: 只处理2个指定店铺，避免超时
- **无数据库**: 移除所有数据库操作，提高响应速度
- **轻量化**: 移除不必要的功能，专注核心爬取

## 🔍 测试目的

这个简化版主要用于测试：

1. **Cloudflare限制**: 测试Workers是否会被Temu检测为爬虫
2. **IP优势**: 验证Cloudflare的全球IP是否能减少风控
3. **性能表现**: 测试在Workers环境下的执行效果
4. **稳定性**: 验证长期运行的稳定性

## 📝 注意事项

1. **执行时间**: 
   - 免费版有CPU时间限制
   - 如果超时，可能只显示部分数据
   - 建议升级到付费版（$5/月）获得更长执行时间

2. **Cookie管理**: 
   - Cookie会过期，需要定期更新
   - 过期时页面会显示错误信息
   - 使用`/test-cookie`端点检查状态

3. **请求频率**: 
   - 已优化请求间隔，减少被限制的可能
   - 如遇429错误，说明请求过于频繁

4. **数据实时性**: 
   - 每次访问都会重新爬取最新数据
   - 不存储历史数据
   - 适合实时监控

## 🛠️ 故障排除

### 问题1: 页面显示错误
- 检查Cookie是否过期：访问 `/test-cookie`
- 更新Cookie：重新运行 `wrangler secret put TEMU_COOKIES`

### 问题2: 执行超时
- 这是正常现象，免费版有时间限制
- 可以多刷新几次
- 或升级到付费版

### 问题3: 数据不完整
- 可能是网络问题或API限制
- 稍等片刻后重新访问
- 检查控制台日志：`wrangler tail`

## 🔄 更新Cookie

当Cookie过期时：

1. 获取新的cookie.json文件
2. 运行转换工具：`node cookie-converter.js`
3. 更新环境变量：`wrangler secret put TEMU_COOKIES`
4. 无需重新部署，立即生效

## 📊 监控方法

1. **实时日志**：
   ```powershell
   wrangler tail
   ```

2. **定期检查**：
   - 每天访问一次主页面
   - 观察是否正常显示数据
   - 注意是否有错误信息

3. **性能监控**：
   - 观察页面加载时间
   - 检查数据完整性
   - 记录任何异常情况

这个简化版本专门为测试Cloudflare Workers的爬虫能力而设计，帮助您快速验证这种方案的可行性！
