import requests
import json
import logging
import traceback
import os
from datetime import datetime
import time
import random

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"test.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logging.info(f"日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 随机延迟函数
def random_delay(min_delay=1, max_delay=3):
    """随机延迟一段时间，模拟人类行为"""
    delay = min_delay + random.random() * (max_delay - min_delay)
    logging.info(f"随机延迟 {delay:.2f} 秒")
    time.sleep(delay)
    return delay

# 从cookie.json文件加载cookie
def load_cookies_from_file():
    """从cookie.json文件加载cookie信息"""
    try:
        cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        logging.info(f"尝试从 {cookie_file_path} 加载cookie")
        
        if not os.path.exists(cookie_file_path):
            logging.warning(f"警告: cookie文件不存在: {cookie_file_path}")
            return None
            
        with open(cookie_file_path, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
            
        # 将cookie列表转换为字符串格式
        cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_data if cookie.get('name') and cookie.get('value')])
        logging.info(f"成功加载cookie，包含 {len(cookies_data)} 个条目")
        return cookie_str
    except Exception as e:
        logging.error(f"加载cookie文件时出错: {str(e)}")
        traceback.print_exc()
        return None

def get_temu_headers():
    """返回Temu API请求的请求头"""
    # 每次调用时重新加载cookie
    fresh_cookies = load_cookies_from_file()
    if not fresh_cookies:
        logging.warning("警告: 无法获取cookie")
        return None
    
    headers = {
        "content-type": "application/json;charset=UTF-8",
        "Cookie": fresh_cookies,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Origin': 'https://www.temu.com',
        'Referer': 'https://www.temu.com/',
    }
    logging.info(f"生成Temu请求头: {json.dumps(headers, ensure_ascii=False)[:100]}...")
    return headers

def fetch_temu_shop_data(mall_id):
    """从Temu API获取商店和产品数据"""
    url = "https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList"
    payload = {
        "mallId": mall_id,
        "mainGoodsIds": ["1"],
        "source_page_sn": "10013",
        "mall_id": mall_id,
        "main_goods_ids": ["1"],
        "filter_items": "",
        "page_number": 1,
        "page_size": 8,
        "list_id": "r7oe7gyw0vd5xo2z2qja2",
        "scene_code": "mall_rule",
        "page_sn": 10040,
        "page_el_sn": 201265,
        "source": 10018,
        "anti_content": "1"
    }
    
    logging.info(f"请求Temu数据: {url}, mall_id: {mall_id}")
    logging.info(f"请求payload: {json.dumps(payload, ensure_ascii=False)}")
    
    # 随机延迟
    random_delay(1, 3)
    
    headers = get_temu_headers()
    if not headers:
        logging.error("无法获取有效的请求头，跳过请求")
        return None
        
    try:
        response = requests.post(url, headers=headers, json=payload)
        logging.info(f"Temu API状态码: {response.status_code}")
        
        if response.status_code != 200:
            logging.error(f"获取mall_id为{mall_id}的Temu数据时出错: {response.status_code}")
            logging.error(f"返回内容: {response.text[:500]}...")
            return None
        
        result = response.json()
        logging.info(f"Temu返回数据结构: {list(result.keys())}")
        
        # 检查响应结构并打印实际返回的商品数量
        goods_count = 0
        if "result" in result and "data" in result["result"] and "goods_list" in result["result"]["data"]:
            goods_count = len(result["result"]["data"]["goods_list"])
            logging.info(f"成功获取到{goods_count}件商品")
        elif "data" in result and "goods_list" in result["data"]:
            goods_count = len(result["data"]["goods_list"])
            logging.info(f"成功获取到{goods_count}件商品")
        
        if "result" in result:
            logging.info("找到'result'键，包含店铺数据")
            shop_info = result.get("result", {})
            logging.info(f"找到店铺信息: {shop_info.get('mall_name', '未知')}")
            logging.info(f"店铺数据摘要: {json.dumps({k: shop_info.get(k) for k in ['mall_id', 'mall_name', 'goods_sales_num', 'goods_num'] if k in shop_info}, ensure_ascii=False)}")
        
        # 打印产品信息
        if goods_count > 0:
            goods_list = []
            if "result" in result and "data" in result["result"] and "goods_list" in result["result"]["data"]:
                goods_list = result["result"]["data"]["goods_list"]
            elif "data" in result and "goods_list" in result["data"]:
                goods_list = result["data"]["goods_list"]
                
            for i, product in enumerate(goods_list[:3], 1):  # 只打印前3个产品
                logging.info(f"产品 {i}: {json.dumps({k: product.get(k) for k in ['goods_id', 'title'] if k in product}, ensure_ascii=False)}")
                
        # 保存原始数据到文件
        with open(f"shop_data_{mall_id}.json", "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        logging.info(f"完整数据已保存到 shop_data_{mall_id}.json")
            
        return result
    except Exception as e:
        logging.error(f"获取Temu数据时发生异常: {str(e)}")
        traceback.print_exc()
        return None

def main():
    # 初始化日志
    setup_logging()
    
    # 要测试的商店ID
    mall_id = "634418212233370"
    
    logging.info("="*50)
    logging.info(f"开始测试抓取商店数据: mall_id={mall_id}")
    logging.info("="*50)
    
    # 抓取数据
    result = fetch_temu_shop_data(mall_id)
    
    if result:
        logging.info("数据抓取成功!")
    else:
        logging.error("数据抓取失败!")
    
    logging.info("="*50)
    logging.info("测试完成")
    logging.info("="*50)

if __name__ == "__main__":
    main() 