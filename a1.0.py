import requests
import json
from datetime import datetime
import time
import traceback
import pytz  # 导入pytz库用于处理时区
import os
import logging  # 导入logging模块用于日志记录

# 配置日志
def setup_logging():
    """设置日志系统"""
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"temu_crawler_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logging.info(f"日志系统初始化完成，日志文件: {log_file}")
    return log_file

# 立即初始化日志系统
log_file = setup_logging()

# 配置
BASE_API_URL = "http://192.168.5.221:8055"
API_TOKEN = "OppexW5M7FRYT3VQHT3EQx8x3Ly6k2ZM"

# 辅助函数 - 确保在被调用前定义
def convert_to_int(value):
    """将各种格式的值转换为整数"""
    if value is None:
        return 0
    
    if isinstance(value, int):
        return value
    
    if isinstance(value, float):
        return int(value)
    
    if isinstance(value, str):
        return extract_number_from_string(value)
    
    return 0

def extract_number_from_string(text):
    """从字符串中提取数字，处理带有'万'等单位的情况"""
    if not text or not isinstance(text, str):
        return 0
    
    try:
        # 处理带有"万"的情况
        if "万" in text:
            # 提取数字部分
            import re
            numbers = re.findall(r'[\d.]+', text)
            if numbers:
                # 将"万"转换为实际数字（乘以10000）
                return int(float(numbers[0]) * 10000)
        
        # 处理普通数字
        import re
        numbers = re.findall(r'\d+', text)
        if numbers:
            return int(numbers[0])
        
        return 0
    except Exception as e:
        logging.error(f"从字符串'{text}'提取数字时出错: {str(e)}")
        return 0

# 获取香港时区的当前时间
def get_hk_time():
    """获取香港时区的当前时间"""
    hk_timezone = pytz.timezone('Asia/Hong_Kong')
    return datetime.now(hk_timezone).strftime('%Y-%m-%d %H:%M:%S')

def get_headers():
    """返回带有认证令牌的API请求头"""
    return {
        "Authorization": "Bearer " + API_TOKEN,
        "Content-Type": "application/json"
    }

# 从cookie.json文件加载cookie
def load_cookies_from_file():
    """从cookie.json文件加载cookie信息"""
    try:
        cookie_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cookie.json")
        logging.info(f"尝试从 {cookie_file_path} 加载cookie")
        
        if not os.path.exists(cookie_file_path):
            logging.warning(f"警告: cookie文件不存在: {cookie_file_path}")
            return None
            
        with open(cookie_file_path, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
            
        # 将cookie列表转换为字符串格式
        cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_data if cookie.get('name') and cookie.get('value')])
        logging.info(f"成功加载cookie，包含 {len(cookies_data)} 个条目")
        return cookie_str
    except Exception as e:
        logging.error(f"加载cookie文件时出错: {str(e)}")
        traceback.print_exc()
        return None

# 加载cookie
TEMU_COOKIES = load_cookies_from_file()
# 如果加载失败，使用默认cookie
if not TEMU_COOKIES:
    logging.info("使用默认cookie")
    TEMU_COOKIES = "none"

def get_temu_headers():
    """返回Temu API请求的请求头"""
    headers = {
        "content-type": "application/json;charset=UTF-8",
        "Cookie": TEMU_COOKIES
    }
    logging.info(f"生成Temu请求头: {json.dumps(headers, ensure_ascii=False)[:100]}...")
    return headers

def save_shop_data(shop_data):
    """将商店数据保存到数据库"""
    if not shop_data:
        logging.warning("警告: 商店数据为空，跳过保存")
        return False
    
    url = f"{BASE_API_URL}/items/shop_data"
    
    logging.info(f"保存商店数据: mall_id={shop_data.get('mall_id')}")
    try:
        response = requests.post(url, headers=get_headers(), json=shop_data)
        logging.info(f"保存商店数据状态码: {response.status_code}")
        
        if response.status_code in [200, 201, 204]:
            logging.info(f"成功保存商店数据: {shop_data.get('mall_id')}")
            return True
        else:
            logging.error(f"保存商店数据时出错: {response.status_code}")
            logging.error(f"返回内容: {response.text}")
            return False
    except Exception as e:
        logging.error(f"保存商店数据时发生异常: {str(e)}")
        traceback.print_exc()
        return False

def save_product_data(product_data):
    """将产品数据保存到数据库"""
    if not product_data or not product_data.get("goods_id") or not product_data.get("mall_id"):
        logging.warning("警告: 产品数据不完整，跳过保存")
        return False
    
    url = f"{BASE_API_URL}/items/product_data"
    
    logging.info(f"保存产品数据: goods_id={product_data.get('goods_id')}")
    try:
        response = requests.post(url, headers=get_headers(), json=product_data)
        logging.info(f"保存产品数据状态码: {response.status_code}")
        
        if response.status_code in [200, 201, 204]:
            logging.info(f"成功保存产品数据: {product_data.get('goods_id')}")
            return True
        else:
            logging.error(f"保存产品数据时出错: {response.status_code}")
            logging.error(f"返回内容: {response.text}")
            return False
    except Exception as e:
        logging.error(f"保存产品数据时发生异常: {str(e)}")
        traceback.print_exc()
        return False

# 将所有print语句替换为logging语句
def fetch_shop_ids():
    """从数据库获取所有商店ID并去重"""
    url = f"{BASE_API_URL}/items/shop_data"
    params = {
        "fields": "mall_id"
    }
    
    logging.info(f"请求商店ID列表: {url}")
    try:
        response = requests.get(url, headers=get_headers(), params=params)
        logging.info(f"获取商店ID状态码: {response.status_code}")
        
        if response.status_code != 200:
            logging.error(f"获取商店ID时出错: {response.status_code}")
            logging.error(f"返回内容: {response.text}")
            return []
        
        data = response.json()
        logging.info(f"获取到的商店数据: {json.dumps(data, ensure_ascii=False, indent=2)[:500]}...")
        
        # 从响应数据中提取全部mall_id
        all_shop_ids = [item.get('mall_id') for item in data.get('data', []) if item.get('mall_id')]
        logging.info(f"获取到的全部mall_id数量: {len(all_shop_ids)}")
        
        # 使用集合进行去重
        unique_shop_ids = list(set(all_shop_ids))
        logging.info(f"去重后的mall_id数量: {len(unique_shop_ids)}")
        logging.info(f"去除了 {len(all_shop_ids) - len(unique_shop_ids)} 个重复的mall_id")
        
        # 记录重复的mall_id
        if len(all_shop_ids) > len(unique_shop_ids):
            duplicates = {}
            for shop_id in all_shop_ids:
                if shop_id in duplicates:
                    duplicates[shop_id] += 1
                else:
                    duplicates[shop_id] = 1
            
            repeated_ids = {shop_id: count for shop_id, count in duplicates.items() if count > 1}
            if repeated_ids:
                logging.info(f"以下mall_id有重复记录: {json.dumps(repeated_ids, ensure_ascii=False)}")
        
        logging.info(f"最终使用的商店ID列表: {unique_shop_ids}")
        return unique_shop_ids
    except Exception as e:
        logging.error(f"获取商店ID时发生异常: {str(e)}")
        traceback.print_exc()
        return []

def fetch_temu_shop_data(mall_id):
    """从Temu API获取商店和产品数据"""
    url = "https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList"
    payload = {
        "mallId": mall_id,
        "mainGoodsIds": ["1"],
        "source_page_sn": "10013",
        "mall_id": mall_id,
        "main_goods_ids": ["1"],
        "filter_items": "",
        "page_number": 1,
        "page_size": 5,  # 修改为5，获取更多产品
        "list_id": "r7oe7gyw0vd5xo2z2qja2",
        "scene_code": "mall_rule",
        "page_sn": 10040,
        "page_el_sn": 201265,
        "source": 10018,
        "anti_content": "1"
    }
    
    logging.info(f"请求Temu数据: {url}, mall_id: {mall_id}")
    logging.info(f"请求payload: {json.dumps(payload, ensure_ascii=False)}")
    try:
        response = requests.post(url, headers=get_temu_headers(), json=payload)
        logging.info(f"Temu API状态码: {response.status_code}")
        
        if response.status_code != 200:
            logging.error(f"获取mall_id为{mall_id}的Temu数据时出错: {response.status_code}")
            logging.error(f"返回内容: {response.text[:500]}...")
            return None
        
        result = response.json()
        logging.info(f"Temu返回数据结构: {list(result.keys())}")
        
        # 检查响应结构并打印实际返回的商品数量
        goods_count = 0
        if "result" in result and "data" in result["result"] and "goods_list" in result["result"]["data"]:
            goods_count = len(result["result"]["data"]["goods_list"])
            logging.info(f"成功获取到{goods_count}件商品，尽管page_size设置为1")
        elif "data" in result and "goods_list" in result["data"]:
            goods_count = len(result["data"]["goods_list"])
            logging.info(f"成功获取到{goods_count}件商品，尽管page_size设置为1")
        
        if "result" in result:
            logging.info("找到'result'键，包含店铺数据")
            if "data" in result["result"] and "goods_list" in result["result"]["data"]:
                goods_count = len(result["result"]["data"]["goods_list"])
                logging.info(f"成功获取到{goods_count}件商品")
            else:
                shop_info = result.get("result", {})
                logging.info(f"找到店铺信息: {shop_info.get('mall_name', '未知')}")
                logging.info("未找到商品列表，可能是API结构不同")
        else:
            # 第二种可能的结构
            if "data" in result and "goods_list" in result["data"]:
                goods_count = len(result["data"]["goods_list"])
                logging.info(f"成功获取到{goods_count}件商品")
            else:
                logging.info(f"未按预期找到数据结构，返回数据: {json.dumps(result, ensure_ascii=False, indent=2)[:500]}...")
        
        return result
    except Exception as e:
        logging.error(f"获取Temu数据时发生异常: {str(e)}")
        traceback.print_exc()
        return None

def process_shop_data(result):
    """处理并提取商店数据"""
    current_time = get_hk_time()  # 使用香港时区
    
    logging.info("处理商店数据...")
    
    # 提取商店数据的路径可能不同，尝试多种可能的结构
    shop_data = None
    
    if "result" in result:
        shop_info = result["result"]
        
        # 检查必要字段是否存在
        if "mall_id" in shop_info and "mall_name" in shop_info:
            # 检查必要数据不为空
            if shop_info["mall_id"] and shop_info["mall_name"]:
                # 将数值字段转换为整数
                goods_sales_num = convert_to_int(shop_info.get("goods_sales_num", "0"))
                goods_num = convert_to_int(shop_info.get("goods_num", "0"))
                review_num = convert_to_int(shop_info.get("review_num", "0"))
                
                shop_data = {
                    "update_time": current_time,
                    "mall_id": str(shop_info.get("mall_id", "")),
                    "mall_name": shop_info.get("mall_name", ""),
                    "mall_logo": shop_info.get("mall_logo", ""),
                    "goods_sales_num": goods_sales_num,
                    "goods_num": goods_num,
                    "review_num": review_num
                }
            else:
                logging.warning("警告: 商店数据缺少必要信息（mall_id或mall_name为空）")
        else:
            logging.warning("警告: 商店数据缺少必要字段（mall_id或mall_name）")
    else:
        # 尝试其他可能的数据结构
        if "data" in result and "mall_info" in result["data"]:
            shop_info = result["data"]["mall_info"]
            
            # 检查必要字段是否存在
            if "mall_id" in shop_info and "mall_name" in shop_info:
                # 检查必要数据不为空
                if shop_info["mall_id"] and shop_info["mall_name"]:
                    # 将数值字段转换为整数
                    goods_sales_num = convert_to_int(shop_info.get("goods_sales_num", "0"))
                    goods_num = convert_to_int(shop_info.get("goods_num", "0"))
                    review_num = convert_to_int(shop_info.get("review_num", "0"))
                    
                    shop_data = {
                        "update_time": current_time,
                        "mall_id": str(shop_info.get("mall_id", "")),
                        "mall_name": shop_info.get("mall_name", ""),
                        "mall_logo": shop_info.get("mall_logo", ""),
                        "goods_sales_num": goods_sales_num,
                        "goods_num": goods_num,
                        "review_num": review_num
                    }
                else:
                    logging.warning("警告: 商店数据缺少必要信息（mall_id或mall_name为空）")
            else:
                logging.warning("警告: 商店数据缺少必要字段（mall_id或mall_name）")
    
    # 最终检查，确保所有字段均有值，避免空数据
    if shop_data:
        # 验证所有数据字段
        for key, value in shop_data.items():
            if value is None or (isinstance(value, str) and value == ""):
                logging.warning(f"警告: 字段 {key} 值为空，设置为默认值")
                if key == "update_time":
                    shop_data[key] = current_time
                elif key == "mall_id" or key == "mall_name":
                    logging.error(f"错误: 必要字段 {key} 为空，无法保存商店数据")
                    return None
                elif key in ["goods_sales_num", "goods_num", "review_num"]:
                    shop_data[key] = 0  # 对于数值类型字段，默认值为0
                else:
                    shop_data[key] = ""  # 其他字段默认为空字符串
        
        logging.info(f"处理后的商店数据: {json.dumps(shop_data, ensure_ascii=False)}")
        return shop_data
    else:
        logging.error("错误: 无法从API响应中提取商店数据")
        return None

def process_products(result, mall_id):
    """处理和提取产品数据"""
    current_time = get_hk_time()  # 使用香港时区
    
    logging.info("处理产品数据...")
    products_data = []
    
    # 尝试从多种可能的结构中提取产品列表
    goods_list = None
    
    if "result" in result and "data" in result["result"] and "goods_list" in result["result"]["data"]:
        goods_list = result["result"]["data"]["goods_list"]
    elif "data" in result and "goods_list" in result["data"]:
        goods_list = result["data"]["goods_list"]
    
    if goods_list:
        logging.info(f"找到{len(goods_list)}个产品")
        
        for index, product in enumerate(goods_list):
            try:
                # 检查必要字段
                if "goods_id" not in product or not product["goods_id"]:
                    logging.warning(f"警告: 产品 #{index+1} 缺少goods_id，跳过")
                    continue
                
                # 提取价格，处理可能的不同结构，并转换为美元格式
                price = 0
                if "price_info" in product and "price" in product["price_info"]:
                    # 将价格从美分转换为美元（除以100）
                    price_cents = product["price_info"]["price"]
                    price = price_cents / 100.0
                    logging.info(f"价格转换: {price_cents}美分 -> {price}美元")
                
                # 提取图片URL
                image_url = ""
                if "image" in product and "url" in product["image"]:
                    image_url = product["image"]["url"]
                elif "thumb_url" in product:
                    image_url = product["thumb_url"]
                
                # 提取销售数量并转换为整数
                sales_num = 0
                if "sales_tip" in product:
                    sales_tip = product["sales_tip"]
                    if isinstance(sales_tip, str):
                        sales_num = extract_number_from_string(sales_tip)
                elif "sales_tip_text" in product and len(product["sales_tip_text"]) > 0:
                    sales_num = extract_number_from_string(product["sales_tip_text"][0])
                
                # 提取评论数并转换为整数
                comment_num = 0
                if "comment" in product:
                    comment_data = product["comment"]
                    if isinstance(comment_data, dict) and "comment_num_tips" in comment_data:
                        comment_num = extract_number_from_string(comment_data["comment_num_tips"])
                
                # 构建产品数据
                product_data = {
                    "update_time": current_time,
                    "mall_id": mall_id,
                    "goods_id": str(product.get("goods_id", "")),
                    "title": product.get("title", ""),
                    "image_url": image_url,
                    "sales_num": sales_num,
                    "price": price,  # 已转换为美元的价格
                    "comment": comment_num
                }
                
                # 验证所有字段都有值
                for key, value in product_data.items():
                    if value is None or (isinstance(value, str) and value == ""):
                        logging.warning(f"警告: 产品 #{index+1} 字段 {key} 值为空，设置为默认值")
                        if key == "update_time":
                            product_data[key] = current_time
                        elif key == "goods_id" or key == "mall_id":
                            # 这些是必要字段，如果为空，跳过此产品
                            logging.error(f"错误: 产品 #{index+1} 必要字段 {key} 为空，跳过此产品")
                            break
                        elif key in ["sales_num", "price", "comment"]:
                            product_data[key] = 0  # 对于数值类型字段，默认值为0
                        else:
                            product_data[key] = ""  # 其他字段默认为空字符串
                
                if product_data["goods_id"] and product_data["mall_id"]:  # 确保必要字段不为空
                    if index < 2:  # 只打印前两个产品的详细信息，避免日志过长
                        logging.info(f"处理后的产品数据 {index+1}: {json.dumps(product_data, ensure_ascii=False)}")
                    
                    products_data.append(product_data)
            except Exception as e:
                logging.error(f"处理产品时出错: {str(e)}")
                logging.error(f"问题产品数据: {json.dumps(product, ensure_ascii=False)}")
                traceback.print_exc()
    else:
        logging.warning("无法在响应中找到商品列表")
        logging.info(f"数据结构: {json.dumps(list(result.keys()), ensure_ascii=False)}")
    
    return products_data

def main():
    """运行数据抓取过程的主函数"""
    # 不再需要初始化日志系统，因为已经在文件开头初始化了
    
    logging.info("="*50)
    logging.info("开始运行Temu数据抓取")
    logging.info(f"时间: {get_hk_time()}")  # 使用香港时区
    logging.info(f"日志文件: {log_file}")
    logging.info("="*50)
    
    # 测试API连接
    logging.info("\n测试本地API连接...")
    try:
        test_response = requests.get(f"{BASE_API_URL}/server/info", headers=get_headers())
        logging.info(f"API连接测试状态码: {test_response.status_code}")
        if test_response.status_code == 200:
            logging.info("本地API连接成功")
        else:
            logging.error(f"本地API连接失败: {test_response.text}")
    except Exception as e:
        logging.error(f"本地API连接测试异常: {str(e)}")
        traceback.print_exc()
    
    # 获取所有商店ID
    logging.info("\n获取商店ID列表...")
    shop_ids = fetch_shop_ids()
    
    # 如果没有找到商店ID，使用默认测试ID
    if not shop_ids:
        shop_ids = ["6313567470795"]  # 使用您提供的示例mall_id
        logging.info(f"没有找到商店ID，使用测试ID: {shop_ids}")
    
    total_shops = len(shop_ids)
    total_products = 0
    successful_shops = 0
    successful_products = 0
    
    for index, mall_id in enumerate(shop_ids, 1):
        logging.info("\n" + "="*50)
        logging.info(f"正在处理商店 {index}/{total_shops}: mall_id {mall_id}")
        logging.info("="*50)
        
        try:
            # 从Temu API获取数据
            result = fetch_temu_shop_data(mall_id)
            if not result:
                logging.info(f"由于API错误，跳过mall_id为{mall_id}的商店")
                continue
            
            # 处理并保存商店数据
            shop_data = process_shop_data(result)
            if shop_data and save_shop_data(shop_data):
                successful_shops += 1
            
            # 处理并保存产品数据
            products = process_products(result, mall_id)
            logging.info(f"为mall_id {mall_id}找到{len(products)}个产品")
            
            saved_products = 0
            for i, product in enumerate(products, 1):
                logging.info(f"正在保存产品 {i}/{len(products)} (goods_id: {product['goods_id']})")
                if save_product_data(product):
                    saved_products += 1
                    successful_products += 1
                total_products += 1
                # 添加小延迟，避免过度请求API
                time.sleep(0.2)
            
            logging.info(f"商店 {mall_id} 处理完成: 成功保存 {saved_products}/{len(products)} 个产品")
            
            # 在商店之间添加延迟，避免速率限制
            time.sleep(1)
        except Exception as e:
            logging.info(f"处理商店 {mall_id} 时发生异常: {str(e)}")
            traceback.print_exc()
    
    # 输出汇总信息
    logging.info("\n" + "="*50)
    logging.info("Temu数据抓取汇总")
    logging.info(f"处理时间: {get_hk_time()}")  # 使用香港时区
    logging.info(f"处理商店总数: {total_shops}")
    logging.info(f"成功保存商店数: {successful_shops}")
    logging.info(f"处理产品总数: {total_products}")
    logging.info(f"成功保存产品数: {successful_products}")
    logging.info("="*50)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logging.error(f"程序执行过程中发生未捕获的异常: {str(e)}")
        traceback.print_exc()