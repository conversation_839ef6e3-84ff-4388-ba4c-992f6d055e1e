/**
 * Temu数据爬虫 - Cloudflare Workers版本
 * 将Python爬虫程序转换为Cloudflare Workers JavaScript版本
 */

// 配置常量
const CONFIG = {
  TEMU_API: {
    BASE_URL: "https://www.temu.com",
    API_PATH: "/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList",
    // 固定的两个店铺ID
    FIXED_MALL_IDS: ["634418221704901", "634418221321199"],
    PAGE_SIZE: 8,
    SHOP_DELAY: 5000, // 减少到5秒
    PRODUCT_DELAY_MIN: 1000,
    PRODUCT_DELAY_MAX: 2000,
    REQUEST_DELAY_MIN: 3000, // 减少延迟时间
    REQUEST_DELAY_MAX: 5000,
    BATCH_SIZE: 2,
    BATCH_DELAY_MIN: 2000,
    BATCH_DELAY_MAX: 3000
  },
  TIMEZONE: {
    DEFAULT_TIMEZONE: "Asia/Hong_Kong"
  }
};

// 工具函数
function getHKTime() {
  const now = new Date();
  const hkTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Hong_Kong"}));
  return hkTime.toISOString().replace('T', ' ').substring(0, 19);
}

function convertToInt(value) {
  if (value === null || value === undefined) return 0;
  if (typeof value === 'number') return Math.floor(value);
  if (typeof value === 'string') return extractNumberFromString(value);
  return 0;
}

function extractNumberFromString(text) {
  if (!text || typeof text !== 'string') return 0;
  
  try {
    // 处理带有"万"的情况
    if (text.includes('万')) {
      const numbers = text.match(/[\d.]+/g);
      if (numbers && numbers.length > 0) {
        return Math.floor(parseFloat(numbers[0]) * 10000);
      }
    }
    
    // 处理普通数字
    const numbers = text.match(/\d+/g);
    if (numbers && numbers.length > 0) {
      return parseInt(numbers[0]);
    }
    
    return 0;
  } catch (error) {
    console.error(`从字符串'${text}'提取数字时出错:`, error);
    return 0;
  }
}

function randomDelay(minDelay, maxDelay) {
  const delay = minDelay + Math.random() * (maxDelay - minDelay);
  return new Promise(resolve => setTimeout(resolve, delay));
}

// 硬编码的Cookie数据
const HARDCODED_COOKIES = [
  {"name": "region", "value": "211"},
  {"name": "timezone", "value": "Asia%2FHong_Kong"},
  {"name": "gmp_temu_token", "value": "fCKntobDpWtrQalOFsdw6lRk80S+1rvfA8QeZfMhjJ37hCPvZYl+4+VQRNle8Ip4sK223UjYr8zgbNxb7P1guU4j/loMg091OU+beY3SIpkqQcmdsepvmv2KqSObFMVEmTIUX62KC/Tzl8yQmoQ3/dxwIakfpMBkApFb45Z33dE"},
  {"name": "_hal_tag", "value": "AGi9exQ3PzzdfuTxzgk/pAAwAHOCjfFRRPSXXH3LhTMnikA3SmLnnYVg7PUv7qwXIt7Wp5ZWPeIt2acB7I3duA=="},
  {"name": "_u_pa", "value": "%7B%22nrpt%22%3A0%7D"},
  {"name": "api_uid", "value": "Cm26mmfk81mYzW9K+Ky8Ag=="},
  {"name": "language", "value": "zh-Hans"},
  {"name": "currency", "value": "USD"},
  {"name": "__cf_bm", "value": "a3nnszq.yqC1JwzFga3pMr7KBSDc1govoNgyy0c2ing-1750658630-*******-80uBwK7bn9y247Q2jyVxdXJaSI_MCsKP_DsnfjTmmwZCfnNLvp7_LEU_DmJtjZmXeKKYVICNC0nX6mWJhuHDDNVJfoV_BbsWVy4YJz.lCXg"},
  {"name": "_bee", "value": "oGURgzZKyJtUmvWwYiF1DhvQKWuQkapT"},
  {"name": "_nano_fp", "value": "XpmYXq9ynqmynpTJXo_lhck5IrrPdZlFa8G0pr0b"},
  {"name": "_ttc", "value": "3.yMBn1kGOdLdl.1782194671"},
  {"name": "AccessToken", "value": "6DV5QPMIGRVBBS3V4ZCQE2IHR72J6LIXRGE42QSTW7B2QOFCVASQ0110d349e14b"},
  {"name": "dilx", "value": "~sXEue2kjh_DRO2g5Ou77"},
  {"name": "hfsc", "value": "L3yIeI437D/92pTJcA=="},
  {"name": "isLogin", "value": "1750658664596"},
  {"name": "njrpl", "value": "oGURgzZKyJtUmvWwYiF1DhvQKWuQkapT"},
  {"name": "user_uin", "value": "BAEZ47EIPAHX5GRSKFNCNSKVA733VJITLGPO2PCU"},
  {"name": "webp", "value": "1"}
];

// Cookie处理函数
function loadCookiesFromHardcoded() {
  try {
    // 将cookie数组转换为字符串格式
    const cookieStr = HARDCODED_COOKIES
      .filter(cookie => cookie.name && cookie.value)
      .map(cookie => `${cookie.name}=${cookie.value}`)
      .join('; ');

    console.log(`成功加载硬编码cookie，包含 ${HARDCODED_COOKIES.length} 个条目`);
    return cookieStr;
  } catch (error) {
    console.error('加载硬编码cookie时出错:', error);
    return null;
  }
}

function getVerifyAuthToken() {
  try {
    const verifyToken = HARDCODED_COOKIES.find(cookie => cookie.name === 'verifyAuthToken');
    return verifyToken ? verifyToken.value : null;
  } catch (error) {
    console.error('获取verifyAuthToken时出错:', error);
    return null;
  }
}

// 请求头生成函数
function getTemuHeaders() {
  const cookies = loadCookiesFromHardcoded();
  if (!cookies) {
    console.error('无法获取有效的cookie');
    return null;
  }

  const headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
    "content-type": "application/json;charset=UTF-8",
    "origin": "https://www.temu.com",
    "priority": "u=1, i",
    "referer": "https://www.temu.com/",
    "sec-ch-ua": '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36",
    "Cookie": cookies
  };

  const verifyToken = getVerifyAuthToken();
  if (verifyToken) {
    headers["verifyauthtoken"] = verifyToken;
    console.log('✅ 添加verifyAuthToken到headers');
  } else {
    console.log('⚠️ 未找到verifyAuthToken，使用基础认证');
  }

  return headers;
}

// 移除后端API相关函数，不再连接数据库

// Temu API数据获取
async function fetchTemuShopData(mallId) {
  const url = CONFIG.TEMU_API.BASE_URL + CONFIG.TEMU_API.API_PATH;
  const payload = {
    "mallId": mallId,
    "mainGoodsIds": ["1"],
    "source_page_sn": "10013",
    "mall_id": mallId,
    "main_goods_ids": ["1"],
    "filter_items": "",
    "page_number": 1,
    "page_size": CONFIG.TEMU_API.PAGE_SIZE,
    "list_id": "r7oe7gyw0vd5xo2z2qja2",
    "scene_code": "mall_rule",
    "page_sn": 10040,
    "page_el_sn": 201265,
    "source": 10018,
    "anti_content": "1"
  };
  
  console.log(`请求Temu数据: ${url}, mall_id: ${mallId}`);
  
  // 添加随机延迟
  await randomDelay(CONFIG.TEMU_API.REQUEST_DELAY_MIN, CONFIG.TEMU_API.REQUEST_DELAY_MAX);
  
  const headers = getTemuHeaders();
  if (!headers) {
    console.error('无法获取有效的请求头，跳过请求');
    return null;
  }
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(payload)
    });
    
    console.log(`Temu API状态码: ${response.status}`);
    
    if (!response.ok) {
      console.error(`获取mall_id为${mallId}的Temu数据时出错: ${response.status}`);
      const text = await response.text();
      console.error(`返回内容: ${text.substring(0, 500)}...`);
      
      // 处理429错误（请求频率过高）
      if (response.status === 429) {
        console.warn('遇到429错误（请求频率过高），建议增加延迟时间');
      }
      
      return null;
    }
    
    const result = await response.json();
    return result;
  } catch (error) {
    console.error(`获取Temu数据时发生异常: ${error.message}`);
    return null;
  }
}

// 商店数据处理
function processShopData(result) {
  const currentTime = getHKTime();
  console.log('处理商店数据...');
  
  let shopData = null;
  
  if (result.result) {
    const shopInfo = result.result;
    
    if (shopInfo.mall_id && shopInfo.mall_name) {
      const goodsSalesNum = convertToInt(shopInfo.goods_sales_num || "0");
      const goodsNum = convertToInt(shopInfo.goods_num || "0");
      const reviewNum = convertToInt(shopInfo.review_num || "0");
      
      shopData = {
        update_time: currentTime,
        mall_id: String(shopInfo.mall_id || ""),
        mall_name: shopInfo.mall_name || "",
        mall_logo: shopInfo.mall_logo || "",
        goods_sales_num: goodsSalesNum,
        goods_num: goodsNum,
        review_num: reviewNum
      };
    } else {
      console.warn('警告: 商店数据缺少必要信息（mall_id或mall_name为空）');
    }
  } else if (result.data && result.data.mall_info) {
    const shopInfo = result.data.mall_info;
    
    if (shopInfo.mall_id && shopInfo.mall_name) {
      const goodsSalesNum = convertToInt(shopInfo.goods_sales_num || "0");
      const goodsNum = convertToInt(shopInfo.goods_num || "0");
      const reviewNum = convertToInt(shopInfo.review_num || "0");
      
      shopData = {
        update_time: currentTime,
        mall_id: String(shopInfo.mall_id || ""),
        mall_name: shopInfo.mall_name || "",
        mall_logo: shopInfo.mall_logo || "",
        goods_sales_num: goodsSalesNum,
        goods_num: goodsNum,
        review_num: reviewNum
      };
    } else {
      console.warn('警告: 商店数据缺少必要信息（mall_id或mall_name为空）');
    }
  }
  
  if (shopData) {
    // 验证所有数据字段
    for (const [key, value] of Object.entries(shopData)) {
      if (value === null || value === undefined || (typeof value === 'string' && value === "")) {
        console.warn(`警告: 字段 ${key} 值为空，设置为默认值`);
        if (key === "update_time") {
          shopData[key] = currentTime;
        } else if (key === "mall_id" || key === "mall_name") {
          console.error(`错误: 必要字段 ${key} 为空，无法保存商店数据`);
          return null;
        } else if (["goods_sales_num", "goods_num", "review_num"].includes(key)) {
          shopData[key] = 0;
        } else {
          shopData[key] = "";
        }
      }
    }

    console.log('处理后的商店数据:', JSON.stringify(shopData));
    return shopData;
  } else {
    console.error('错误: 无法从API响应中提取商店数据');
    return null;
  }
}

// 产品数据处理
function processProducts(result, mallId) {
  const currentTime = getHKTime();
  console.log('处理产品数据...');
  const productsData = [];

  let goodsList = null;

  if (result.result && result.result.data && result.result.data.goods_list) {
    goodsList = result.result.data.goods_list;
  } else if (result.data && result.data.goods_list) {
    goodsList = result.data.goods_list;
  }

  if (goodsList) {
    console.log(`找到${goodsList.length}个产品`);

    for (let index = 0; index < goodsList.length; index++) {
      const product = goodsList[index];
      try {
        // 检查必要字段
        if (!product.goods_id) {
          console.warn(`警告: 产品 #${index + 1} 缺少goods_id，跳过`);
          continue;
        }

        // 提取价格，处理可能的不同结构，并转换为美元格式
        let price = 0;
        if (product.price_info && product.price_info.price) {
          // 将价格从美分转换为美元（除以100）
          const priceCents = product.price_info.price;
          price = priceCents / 100.0;
          console.log(`价格转换: ${priceCents}美分 -> ${price}美元`);
        }

        // 提取图片URL
        let imageUrl = "";
        if (product.image && product.image.url) {
          imageUrl = product.image.url;
        } else if (product.thumb_url) {
          imageUrl = product.thumb_url;
        }

        // 提取销售数量并转换为整数
        let salesNum = 0;
        if (product.sales_tip) {
          if (typeof product.sales_tip === 'string') {
            salesNum = extractNumberFromString(product.sales_tip);
          }
        } else if (product.sales_tip_text && product.sales_tip_text.length > 0) {
          salesNum = extractNumberFromString(product.sales_tip_text[0]);
        }

        // 提取评论数并转换为整数
        let commentNum = 0;
        if (product.comment) {
          const commentData = product.comment;
          if (typeof commentData === 'object' && commentData.comment_num_tips) {
            commentNum = extractNumberFromString(commentData.comment_num_tips);
          }
        }

        // 构建产品数据
        const productData = {
          update_time: currentTime,
          mall_id: mallId,
          goods_id: String(product.goods_id || ""),
          title: product.title || "",
          image_url: imageUrl,
          sales_num: salesNum,
          price: price,
          comment: commentNum
        };

        // 验证所有字段都有值
        let isValid = true;
        for (const [key, value] of Object.entries(productData)) {
          if (value === null || value === undefined || (typeof value === 'string' && value === "")) {
            console.warn(`警告: 产品 #${index + 1} 字段 ${key} 值为空，设置为默认值`);
            if (key === "update_time") {
              productData[key] = currentTime;
            } else if (key === "goods_id" || key === "mall_id") {
              console.error(`错误: 产品 #${index + 1} 必要字段 ${key} 为空，跳过此产品`);
              isValid = false;
              break;
            } else if (["sales_num", "price", "comment"].includes(key)) {
              productData[key] = 0;
            } else {
              productData[key] = "";
            }
          }
        }

        if (isValid && productData.goods_id && productData.mall_id) {
          if (index < 2) { // 只打印前两个产品的详细信息
            console.log(`处理后的产品数据 ${index + 1}:`, JSON.stringify(productData));
          }
          productsData.push(productData);
        }
      } catch (error) {
        console.error(`处理产品时出错: ${error.message}`);
        console.error(`问题产品数据:`, JSON.stringify(product));
      }
    }
  } else {
    console.warn('无法在响应中找到商品列表');
    console.log('数据结构:', JSON.stringify(Object.keys(result)));
  }

  return productsData;
}

// 移除未使用的函数

// 移除数据库保存功能，数据将直接返回显示

// 移除产品数据保存功能，数据将直接返回显示

// 处理单个商店的数据
async function processShop(mallId) {
  console.log(`\n开始处理商店: mall_id ${mallId}`);

  const result = {
    mallId: mallId,
    success: false,
    shopData: null,
    products: [],
    error: null
  };

  try {
    // 从Temu API获取数据
    const temuData = await fetchTemuShopData(mallId);
    if (!temuData) {
      result.error = 'Failed to fetch data from Temu API';
      return result;
    }

    // 处理商店数据
    const shopData = processShopData(temuData);
    if (shopData) {
      result.shopData = shopData;
      console.log(`商店数据处理完成: ${shopData.mall_name}`);
    }

    // 处理产品数据
    const products = processProducts(temuData, mallId);
    console.log(`为mall_id ${mallId}找到${products.length}个产品`);

    result.products = products;
    result.success = true;

    console.log(`商店 ${mallId} 处理完成: 获取到 ${products.length} 个产品`);

  } catch (error) {
    console.error(`处理商店 ${mallId} 时发生异常: ${error.message}`);
    result.error = error.message;
  }

  return result;
}

// 主要处理函数
async function processShops() {
  const mallIds = CONFIG.TEMU_API.FIXED_MALL_IDS;

  console.log('='.repeat(50));
  console.log('开始运行Temu数据抓取 - Cloudflare Workers版本');
  console.log(`时间: ${getHKTime()}`);
  console.log(`要处理的商店数量: ${mallIds.length}`);
  console.log(`固定商店ID: ${mallIds.join(', ')}`);
  console.log('='.repeat(50));

  const results = [];
  let totalShops = mallIds.length;
  let successfulShops = 0;
  let totalProducts = 0;

  for (let i = 0; i < mallIds.length; i++) {
    const mallId = mallIds[i];

    console.log(`\n正在处理商店 ${i + 1}/${totalShops}: mall_id ${mallId}`);

    const result = await processShop(mallId);
    results.push(result);

    if (result.success) {
      successfulShops++;
      totalProducts += result.products.length;
    }

    // 在商店之间添加延迟，避免API速率限制
    if (i < mallIds.length - 1) {
      const delay = CONFIG.TEMU_API.SHOP_DELAY;
      console.log(`商店间延迟 ${delay / 1000} 秒`);
      await randomDelay(delay, delay);
    }
  }

  // 输出汇总信息
  console.log('\n' + '='.repeat(50));
  console.log('Temu数据抓取汇总');
  console.log(`处理时间: ${getHKTime()}`);
  console.log(`处理商店总数: ${totalShops}`);
  console.log(`成功处理商店数: ${successfulShops}`);
  console.log(`处理产品总数: ${totalProducts}`);
  console.log('='.repeat(50));

  return {
    summary: {
      totalShops,
      successfulShops,
      totalProducts,
      processTime: getHKTime()
    },
    shops: results
  };
}

// 生成HTML页面
function generateHTML(data) {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Temu爬虫数据展示</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #fff; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .shop { background: #fff; margin-bottom: 20px; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .shop-header { background: #007bff; color: white; padding: 15px; }
        .shop-info { padding: 15px; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
        .info-item { background: #f8f9fa; padding: 10px; border-radius: 4px; }
        .products { padding: 15px; }
        .product-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; }
        .product { border: 1px solid #ddd; border-radius: 8px; overflow: hidden; background: #fff; }
        .product-image { width: 100%; height: 200px; object-fit: cover; }
        .product-info { padding: 15px; }
        .product-title { font-weight: bold; margin-bottom: 10px; color: #333; }
        .product-price { color: #e74c3c; font-size: 18px; font-weight: bold; }
        .product-stats { display: flex; justify-content: space-between; margin-top: 10px; font-size: 14px; color: #666; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .refresh-btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .refresh-btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛍️ Temu商店数据爬取结果</h1>
            <p><strong>抓取时间:</strong> ${data.summary.processTime}</p>
            <p><strong>处理商店数:</strong> ${data.summary.totalShops} | <strong>成功:</strong> ${data.summary.successfulShops} | <strong>产品总数:</strong> ${data.summary.totalProducts}</p>
            <button class="refresh-btn" onclick="location.reload()">🔄 刷新数据</button>
        </div>

        ${data.shops.map(shop => {
          if (!shop.success) {
            return `
              <div class="shop">
                <div class="shop-header">
                  <h2>❌ 商店 ${shop.mallId} - 获取失败</h2>
                </div>
                <div class="error">
                  错误信息: ${shop.error || '未知错误'}
                </div>
              </div>
            `;
          }

          return `
            <div class="shop">
              <div class="shop-header">
                <h2>🏪 ${shop.shopData?.mall_name || '未知商店'} (ID: ${shop.mallId})</h2>
              </div>

              ${shop.shopData ? `
                <div class="shop-info">
                  <div class="info-item">
                    <strong>商店名称:</strong><br>${shop.shopData.mall_name}
                  </div>
                  <div class="info-item">
                    <strong>商品销售总数:</strong><br>${shop.shopData.goods_sales_num.toLocaleString()}
                  </div>
                  <div class="info-item">
                    <strong>商品总数:</strong><br>${shop.shopData.goods_num.toLocaleString()}
                  </div>
                  <div class="info-item">
                    <strong>评论总数:</strong><br>${shop.shopData.review_num.toLocaleString()}
                  </div>
                </div>
              ` : ''}

              <div class="products">
                <h3>📦 商品列表 (${shop.products.length}个)</h3>
                <div class="product-grid">
                  ${shop.products.map(product => `
                    <div class="product">
                      ${product.image_url ? `<img src="${product.image_url}" alt="${product.title}" class="product-image" onerror="this.style.display='none'">` : ''}
                      <div class="product-info">
                        <div class="product-title">${product.title}</div>
                        <div class="product-price">$${product.price.toFixed(2)}</div>
                        <div class="product-stats">
                          <span>💰 销量: ${product.sales_num.toLocaleString()}</span>
                          <span>💬 评论: ${product.comment.toLocaleString()}</span>
                        </div>
                        <div style="font-size: 12px; color: #999; margin-top: 5px;">
                          ID: ${product.goods_id}
                        </div>
                      </div>
                    </div>
                  `).join('')}
                </div>
              </div>
            </div>
          `;
        }).join('')}
    </div>
</body>
</html>
  `;
}

// HTTP请求处理器
async function handleRequest(request) {
  const url = new URL(request.url);
  const path = url.pathname;

  // CORS处理
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (path === '/') {
      // 主页面 - 执行爬取并显示结果
      const results = await processShops();
      const html = generateHTML(results);

      return new Response(html, {
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
          ...corsHeaders
        }
      });
    }

    if (path === '/health') {
      // 健康检查端点
      return new Response(JSON.stringify({
        status: 'ok',
        service: 'Temu Scraper - Cloudflare Workers (简化版)',
        time: getHKTime(),
        version: '2.0.0',
        fixedShops: CONFIG.TEMU_API.FIXED_MALL_IDS
      }), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    if (path === '/api') {
      // API端点 - 返回JSON数据
      const results = await processShops();

      return new Response(JSON.stringify({
        success: true,
        message: 'Scraping completed',
        ...results
      }), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    if (path === '/test-cookie') {
      // 测试Cookie端点
      const cookies = loadCookiesFromHardcoded();
      const verifyToken = getVerifyAuthToken();

      return new Response(JSON.stringify({
        hasCookies: !!cookies,
        cookieLength: cookies ? cookies.length : 0,
        hasVerifyToken: !!verifyToken,
        verifyTokenPreview: verifyToken ? verifyToken.substring(0, 20) + '...' : null,
        cookieCount: HARDCODED_COOKIES.length,
        time: getHKTime()
      }), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // 404处理
    return new Response(JSON.stringify({
      error: 'Not Found',
      message: 'Available endpoints: / (HTML页面), /health (健康检查), /api (JSON数据), /test-cookie (Cookie测试)',
      time: getHKTime()
    }), {
      status: 404,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('处理请求时发生错误:', error);

    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      time: getHKTime()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// Cloudflare Workers入口点
export default {
  async fetch(request) {
    return handleRequest(request);
  }
};
