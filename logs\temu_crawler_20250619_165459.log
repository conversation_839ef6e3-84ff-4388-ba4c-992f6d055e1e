2025-06-19 16:54:59,692 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250619_165459.log
2025-06-19 16:54:59,693 [INFO] ==================================================
2025-06-19 16:54:59,693 [INFO] 开始运行Temu数据抓取
2025-06-19 16:54:59,748 [INFO] 时间: 2025-06-19 16:54:59
2025-06-19 16:54:59,748 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250619_165459.log
2025-06-19 16:54:59,748 [INFO] ==================================================
2025-06-19 16:54:59,748 [INFO] 
检查Cookie状态...
2025-06-19 16:54:59,748 [INFO] 检查cookie是否需要更新...
2025-06-19 16:55:00,364 [INFO] Cookie更新程序退出码: 0
2025-06-19 16:55:00,364 [INFO] Cookie检查/更新完成
2025-06-19 16:55:00,364 [INFO] 
获取商店ID列表...
2025-06-19 16:55:00,364 [INFO] 请求商店ID列表: http://172.25.165.28:8055/items/shop_data
2025-06-19 16:55:00,440 [INFO] 获取商店ID状态码: 200
2025-06-19 16:55:00,440 [INFO] 获取到的商店数据: {
  "data": [
    {
      "mall_id": "634418212233370"
    },
    {
      "mall_id": "634418221321199"
    },
    {
      "mall_id": "634418221704901"
    },
    {
      "mall_id": "634418213167233"
    }
  ]
}...
2025-06-19 16:55:00,441 [INFO] 获取到的全部mall_id数量: 4
2025-06-19 16:55:00,441 [INFO] 去重后的mall_id数量: 4
2025-06-19 16:55:00,441 [INFO] 去除了 0 个重复的mall_id
2025-06-19 16:55:00,441 [INFO] 最终使用的商店ID列表: ['634418213167233', '634418221704901', '634418221321199', '634418212233370']
2025-06-19 16:55:00,441 [INFO] 
开始处理第 1 批商店，共 4 个
2025-06-19 16:55:00,442 [INFO] 
==================================================
2025-06-19 16:55:00,442 [INFO] 正在处理商店 1/4: mall_id 634418213167233
2025-06-19 16:55:00,442 [INFO] ==================================================
2025-06-19 16:55:00,442 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418213167233
2025-06-19 16:55:00,442 [INFO] 随机延迟 1.17 秒
2025-06-19 16:55:01,615 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-06-19 16:55:01,615 [INFO] 成功加载cookie，包含 20 个条目
2025-06-19 16:55:01,615 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-06-19 16:55:02,148 [INFO] Temu API状态码: 429
2025-06-19 16:55:02,148 [ERROR] 获取mall_id为634418213167233的Temu数据时出错: 429
2025-06-19 16:55:02,148 [ERROR] 返回内容: {"error_code":406008,"error_msg":""}...
2025-06-19 16:55:02,149 [INFO] 由于API错误，跳过mall_id为634418213167233的商店
2025-06-19 16:55:02,150 [INFO] 
==================================================
2025-06-19 16:55:02,150 [INFO] 正在处理商店 2/4: mall_id 634418221704901
2025-06-19 16:55:02,150 [INFO] ==================================================
2025-06-19 16:55:02,151 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418221704901
2025-06-19 16:55:02,151 [INFO] 随机延迟 1.50 秒
2025-06-19 16:55:03,650 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-06-19 16:55:03,650 [INFO] 成功加载cookie，包含 20 个条目
2025-06-19 16:55:03,650 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-06-19 16:55:04,158 [INFO] Temu API状态码: 429
2025-06-19 16:55:04,158 [ERROR] 获取mall_id为634418221704901的Temu数据时出错: 429
2025-06-19 16:55:04,158 [ERROR] 返回内容: {"error_code":406008,"error_msg":""}...
2025-06-19 16:55:04,159 [INFO] 由于API错误，跳过mall_id为634418221704901的商店
2025-06-19 16:55:04,159 [INFO] 
==================================================
2025-06-19 16:55:04,160 [INFO] 正在处理商店 3/4: mall_id 634418221321199
2025-06-19 16:55:04,160 [INFO] ==================================================
2025-06-19 16:55:04,160 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418221321199
2025-06-19 16:55:04,160 [INFO] 随机延迟 1.26 秒
2025-06-19 16:55:05,418 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-06-19 16:55:05,418 [INFO] 成功加载cookie，包含 20 个条目
2025-06-19 16:55:05,418 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-06-19 16:55:06,191 [INFO] Temu API状态码: 429
2025-06-19 16:55:06,191 [ERROR] 获取mall_id为634418221321199的Temu数据时出错: 429
2025-06-19 16:55:06,191 [ERROR] 返回内容: {"error_code":406008,"error_msg":""}...
2025-06-19 16:55:06,192 [INFO] 由于API错误，跳过mall_id为634418221321199的商店
2025-06-19 16:55:06,192 [INFO] 
==================================================
2025-06-19 16:55:06,192 [INFO] 正在处理商店 4/4: mall_id 634418212233370
2025-06-19 16:55:06,192 [INFO] ==================================================
2025-06-19 16:55:06,192 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418212233370
2025-06-19 16:55:06,192 [INFO] 随机延迟 2.70 秒
2025-06-19 16:55:08,892 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-06-19 16:55:08,892 [INFO] 成功加载cookie，包含 20 个条目
2025-06-19 16:55:08,892 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-06-19 16:55:09,517 [INFO] Temu API状态码: 429
2025-06-19 16:55:09,518 [ERROR] 获取mall_id为634418212233370的Temu数据时出错: 429
2025-06-19 16:55:09,518 [ERROR] 返回内容: {"error_code":406008,"error_msg":""}...
2025-06-19 16:55:09,519 [INFO] 由于API错误，跳过mall_id为634418212233370的商店
2025-06-19 16:55:09,519 [INFO] 
==================================================
2025-06-19 16:55:09,519 [INFO] Temu数据抓取汇总
2025-06-19 16:55:09,519 [INFO] 处理时间: 2025-06-19 16:55:09
2025-06-19 16:55:09,520 [INFO] 处理商店总数: 4
2025-06-19 16:55:09,520 [INFO] 成功保存商店数: 0
2025-06-19 16:55:09,520 [INFO] 处理产品总数: 0
2025-06-19 16:55:09,520 [INFO] 成功保存产品数: 0
2025-06-19 16:55:09,520 [INFO] ==================================================
