import{w as e,x as t,y as n,z as o,A as i,B as s,C as a,D as r,E as l,F as c,G as d,H as u,I as h,X as m,J as f,K as b,L as g,h as p,f as v,l as k,M as x,N as I,O as w,P as y,Q as M,R as T,S as _,U as E,V as L,W as C,Y as U,i as D}from"./util-86a8139d.js";import{o as S,a as P,t as B,r as R,c as A,b as V,i as W,d as q,e as G,f as H}from"./tool-13238bfa.js";import{s as F,f as $}from"./stat-481b0a88.js";import{a as O}from"./index-9000aff5.js";const N={type:"black",domains:["v.youku.com","iqiyi.com","v.qq.com","ixigua.com","weibo.com","cctv.com","huya.com","douyu.com","bilibili.com","bilibili.to","163.com","youdao.com","zhihu.com","haokan.baidu.com","video.sina.com.cn","zjstv.com","qq.com","64memo.com","64tianwang.com","asp.fgmtv.org","bannedbook.net","bannedbook.org","beijingzx.org","china21.org","dongtaiwang.com","epochtimes.com","falundafa.org","falundafamuseum.org","fgmtv.org","hrichina.org","internetfreedom.org","maxtv.cn","mhradio.org","minghui.org","mingjingnews.com","ntdtv.com","rfa.org","secretchina.com","tuidang.org","publicdbhost.dmca.gripe","ai-course.cn","mingpao.com","xiaohongshu.com","douyin.com","le.com","pptv.com","mgtv.com","migu.cn","fun.tv","sohu.com","kuaishou.com"],black_list:["*.aliyundrive.com"]},j=()=>{let m,f={...N};f.bigModelDebug=!1;let b=!1,g=!1;const p=chrome.i18n.getMessage("context_title");let v=new Set(f.black_list),k=f.black_list,x=f.domains;const I=e=>{g=e&&!0===e.isInstallThunderKey},w=()=>{m=null},y=e=>{m=chrome.runtime.connectNative("com.thunder.chrome.host"),m.onMessage.addListener(I),m.onDisconnect.addListener(w);const t=navigator.userAgent;m.postMessage({ua:t,...e})};chrome.runtime.onMessage.addListener((s,a,r)=>{if(s.name===S.xl_call_function)switch(s.method){case P.startThunder:y(s);break;case P.getWebsiteDomains:r({websiteDomains:x});break;case P.addBlackListWebsite:T(s),e(B.PAGE_DISABLE,s.args[1]),t(c.websiteBlacklistArr,k);break;case P.removeBlackListWebsite:_(s),e(B.ENABLE,s.args[1]),t(c.websiteBlacklistArr,k);break;case P.trackEvent:F.apply(null,s.args)}else{if(s.name===S.CheckEnabled)return chrome.runtime.sendNativeMessage("com.thunder.chrome.host",{commandkey:"1"},e=>{g=e&&!0===e.isInstallThunderKey,r({websiteBlacklist:k,isInstallThunder:g})}),!0;if(s.name===S.xl_download){const{linkUrl:e,refererUrl:t,from:i}=s;n({url:a.tab.url}).then(n=>{y({linkurl:e,pageurl:t,cookie:n})}),fetch(e,{method:"HEAD"}).then(n=>{const s=(n.headers.get("Content-Length")/1024/1024).toFixed(2);F(1022,923,"value1="+encodeURIComponent(t||"")+"&value5="+s+"&value6="+encodeURIComponent(e||"")+"&value7="+i+"&value8="+o(e||""))})}else if(s.name===S.xl_video_show){const{videoSrc:e}=s;fetch(e,{method:"HEAD"}).then(t=>{const n=(t.headers.get("Content-Length")/1024/1024).toFixed(2);F(1022,922,"value1="+encodeURIComponent(a.tab.url||"")+"&value5=true&value6="+n+"&value7="+encodeURIComponent(e||"")+"&value8=0&value9="+o(e))})}else if(s.name===S.xl_sniff_video_info)f.isStat&&F(1022,935,"value1="+encodeURIComponent(a.tab.url)+"&value2="+s.videoType+"&value5="+s.fileUrlSuffix+"&value6="+s.videoDuration+"&value7="+s.videoSrc);else if("xl_stat"===s.name){const e={value1:encodeURIComponent(a.tab.url)},{eventId:t,extParam:n={}}=s;$(1022,t,{...e,...n})}else if(s.name===S.xl_install_thunder)return i(R,"XunLeiWebSetup_extrecall.exe"),!0}}),chrome.downloads.onChanged.addListener((function(e){e.state&&e.state.current,e.totalBytes&&e.totalBytes.current})),chrome.downloads.onDeterminingFilename.addListener(async e=>{if(v.has(e.referrer)||b||!g||!s(e.referrer)||a(e.finalUrl))return;if(""!==e.referrer&&"about:blank"!==e.referrer||r({active:!0,currentWindow:!0},e=>{e&&(""!==e.url&&"about:blank"!==e.url||chrome.tabs.remove(e.id))}),e.fileSize<2097152)return;(e=>{const{id:t}=e;chrome.downloads.cancel(t,(function(){chrome.downloads.erase({id:t},(function(){}))}))})(e),F(1022,918,"value5="+encodeURIComponent(e.finalUrl));const t=await n({url:e.referrer});y({linkurl:e.finalUrl,pageurl:e.referrer,filename:e.filename,cookie:t})}),chrome.contextMenus.onClicked.addListener(async e=>{const t=e.linkUrl?e.linkUrl:e.srcUrl,o=await n({url:e.pageUrl});y({linkurl:t,pageurl:e.pageUrl,cookie:o})}),chrome.tabs.onActivated.addListener(e=>{r({active:!0,currentWindow:!0},e=>{e&&E(e)})}),chrome.tabs.onUpdated.addListener((e,t,n)=>{"complete"===t.status&&E(n)});const M=async()=>{(async()=>{k=await h("websiteBlacklistArr"),v=new Set(k)})(),f=await d(),f.black_list.forEach(e=>{v.add(e)}),k=Array.from(v),x=f.domains,t(c.websiteBlacklistArr,k)},T=e=>{b=!0;const t=e.args[0];v.add(t),k=Array.from(v)},_=e=>{b=!1;const t=e.args[0];v.delete(t),k=Array.from(v)},E=t=>{if(!s(t.url))return;const n=l.exec(t.url)[0];v.has(n)?(e(B.PAGE_DISABLE,t.id),b=!0):(e(B.ENABLE,t.id),b=!1)};(async()=>{(async()=>{try{const e=await u("com.thunder.chrome.host",{commandkey:"1"});if(g=e&&!0===e.isInstallThunderKey,!g)return void F(1022,919);F(1022,916,"value1=1")}catch(e){g=!1,F(1022,919)}})(),chrome.contextMenus.create({id:"down_menu",type:"normal",title:p,contexts:["link","image"]}),M(),F(1022,920)})()};function z(){this.listeners=[],this.valid=!1}z.prototype={isThunderXSupportWatch:function(e,t){const n=this;m.postMessage("GetThunderInfo",[],void 0,(function(o,i){let s="";o&&(s=i[0].thunderVersion);let a=!1;s.length>0&&f(s,"10.1.24.578")>=0&&(a=!0),n.valid=a,"function"==typeof t&&t.apply(e,[a])}))},setBHOConfig:function(e,t,n){if(!this.valid)return;for(let o=0;o<this.listeners.length;++o)this.listeners[o].section===e&&this.listeners[o].key===t&&(this.listeners[o].currentValue=n);b({url:"http://127.0.0.1:5021/setbhoconfig?section="+e+"&key="+t+"&value="+n,type:"GET",success:function(e){},error:function(e){}})},setConfig:function(e,t,n){if(!this.valid)return;b({url:"http://127.0.0.1:5021/setconfig",type:"POST",data:{section:e,key:t,value:n},success:function(e){},error:function(e){}})},query:function(e,t,n){const o="http://127.0.0.1:5021/getbhoconfig?section="+e.section+"&key="+e.key;b({url:o,type:"GET",success:function(n){if(n&&0===n.code&&n.value&&n.value!==e.currentValue){const t=e.currentValue;e.currentValue=n.value,e.cb.apply(e.l,[n.value,t])}"function"==typeof t&&t(n)},error:function(e){"function"==typeof n&&n(e)}})},onQueryTimer:function(){this.queryResultCount=0,this.queryCount=this.listeners.length;const e=this;for(let t=0;t<this.listeners.length;++t){const n=this.listeners[t];this.query(n,(function(){e.queryResultCount++,e.queryCount===e.queryResultCount&&e.setQueryTimer()}),(function(){e.queryResultCount++,e.queryCount===e.queryResultCount&&e.setQueryTimer()}))}},setQueryTimer:function(){do{if(this.listeners.length<=0)break;if(this.timer)break;var e=this;this.timer=setTimeout((function(){e.onQueryTimer(),e.timer=null}),5e3)}while(0)},addWatch:function(e,t,n,o,i){if(this.valid){for(let i=0;i<this.listeners.length;++i)if(this.listeners[i].l===e&&this.listeners[i].cb===t&&this.listeners[i].section===n&&this.listeners[i].key===o)return;this.listeners.push({l:e,cb:t,section:n,key:o,currentValue:i}),this.setQueryTimer()}},updateWatchValue:function(e,t,n,o,i){for(let s=0;s<this.listeners.length;++s)if(this.listeners[s].l===e&&this.listeners[s].cb===t&&this.listeners[s].section===n&&this.listeners[s].key===o){this.listeners[s].currentValue=i;break}},removeWatchValue:function(e,t,n,o){for(let i=this.listeners.length-1;i>=0;--i)this.listeners[i].l!==e||this.listeners[i].cb!==t||n&&this.listeners[i].section!==n||o&&this.listeners[i].key!==o||this.listeners.splice(i,1);this.listeners.length<=0&&this.timer&&(clearTimeout(this.timer),this.timer=null)},init:function(){this.isThunderXSupportWatch()}};const X=new z,Q="https://sl-m-ssl.xunlei.com/entry/browser-plugin",K=async e=>Promise.resolve({activity:{name:"迅雷插件用户回归",start_unix:1692332896,end_unix:1697731199,status:3},game_depend:{status:1}}),J=async(e,t)=>{const n={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:e,event:"xlppc-fluentplay-website-block",title:t})};try{const e=await(o=15e3,(e,t)=>{const n=new AbortController;return(t=t||{}).signal=n.signal,setTimeout(()=>{n.abort()},o),fetch(e,t)})("https://api-shoulei-ssl.xunlei.com/xlppc.blacklist.api/v1/check",n),{data:t}=await e.json();return"accept"===t.result}catch(i){return"AbortError"===i.name||i.message.includes("abort")}var o};function Y(){this.headers={},this.headers["user-agent"]="",this.headers.referer="",this.headers.cookie="",this.headers["content-type"]="",this.headers["content-disposition"]="",this.headers.host="",this.headers["content-length"]=0,this.headers["access-control-allow-origin"]="",this.url="",this.fileName="",this.ext="",this.postData="",this.tabId=void 0}async function Z(e,t){const o=e.linkUrl||e.srcUrl,i=await n({url:o}),s=new Y;s.url=o,s.headers.cookie=i,s.headers.referer=e.pageUrl,async function(e){let t=e.headers.referer||"";t=t.concat("#@$@#"),t=t.concat(1,"#@$@#"),t=t.concat(e.url,"#@$@#"),t=t.concat(e.fileName,"#@$@#"),t=t.concat(navigator.userAgent,"#@$@#"),t=e.headers.cookie.length>0?t.concat(e.headers.cookie,"#@$@#"):t.concat("","#@$@#");t=t.concat("","#@$@#"),t=e.headers["content-length"]&&e.headers["content-length"].length?t.concat(e.headers["content-length"],"#@$@#"):t.concat("","#@$@#");$(1022,918,{value5:encodeURIComponent(e.url||"")}),m.postMessage("DownLoadByThunder",[t])}(s)}const ee=()=>{const e=new Map,t=t=>{(function(e){const t=null==e?void 0:e.responseHeaders.filter(e=>"content-type"===e.name.toLowerCase())[0];if(void 0===t)return!1;const n=t.value.toLowerCase();return!(!(null==e?void 0:e.url.includes(".m3u8"))||(null==e?void 0:e.url.includes("stat.download.xunlei.com"))||!O.includes(n))})(t)&&t.tabId>=0&&chrome.tabs.get(t.tabId,n=>{const o={url:t.url,type:t.type,tabId:t.tabId,pageUrl:(null==n?void 0:n.url)||"未知页面",timestamp:(new Date).toISOString(),isM3U8Video:!0};e.set(n.url,t.url),chrome.tabs.sendMessage(t.tabId,{action:"newM3u8Request",data:o})})};function s(){this.pluginEnabled=!0,this.exception=!1,this.exceptionTimerId=void 0,this.blackListPageArray=[],this.blackListWebsiteArray=[],this.videoConfigGetted=!1,this.videoConfigs=null,this.alwaysIgnoreList=[],this.monitorVideo=!0,this.isShortcutEnable=!0,this.limitSizeInfo=void 0,this.monitorEmule=!1,this.monitorMagnet=!1,this.monitorTradition=!1,this.monitorIE=!1,this.enabledCapture=!0,this.monitorDomains="",this.filterDomains="",this.monitorFileExts="",this.bUseChromeDownloadAPI=!!chrome.downloads,this.isShowRecallInfo=!1,this.isHiddenRecallBadge=!0,this.recallTimer=null,this.webPeerId="",this.triggerNotificationsTabId=null,this.backgroundPageExport={trackEvent:(e,t,n)=>{this.trackEvent(e,t,n)},onFeedback:()=>{this.feedback()},setPluginEnabled:e=>{this.setPluginEnabled(e)},startThunder:()=>{m.postMessage("DownLoadByThunder",[])},removeBlackListPage:(e,t)=>{this.removeBlackListPage(e,t)},removeBlackListWebsite:(e,t,n,o)=>{this.removeBlackListWebsite(e,t,n,o)},addBlackListPage:(e,t)=>{this.addBlackListPage(e,t)},addBlackListWebsite:(e,t)=>{this.addBlackListWebsite(e,t)},isException:()=>this.exception,isPluginEnabled:()=>this.pluginEnabled,isVideoMonitor:()=>this.monitorVideo,setMonitorVideoTags:e=>{this.setMonitorVideoTags(e)},isMultiSelectShortcutEnable:()=>this.isShortcutEnable,setMultiSelectShortcutEnable:e=>{this.setMultiSelectShortcutEnable(e)},setMonitorFileExts:e=>{this.notifyThunderMonitorFileExts(e)},getMonitorFileExts:()=>this.monitorFileExts,getLimitSizeInfo:()=>this.limitSizeInfo,setLimitSizeInfo:(e,t)=>{this.setLimitSizeInfo(e,t)},isUseChromeDownloadAPI:()=>this.bUseChromeDownloadAPI,isMonitorDomain:e=>this.isMonitorDomain(e),checkIsPageInUserBlackList:e=>this.checkIsPageInUserBlackList(e),enterMultiDownload:(e,t)=>{this.enterMultiDownload(e,t)}}}s.prototype={requestItems:{},blockDownload:!1,tabUrls:{},currentTabId:void 0,m3u8VideoMap:new Map,isValidDownload:function(e){let t="";const n=e.headers["content-disposition"];if(n.length>0&&(t=g(n)),0===t.length&&(t=p(e.url)),0===t.length)return!1;e.fileName=t;const o=e.headers["content-type"];if(-1!==o.indexOf("text/")&&(-1===o.indexOf("text/multipart")||0===t.length))return!1;const i=v(t);return e.ext=i,!!this.canDownload(e)&&this.isMonitorFileExt(i)},allowPromptThunder:function(e){e&&chrome.storage.local.get(t=>{t.xl_prompt_close&&e(!1);let n=t.xl_prompt_limit_size;n&&!isNaN(n)||(n=104857600),n=Number(n),e(!0,n)})},tryThunderGuide:function(e){do{let t=e.url;if(!t)break;this.allowPromptThunder((function(n,o){if(n){t=t.toLowerCase();let n="";if(0===t.indexOf("http://")||0===t.indexOf("https://")){const t=parseInt(e.headers["content-length"]);t&&t>o&&(n="当前文件过大，建议安装迅雷，启用高速下载")}if(n){const t=e.tabId;chrome.tabs.sendMessage(t,{name:"ThunderSupportReminder",text:n})}}}))}while(0)},updateContextMenu:function(e){chrome.contextMenus.update("ThunderContextMenu",{enabled:e},(function(){chrome.runtime.lastError})),chrome.contextMenus.update("ThunderContextMenu_MultiDownload",{enabled:e},(function(){chrome.runtime.lastError}))},updateToolbarBadgeText:function(e,t){const n={text:e,tabId:t};chrome.action.setBadgeBackgroundColor({color:[0,0,0,0]}),chrome.action.setBadgeText(n)},showRecallBadge:function(e,t){const n={text:e,tabId:t};chrome.action.setBadgeBackgroundColor({color:"#dd0c02"}),chrome.action.setBadgeText(n)},updateToolbarTips:function(e,t){const n={title:e,tabId:t};chrome.action.setTitle(n)},updateBrowserActionIcon:function(e,t){const n={path:e,tabId:t};chrome.action.setIcon(n)},setToolBarStatus:function(e,t){this.updateBrowserActionIcon(e.icon,t),this.updateToolbarTips(e.tips,t),this.updateToolbarBadgeText(e.badgeText,t),!this.isHiddenRecallBadge&&this.isShowRecallInfo&&this.showRecallBadge("1",t)},invokeThunder:async function(e){k.info("开始下载前准备",JSON.stringify(e));let t=e.headers.referer||"";t=t.concat("#@$@#"),t=t.concat(1,"#@$@#"),t=t.concat(e.url,"#@$@#"),t=t.concat(e.fileName,"#@$@#"),t=t.concat(navigator.userAgent,"#@$@#"),t=e.headers.cookie.length>0?t.concat(e.headers.cookie,"#@$@#"):t.concat("","#@$@#"),t=t.concat("","#@$@#"),t=e.headers["content-length"]&&e.headers["content-length"].length?t.concat(e.headers["content-length"],"#@$@#"):t.concat("","#@$@#"),F(1022,918,"value5="+encodeURIComponent(e.url||"")),k.warn("即将开始下载 invokeThunder1",t);(await x()).nativeVersion,k.warn("不支持传递下载回调"),m.postMessage("DownLoadByThunder",[t])},handleM3U8HttpRequest(e,t,n){const o=e.responseHeaders.filter(e=>"content-type"===e.name.toLowerCase())[0];if(void 0===o)return;const i=o.value.toLowerCase();!e.url.includes(".m3u8")||e.url.includes("stat.download.xunlei.com")||"application/vnd.apple.mpegurl"!==i&&"application/x-mpegurl"!==i||n.m3u8VideoMap.set(t.tab.url,e.url)},downloadByThunder:function(e,t,n){if(t.headers.referer&&0!==t.headers.referer.length)this.invokeThunder(t);else if(void 0===e||e<0)this.invokeThunder(t);else{const o=this;this.getHrefById(e,e=>{t.headers.referer=e,o.invokeThunder(t)},n)}},enumTabSetEnabled:function(e){const t=this;chrome.tabs.query({active:!0},(function(n){if(n)for(let o=0;o<n.length;o++){const i=n[o],s=t.isMonitorDomain(i.url),a=!t.checkIsPageInUserBlackList(i.url);chrome.tabs.sendMessage(i.id,{name:"UpdateWebsiteEnabled",enable:s}),chrome.tabs.sendMessage(i.id,{name:"UpdatePageEnabled",enable:a}),t.setToolbarStatus(t.exception,e,s,a,i.id)}})),t.exception},setPluginEnabled:function(e){this.notifyAllTabs({name:"UpdatePluginEnabled",enable:e,exception:this.exception}),this.pluginEnabled=e,this.enumTabSetEnabled(e),this.updateContextMenu(e),m.postMessage("SetPluginEnabled",[e])},notifyAllTabs:function(e){do{if(!e)break;chrome.windows.getAll({populate:!0},t=>{if(t)for(const n in t)for(const o in t[n].tabs){const i=t[n].tabs[o];i.id>=0&&chrome.tabs.sendMessage(i.id,e)}})}while(0)},queryTabs:function(e,t){chrome.tabs.query(e,(function(e){if(e)for(let n=0;n<e.length;n++)e[n].id>=0&&t(e[n])}))},setMonitorVideoTags:function(e){this.queryTabs({active:!0},(function(t){chrome.tabs.sendMessage(t.id,{name:"UpdateMoniterVideoTags",enable:e})})),this.monitorVideo=e,chrome.storage.local.set({video_monitor:e})},setMultiSelectShortcutEnable:function(e){this.queryTabs({active:!0},(function(t){chrome.tabs.sendMessage(t.id,{name:"UpdateMultiSelectShortcutEnable",enable:e})})),this.isShortcutEnable=e,chrome.storage.local.set({multi_select_shortcut_enable:e})},onAddBlackListPage:function(e,t,n){e&&t[0].retVal&&(this.queryTabs({active:!0},(function(e){chrome.tabs.sendMessage(e.id,{name:"UpdatePageEnabled",enable:!1})})),this.blackListPageArray[this.blackListPageArray.length]=n[0])},addBlackListPage:function(e,t){for(const n in this.blackListPageArray)if(this.blackListPageArray[n]===e)return;m.postMessage("AddBlackListPage",[e],this,this.onAddBlackListPage),this.setToolbarStatus(this.exception,this.pluginEnabled,!0,!1,t)},onRemoveBlackListPage:function(e,t,n){if(e&&t[0].retVal){this.queryTabs({active:!0},(function(e){chrome.tabs.sendMessage(e.id,{name:"UpdatePageEnabled",enable:!0})}));for(const e in this.blackListPageArray)this.blackListPageArray[e]===n[0]&&delete this.blackListPageArray[e]}},removeBlackListPage:function(e,t){for(const n in this.blackListPageArray)if(this.blackListPageArray[n]===e){m.postMessage("RemoveBlackListPage",[e],this,this.onRemoveBlackListPage),this.setToolbarStatus(this.exception,this.pluginEnabled,!0,!0,t);break}},notifyThunderMonitorSites:function(){m.postMessage("SetFilters",["MonitorDomain",this.monitorDomains])},notifyThunderMonitorFileExts:function(e){this.monitorFileExts=e,m.postMessage("SetFilters",["MonitorFileExt",this.monitorFileExts])},onAddOldBlackListWebsite:function(e,t,n){e&&t[0].retVal&&(this.blackListWebsiteArray[this.blackListWebsiteArray.length]=n[0])},addOldBlackListWebsite:function(e){for(const t in this.blackListWebsiteArray)if(this.blackListWebsiteArray[t]===e)return;m.postMessage("AddBlackListWebsite",[e],this,this.onAddOldBlackListWebsite)},addBlackListWebsite:function(e,t){this.isMonitorDomain(e)&&(this.addMonitorDomain(e)&&(this.notifyThunderMonitorSites(),this.addOldBlackListWebsite(e),this.queryTabs({active:!0},(function(e){chrome.tabs.sendMessage(e.id,{name:"UpdateWebsiteEnabled",enable:!1})}))),this.setToolbarStatus(this.exception,this.pluginEnabled,!1,!0,t))},onRemoveOldBlackListWebsite:function(e,t,n){if(e&&t[0].retVal)for(const o in this.blackListWebsiteArray)this.blackListWebsiteArray[o]===n[0]&&delete this.blackListWebsiteArray[o]},removeOldBlackListWebsite:function(e,t,n,o){for(const i in this.blackListWebsiteArray)if(this.blackListWebsiteArray[i]===e){m.postMessage("RemoveBlackListWebsite",[e],this,this.onRemoveOldBlackListWebsite);break}},removeBlackListWebsite:function(e,t,n,o){this.isMonitorDomain(e)||this.removeMonitorDomain(e)&&(this.notifyThunderMonitorSites(),this.removeOldBlackListWebsite(e,t,n,o),void 0===o&&(o=!this.checkIsPageInUserBlackList(t)),this.queryTabs({active:!0},(function(e){chrome.tabs.sendMessage(e.id,{name:"UpdateWebsiteEnabled",enable:!0})})),this.setToolbarStatus(this.exception,this.pluginEnabled,!0,o,n))},enterMultiDownload:function(e,t){chrome.tabs.sendMessage(e,{name:"EnterMultiSelect",tabId:e})},checkIsPageInUserBlackList:function(e){let t=!1;for(const n in this.blackListPageArray)if(e===this.blackListPageArray[n]){t=!0;break}return t},checkIsPageVideoEnable:function(e){let t=!1;if(!this.videoConfigs)return!1;switch(this.videoConfigs.type){case"disable":break;case"white":t=!(!this.videoConfigs.domains||!A(e,this.videoConfigs.domains));break;case"black":t=!this.videoConfigs.domains||!A(e,this.videoConfigs.domains)}return t},canDownload:function(e){const t=e.tabId,n=e.url;if(e.ext,!this.pluginEnabled)return!1;if(!this.enabledCapture)return!1;let o="";return o=this.tabUrls[t]&&""!==this.tabUrls[t]?this.tabUrls[t]:e.headers.referer||"",!!this.isMonitorDomain(o)&&(!!this.isIgnoreDomain(o)&&(!this.checkIsPageInUserBlackList(o)&&!!this.isMoniterUrl(t,n,o)))},isValidUrlAndMonitorProtocol:function(e){if(0===e.length)return!1;const t=e,n=t.indexOf(":");if(-1===n)return!1;const o=t.substr(0,n+1).toUpperCase();if(""===o)return!1;let i=!0;return-1!=="ED2K://".indexOf(o)?!1===this.monitorEmule&&(i=!1):-1!=="MAGNET:?".indexOf(o)?!1===this.monitorMagnet&&(i=!1):-1!=="HTTP://HTTPS://FTP://THUNDER://MMS://MMST://RTSP://RTSPU://XLAPP://".indexOf(o)?!1===this.monitorTradition&&(i=!1):i=!1,i},isIgnoreDomain:function(e){if(0===e.length)return!0;const t=V(e);if(""===t)return!0;if(!this.alwaysIgnoreList||0===this.alwaysIgnoreList.length)return!0;const n=[];for(const i in this.alwaysIgnoreList){let e=this.alwaysIgnoreList[i];0===e.indexOf("*.")&&(e=e.slice(2));const t=e.trimRight("|");n.push(t)}let o=!0;for(const i in n){const e=n[i];if(e.length>0&&-1!==t.indexOf(e)){o=!1;break}}return o},isMonitorDomain:function(e){const t=this.monitorDomains,n=W(e,t);return n||k.info(`referer: ${e} '未接管!!!`),n},isFilterDomain:function(e){if(0===e.length)return!1;if(0===this.filterDomains.length)return!1;const t=new Array,n=this.filterDomains.split("||");for(const s in n){const e=n[s].slice(2).toLowerCase().trimRight("|");t.push(e)}let o=!1;const i=e.toLowerCase();for(const s in t)if(t[s]>0&&-1!==i.indexOf(t[s])){o=!0;break}return o},getExtensionFileName:function(e){const t=e.replace(/(\\+)/g,"#").split("#"),n=t[t.length-1].split(".");return n[n.length-1]},isMonitorFileExt:function(e){let t=!1;return 0!==e.length&&(e=e.toLowerCase(),e+=";",-1!==this.monitorFileExts.indexOf(e)&&(t=!0),t)},isMoniterUrl:function(e,t,n){return 0!==t.length&&(!1!==this.monitorIE&&(!1!==this.isValidUrlAndMonitorProtocol(t)&&(0===n.length&&(n=t),!1!==this.isMonitorDomain(n)&&!this.isFilterDomain(n))))},setLimitSizeInfo:function(e,t){this.limitSizeInfo.enable=e,t&&!isNaN(t)&&(this.limitSizeInfo.size=parseInt(t)),chrome.storage.local.set({"take-over-limit-size-info":JSON.stringify(this.limitSizeInfo)})},removeMonitorDomain:function(e){let t=!1;if(0===e.length)return t;const n=V(e);if(""===n)return t;const o=new Array,i=this.monitorDomains.split("||");for(var s in i){let e=i[s];0===e.indexOf("*.")&&(e=e.slice(2));const t=e.trimRight("|");o.push(t)}for(s=0;s<o.length;++s)if(o[s].length>0&&-1!==n.indexOf(o[s])){i.splice(s,1),this.monitorDomains=i.join("||"),t=!0;break}return t},addMonitorDomain:function(e){if(0===e.length)return!1;const t=V(e);if(""===t)return!1;const n=new Array,o=this.monitorDomains.split("||");for(var i in o){0===(a=o[i]).indexOf("*.")&&(a=a.slice(2));const e=a.trimRight("|");n.push(e)}let s=!0;for(i=0;i<n.length;++i)if(n[i].length>0&&-1!==t.indexOf(n[i])){s=!1;break}if(s){var a="*."+t;this.monitorDomains=this.monitorDomains+"||"+a}return s},onIsDownloadURL:function(e,t,n){if(e)if(t[0].retVal){const e=new Y;e.url=n[0],e.headers.cookie=n[1],e.headers.referer=n[2],this.invokeThunder(e)}else window.open(n[0])},onBeforeSendHeaders:function(e){do{if(!I(e.type))break;let t=this.requestItems[e.requestId];t||(t=new Y,this.requestItems[e.requestId]=t),t.tabId=e.tabId;const n=e.url;t.url&&0!==t.url.length||(t.url=n);for(let o=0;o<e.requestHeaders.length;++o){const n=e.requestHeaders[o].name.toLowerCase(),i=e.requestHeaders[o].value;switch(n){case"user-agent":t.headers["user-agent"]=i;break;case"referer":t.headers.referer=i;break;case"cookie":t.headers.cookie=i;break;case"content-type":t.headers["content-type"]=i}}}while(0);return{}},onHeadersReceived:function(e){let t={info:()=>{},warn:()=>{},error:()=>{},fatal:()=>{},debug:()=>{}};null==t||t.info(`'HTTP 响应头接收事件', ${e.url},  ${e.type}`,e);do{const i=e.statusCode;if(i>=300&&i<400&&304!==i)break;if(0===e.statusLine.indexOf("HTTP/1.1 204 Intercepted by the Xunlei Advanced Integration"))break;const s=e.type;if(!I(s)){null==t||t.warn("不支持的类型",e.type),"image"===s&&chrome.tabs.get(e.tabId,t=>{const n=chrome.runtime.getURL("xl-images.html");t&&t.url&&0===t.url.indexOf(n)&&chrome.tabs.sendMessage(e.tabId,{name:"xlMultiPicUpdateDetail",value:{responseHeaders:e.responseHeaders,url:e.url}})});break}let a=e.url;var o=this.requestItems[e.requestId];o?delete this.requestItems[e.requestId]:(o=new Y).tabId=e.tabId;for(let t=0;t<e.responseHeaders.length;++t){const n=e.responseHeaders[t].name.toLowerCase(),i=e.responseHeaders[t].value;switch(n){case"referer":o.headers.referer=i;break;case"set-cookie":0===o.headers.cookie.length?o.headers.cookie=i:o.headers.cookie=o.headers.cookie+"; "+i;break;case"access-control-allow-origin":break;case"host":o.headers.host=i;break;case"content-disposition":o.headers["content-disposition"]=i;break;case"content-length":o.headers["content-length"]=i;break;case"content-type":o.headers["content-type"]=i}}if(0===a.length&&(a=host),o.url=a,!w(s)){null==t||t.warn(`${s} 不是支持的请求类型, url:${o.url}`),0===o.fileName.length&&(o.fileName=p(o.url));let e=v(o.fileName);if(0===e.length){const t=p(o.url);e=v(t)}const n=o.headers["content-type"];if(0===n.length&&!y(e)){null==t||t.info("onHeadersReceived isSupportMediaExt failed! ext:",e,", url:",o.url);break}if("swf"===e){null==t||t.warn("不拦截小于2M或swf格式 onHeadersReceived content-length:",` requestItem.headers['content-length'],\n             ', ext:',\n             ${e},\n             ', url:',\n             ${o.url},`);break}if(M(n)){null==t||t.info("onHeadersReceived isSupportContentType contentType:",n,", url:",o.url);break}break}if(2!==Math.round(e.statusCode/100)&&"other"===s){null==t||t.info("onHeadersReceived statusCode:",e.statusCode,", type:",s);break}if(!this.isValidDownload(o)){this.exception&&this.tryThunderGuide(o);break}parseInt(o.headers["content-length"])<2097152&&(null==t||t.info("不再拦截小于2M， 静默下载",o.headers["content-length"],", url:",o.url)),this.blockDownload=!0,k.info("准备用迅雷下载",o),o.headers.referer&&o.headers.cookie?this.downloadByThunder(e.tabId,o):chrome.tabs.get(e.tabId,async t=>{const i=t.openerTabId;if(!o.headers.referer&&t.url&&"about:blank"!==t.url&&(o.headers.referer=t.url),o.headers.cookie)this.downloadByThunder(e.tabId,o,i);else{const t=await n({url:o.url});o.headers.cookie=t,this.downloadByThunder(e.tabId,o,i)}})}while(0);return{}},getHrefById(e,t,n){chrome.tabs.get(n||e,e=>{t(e.url)})},onTabCreated:function(e){e.url?this.tabUrls[e.id]=e.url:e.openerTabId&&this.tabUrls[e.openerTabId]?this.tabUrls[e.id]=this.tabUrls[e.openerTabId]:this.tabUrls[e.id]=""},onTabActivated:function(e){chrome.tabs.sendMessage(e.tabId,{name:"OnActivated",tabId:e.tabId}),chrome.tabs.get(e.tabId,e=>{do{if(!e)break;if(!e.url){this.setToolbarStatus(this.exception,this.pluginEnabled,!0,!0,e.id);break}0!==e.url.indexOf("http://")&&0!==e.url.indexOf("https://")&&0!==e.url.indexOf("ftp://")&&this.setToolbarStatus(this.exception,this.pluginEnabled,!0,!0,e.id)}while(0)}),this.currentTabId=e.tabId},onTabRemoved:function(e,t){e in this.requestItems&&delete this.requestItems[e],e in this.tabUrls&&delete this.tabUrls[e]},onTabUpdated:function(e,t,n){t.url&&(this.tabUrls[e]=t.url)},onQueryAllTabs:function(e){if(e&&e.length>0)for(let t=0;t<e.length;++t)this.tabUrls[e[t].id]=e[t].url},cancelChromeDownload:function(e){chrome.downloads.cancel(e.id),chrome.downloads.erase({id:e.id},(function(e){})),""!==e.referrer&&"about:blank"!==e.referrer||this.queryTabs({active:!0,currentWindow:!0},(function(e){e&&(""!==e.url&&"about:blank"!==e.url||chrome.tabs.remove(e.id))}))},onDownloadCreated:function(e){if("complete"!==e.state&&"interrupted"!==e.state&&!(e.url&&this.noInterceptDownloadUrls&&this.noInterceptDownloadUrls.has(e.url)))if(e.id&&this.noInterceptDownloadIds&&this.noInterceptDownloadIds.has(e.id))this.noInterceptDownloadIds.delete(e.id);else if(this.blockDownload){this.blockDownload=!1,this.cancelChromeDownload(e);const t=function(){if(e.url){if(e.filename)return e.filename;const t=e.url.split("/");return t&&t.length>0&&t.pop()}return"迅雷下载支持"};T().then(n=>{n||_(e.url,t())}).catch(n=>{_(e.url,t())})}else;},registerRuntimeMessageListener:function(){chrome.webRequest.onHeadersReceived.addListener(t,{urls:["<all_urls>"]},["responseHeaders","extraHeaders"])},registerEventListener:function(){const t=this;chrome.runtime.onInstalled.addListener(e=>{const{reason:t}=e;"uninstall"!==t&&"update"!==t||chrome.storage.local.remove("xl_prompt_close"),"uninstall"===t&&chrome.storage.local.remove("isAgreementVisible")}),chrome.notifications.onClicked.addListener(()=>{chrome.tabs.sendMessage(this.triggerNotificationsTabId,{name:"xl_recall_entry_click",source:"notifications"})}),chrome.runtime.onMessage.addListener((function(s,a,r){var l,c,d;if("xl_mark_download_no_intercept"===s.name&&s.downloadId)return t.noInterceptDownloadIds.add(s.downloadId),setTimeout(()=>{t.noInterceptDownloadIds.delete(s.downloadId)},5e3),r({success:!0}),!0;if("xl_download"===s.name){const{title:e}=a.tab,{link:i,isM3U8Video:l,cookie:c,referurl:d,stat:u,isInIframe:h,videoUIVersion:b="",from:g=""}=s,p=e=>{switch(u){case"chrome_download_video":fetch(i,{method:"HEAD"}).then(t=>{const n=(t.headers.get("Content-Length")/1024/1024).toFixed(2);$(1022,923,{value1:encodeURIComponent(d||""),value5:n,value6:encodeURIComponent(i||""),value7:g,value8:o(i||""),value9:e,value11:b})}).catch(e=>{k.error("chrome_download_video",e)});break;case"chrome_download_other":$(1022,946,{value1:encodeURIComponent(d||""),value2:encodeURIComponent(i||""),value7:g,value11:b})}};return m.postMessage("GetThunderInfo",[],void 0,(async function(o,a,h){if(!o)return;const m=a[0].thunderVersion;if(-1===f(m,"12.0.6")&&l)return p("fail"),void r({errType:"version",text:"M3U8下载"});l&&(s.fileName=E(".m3u8",i,e));const b=new Y;b.url=i,s.fileName&&(b.fileName=s.fileName);let g=c;try{g=await n({url:b.url})}catch(v){}b.headers.cookie=g,b.headers.referer=d,b.headers.stat=u,t.invokeThunder(b),p("success")})),!0}if("xl_screen"===s.name){const{videoUIVersion:t="",type:n}=s;if("close"===n)return void $(1022,940,{value1:encodeURIComponent(a.tab.url),value11:t});if("init"===n)return!0;const o=e.get(a.tab.url),i=o||s.data.params.url;return $(1022,938,{value1:encodeURIComponent(a.tab.url),value5:encodeURIComponent(i||""),value7:"video_hover",value11:t}),m.postMessage("GetThunderInfo",[],void 0,(function(e,t,n){if(!e)return;const i=t[0].thunderVersion;if(-1===f(i,"11.4.2"))return F(1022,939,"value1=fail"),void r({errType:"version"});o&&(s.data.params.url=o),k.info("投屏 参数",s.data.params);const a=L(s.data);-1===f(i,"12.0.0")?chrome.tabs.create({url:a}):m.postMessage("DownLoadByThunder",[a]),F(1022,939,"value1=success")})),!0}if("xl_cloudadd"===s.name){const{title:e}=a.tab,t=a.tab.url;s.data.params.originUrl=t,s.data.params.referer=t,s.isM3U8Video&&(s.data.params.name=E(".m3u8",s.data.params.url,e));const n=s.data.params;return m.postMessage("GetThunderInfo",[],void 0,(async function(e,t,o){if(!e)return;const i=t[0].thunderVersion;if(-1===f(i,"12.0.0"))return void r({result:!1,errType:"version",text:n.isVideo?"流畅播":"存云盘"});const a=L(s.data);m.postMessage("DownLoadByThunder",[a]),r({result:!0})})),!0}if("xl_footer_show"===s.name)$(1022,945,{value1:encodeURIComponent(a.tab.url),value2:s.downloadShow,value5:s.playShow,value6:s.saveShow,value7:s.resourceList});else if("xl_footer_other_click"===s.name)F(1022,950,"value1="+s.clickId);else{if("xl_copy"===s.name){F(1022,949,"value1="+encodeURIComponent(a.tab.url)+"&value2="+encodeURIComponent(s.text));let e=!1;const t=a.tab.id;return chrome.scripting.executeScript({target:{tabId:t},args:[s],func:async function(e){try{await navigator.clipboard.writeText(e.text)}catch(t){throw new Error({error:t})}}},t=>{chrome.runtime.lastError&&(e=!1),e=!0,r({status:e})}),!0}if("VideoShow"===s.name){const t=e.get(a.tab.url),n=t||s.videoSrc,{videoUIVersion:i=""}=s;fetch(n,{method:"HEAD"}).then(e=>{const a=(e.headers.get("Content-Length")/1024/1024).toFixed(2);$(1022,922,{value1:encodeURIComponent(s.referurl||""),value5:s.hasDownload,value6:a,value7:encodeURIComponent(n||""),value8:t?1:0,value9:o(n),value10:"video_hover",value11:i})}).catch(e=>{k.error("VideoShow message",e)})}else if("EnabledCapture"===s.name)this.enabledCapture=s.capture;else if("CheckActivated"===s.name)chrome.tabs.query({url:s.url},(function(e){if(e)for(let t=0;t<e.length;t++){const n=e[t];n.active&&chrome.tabs.sendMessage(n.id,{name:"OnActivated",tabId:n.id})}}));else{if("CheckEnabled"===s.name){const e=t.pluginEnabled,n=t.monitorVideo,o=t.isShortcutEnable,i=t.isMonitorDomain(s.url),a=!t.checkIsPageInUserBlackList(s.url);return r({exception:t.exception,bPlugin:e,bMonitorVideo:n,bWebsite:i,bPage:a,bShortcutEnable:o,isShowRecallInfo:t.isShowRecallInfo}),s.topFrame&&t.setToolbarStatus(t.exception,t.pluginEnabled,i,a,s.tabId),!0}if("xl_check_url"===s.name)m.postMessage("IsDownloadURL",[s.link,s.cookie,s.referurl],t,t.onIsDownloadURL);else{if("GetConfig"===s.name){const e={...t.videoConfigs,bMonitorEmule:t.monitorEmule,bMonitorMagnet:t.monitorMagnet,bMonitorTradition:t.monitorTradition,bMonitorIE:t.monitorIE,monitorDomains:t.monitorDomains,filterDomains:t.filterDomains,monitorFileExts:t.monitorFileExts,jsqConfig:null==(l=t.videoConfigs)?void 0:l.jsq,videoTagVersion:(null==(d=null==(c=t.videoConfigs)?void 0:c.video_tag)?void 0:d.ui_version)||q};return r(e),!0}if("CheckVideoInWhiteList"===s.name)return m.postMessage("GetThunderInfo",[],void 0,(function(e,n,o){var i,l;let c=!1;if(e){const e=n[0].thunderVersion;c=-1===f(e,"12.0.0")}const d=t.checkIsPageVideoEnable(s.url),u=t.pluginEnabled,h=t.monitorVideo,m=t.isMonitorDomain(a.tab.url),b=(null==(i=t.videoConfigs)?void 0:i.fluent_play)||G,g=(null==(l=t.videoConfigs)?void 0:l.download_sniff)||H;r({exception:t.exception,videoInWhiteList:d,bPlugin:u,bMonitorVideo:h,bWebsite:m,isLess12Version:c,fluentPlayConfig:b,downloadSniffConfig:g})})),!0;if("xl_chrome_iframe_keydown"===s.name)chrome.tabs.sendMessage(a.tab.id,s);else if("xl_chrome_iframe_multi_hotkey"===s.name)chrome.tabs.sendMessage(a.tab.id,s);else if("xl_download_multi_start"===s.name)F(1022,924,"value1="+encodeURIComponent(s.referurl||""));else if("xl_download_multi"===s.name){const e=s.urls.length;if(F(1022,925,"value1="+encodeURIComponent(s.referurl||"")+"&value2="+e),e>0){const t="#@$@#";let o=s.referurl||"";o=o.concat(t),o=o.concat(e,t);let i=s.cookie;const a=e=>{const n=[];for(const i of s.urls)n.push(encodeURIComponent(i)),o=o.concat(i,t),o=o.concat("",t),o=o.concat(navigator.userAgent,t),o=o.concat(e,t),o=o.concat("",t),o=o.concat("",t);F(1022,918,"value5="+n.join(",")||""),m.postMessage("DownLoadByThunder",[o])};n({url:s.tabUrl}).then(e=>{a(i)}).catch(e=>{a(i)})}}else if("xl_prompt_click"===s.name){let e="";switch(s.action){case"install":e="download_thunder",F(1022,952,"value1="+encodeURIComponent(a.tab.url)+"&value5="+s.source);_("https://down.sandai.net/thunder11/XunLeiWebSetup_ext.exe","XunLeiWebSetup_ext.exe");break;case"close":e="close",chrome.storage.local.set({xl_prompt_close:!0});break;case"startThunder":t.onStartupThunder({linkUrl:"",pageUrl:""})}e&&"xl_reminder_install"===s.stat&&F(1022,929,"value2="+e)}else if("xl_prompt_show"===s.name)F(1022,928,"value1="+encodeURIComponent(a.tab.url));else{if("xl_prompt_enable"===s.name){var u=r;return t.allowPromptThunder((e,t)=>{u({enable:e})}),!0}if("xl_call_function"===s.name){u=r;const e=s.method;if(t.backgroundPageExport[e]){const n=t.backgroundPageExport[e].apply(null,s.args);return n&&n.then?n.then(e=>{u(e)}):u(n),!0}}else if("xl_sniff_video_info"===s.name)t.videoConfigs.isStat&&F(1022,935,"value1="+encodeURIComponent(a.tab.url)+"&value2="+s.videoType+"&value5="+s.fileUrlSuffix+"&value6="+s.videoDuration+"&value7="+s.videoSrc);else if("xl_stat"===s.name){const e={value1:encodeURIComponent(a.tab.url)},{eventId:t,extParam:n={}}=s;$(1022,t,{...e,...n})}else{if("xl_recall_entry_click"===s.name)return m.postMessage("GetThunderInfo",[],void 0,(async function(e,n,o){let i=0,l=0;if(e){i=1;const e=n[0].thunderVersion;if(-1===f(e,"12.0.0.2160"))r({errType:"version"});else{l=1;const e=L({opt:"web:open",params:{url:`${Q}?plugin_id=${t.webPeerId}`}});m.postMessage("DownLoadByThunder",[e])}}else _(R,"XunLeiWebSetup_extrecall.exe");const c=await K(t.webPeerId);t.recallTimer||(t.recallTimer=setTimeout(()=>{!async function(e){const n=await K();t.isShowRecallInfo=2===n.activity.status&&2===n.game_depend.status}(t.webPeerId),clearTimeout(t.recallTimer),t.recallTimer=null},6e5));const d=2===c.game_depend.status?0:1;t.isShowRecallInfo=2===c.activity.status&&2===c.game_depend.status,F(1022,954,"value1="+encodeURIComponent(a.tab.url)+"&value2="+s.source+"&value5="+i+"&value6="+l+"&value7="+d)})),!0;if("xl_install_thunder"===s.name)i(R,"XunLeiWebSetup_extrecall.exe"),F(1022,957,"value1="+encodeURIComponent(a.tab.url));else if("xl_show_action_error_dialog"===s.name)F(1022,951,"value1="+encodeURIComponent(a.tab.url)+"&value5="+s.source);else if("xl_show_recall_entry"===s.name)F(1022,953,"value1="+encodeURIComponent(s.url||a.tab.url)+"&value2="+s.source);else if("xl_show_recall_dialog"===s.name)F(1022,955,"value1="+encodeURIComponent(a.tab.url)+"&value2="+s.curIndex);else{if("xl_receive_vip"===s.name)return m.postMessage("GetThunderInfo",[],void 0,(function(e,n,o){if(!e)return F(1022,956,"value1="+encodeURIComponent(a.tab.url)+"&value2=fail"),void r({exception:!0});const i=n[0].thunderVersion;if(-1===f(i,"12.0.0.2160"))return F(1022,956,"value1="+encodeURIComponent(a.tab.url)+"&value2=fail"),void r({errType:"version",exception:!0});const s=L({opt:"web:open",params:{url:`${Q}?plugin_id=${t.webPeerId}`}});m.postMessage("DownLoadByThunder",[s]),r({exception:!1}),F(1022,956,"value1="+encodeURIComponent(a.tab.url)+"&value2=success")})),!0;if("xl_show_notifications"===s.name){if(!a.tab.id)return;t.triggerNotificationsTabId=a.tab.id,chrome.notifications.create({type:"basic",iconUrl:"images/extension_logo.png",title:"【限时】没套路！点击就送迅雷会员",message:"流畅播在线视频、高速下载、最高12T云盘"},e=>{chrome.runtime.lastError||F(1022,953,"value1="+encodeURIComponent(a.tab.url)+"&value2=notifications")})}else{if("xl_check_blacklist"===s.name){const{url:e,title:t}=a.tab;return J(e,t).then(e=>{r({isAccept:e})}),!0}if("xl_cloudadd_in_iframe"===s.name){const{id:e}=a.tab;chrome.scripting.executeScript({target:{tabId:e},args:[s],func:async function(e){const t={...e};t.name="xl_cloudadd",t.data.params.cookie=document.cookie,t.data.params.webTitle=document.title;const{ext:n,url:o}=t.data.params;".m3u8"===n&&(t.data.params.name=document.title?document.title+n:o.replace(/\?.*$/,"").replace(/.*\//,"")),chrome.runtime.sendMessage(t,e=>{if(!e)return;"version"===e.errType&&function(e){const t=(n="version","ncennffkjdiamlpmcbajkmaiiiddgioo-prompt"+n);var n;if(document.getElementById(t))return;const o=document.createElement("div");o.className="xly-dialog-prompt",o.id=t,o.innerHTML=`\n                <h2>${e}调用失败</h2>\n                <a action="close" href="javascript:;" class="xly-dialog-close" title="关闭"><i class="xl-icon-close"></i></a>\n                <p class="xly-dialog-prompt__text">客户端版本过低，无法启用${e}。</p>\n                <p class="xly-dialog-prompt__text">（升级提示：主菜单-检查更新）</p>\n                <div class="xly-dialog-prompt__footer">\n                  <div class="xly-dialog-prompt__button">\n                    <button action="close" class="td-button--other" style="width:65px; border-radius:4px;" >取消</button>\n                    <button action="startThunder" class="td-button">前往升级</button>\n                  </div>\n                </div>\n                `,document.body.appendChild(o),o.querySelectorAll("a, button").forEach(e=>{e.addEventListener("click",e=>{!function(e,t,n,o){if(!e.target||!t)return;const i=e.target.getAttribute("action");i&&(e.preventDefault(),chrome.runtime.sendMessage({name:"xl_prompt_click",action:i,stat:n,source:o}));document.body.removeChild(t)}(e,o)})})}(e.text);const{url:n,name:o,ext:i,isVideo:s}=t.data.params;chrome.runtime.sendMessage({name:"xl_cloudadd_stat",from:t.from,isSuccess:e.result,data:{url:n,fileName:o,suffix:i,isVideoURL:s}})})}})}else if("xl_show_toast"===s.name){const{id:e}=a.tab;chrome.scripting.executeScript({target:{tabId:e,tabId:e},args:[s],func:function(e){let t=document.querySelector(".xl-chrome-toast");t||(t=document.createElement("div"),t.className="xl-chrome-toast xl-chrome-toast--"+e.type,t.innerHTML+=`\n                  <div class="xl-chrome-toast-img"></div>\n                  <div class="xl-chrome-toast-text">${e.text}</div>\n                `,document.documentElement.appendChild(t)),window.xl_remove_toast_timer&&clearTimeout(window.xl_remove_toast_timer),window.xl_remove_toast_timer=setTimeout(()=>{t&&document.documentElement.removeChild(t)},3e3)}})}else if("xl_cloudadd_stat"===s.name){const{url:e,isVideoURL:t,suffix:n}=s.data;let o="value1="+encodeURIComponent(a.tab.url)+"&value5="+encodeURIComponent(e);if(t)return void fetch(e,{method:"HEAD"}).then(e=>{const t=(e.headers.get("Content-Length")/1024/1024).toFixed(2);o+=`&value2=${t}&value6=${s.from}&value7=${n.slice(1)}&value8=${".m3u8"===n?1:0}&value9=${s.isSuccess?"success":"fail"}`,F(1022,947,o)}).catch(e=>{});o+="&value6="+(s.isSuccess?"success":"fail"),F(1022,948,o)}else if("xl_download_stat"===s.name){const{url:e}=a.tab,{link:t,stat:n,status:i,videoUIVersion:r="",from:l=""}=s;switch(n){case"chrome_download_video":fetch(t,{method:"HEAD"}).then(n=>{const s=(n.headers.get("Content-Length")/1024/1024).toFixed(2);$(1022,923,{value1:encodeURIComponent(e||""),value5:s,value6:encodeURIComponent(t||""),value7:l,value8:o(t||""),value9:i,value11:r})});break;case"chrome_download_other":$(1022,946,{value1:encodeURIComponent(e||""),value2:encodeURIComponent(t||""),value7:l,value11:r})}}else if("xl_jsq_stat"===s.name){const{url:e}=a.tab,{eventId:t,type:n,jsqHomePage:o=""}=s;$(1022,t,{value1:n,value2:encodeURIComponent(o),tabUrl:encodeURIComponent(e||"")})}else{if("GetThunderInfo"===s.name)return m.postMessage("GetThunderInfo",[],void 0,(function(e,t,n){let o="",i="";e&&(o=t[0].peerId,i=t[0].thunderVersion),r({peerId:o,thunderVersion:i})})),!0;if("xl_check_installed"===s.name)return T().then(e=>{r({version:e})}),!0}}}}}}}}})),this.bUseChromeDownloadAPI?(k.info("拦截chrome downloads.onCreated"),chrome.downloads.onCreated.addListener((function(e){t.onDownloadCreated(e)}))):k.warn("chrome.downloads.onCreated 拦截失败"),chrome.webRequest&&(chrome.webRequest.onHeadersReceived.addListener((function(e){return t.onHeadersReceived(e)}),{urls:["<all_urls>"]},["responseHeaders"]),chrome.webRequest.onBeforeSendHeaders.addListener((function(e){return t.onBeforeSendHeaders(e)}),{urls:["<all_urls>"]},["requestHeaders"])),chrome.tabs&&(chrome.tabs.onCreated.addListener((function(e){return t.onTabCreated(e)})),chrome.tabs.onActivated.addListener((function(e){t.onTabActivated(e)})),chrome.tabs.onRemoved.addListener((function(e,n){t.onTabRemoved(e,n)})),chrome.tabs.onUpdated.addListener((function(e,n,o){t.onTabUpdated(e,n,o)})),chrome.tabs.query({},(function(e){t.onQueryAllTabs(e)})))},setToolbarStatus:async function(e,t,n,o,i){this.isHiddenRecallBadge=await h("isHiddenRecallBadge"),e?this.setToolBarStatus(B.EXCEPTION,i):t?n&&o?this.setToolBarStatus(B.ENABLE,i):this.setToolBarStatus(B.PAGE_DISABLE,i):this.setToolBarStatus(B.DISABLE,i)},onStartupThunder:async function(e,t){const o=await n({url:e.linkUrl}),i=new Y;i.url=e.linkUrl,i.headers.cookie=o,i.headers.referer=e.pageUrl,this.invokeThunder(i)},createContextMenu:function(e){var t,n,o;const i={id:"ThunderContextMenu",type:"normal",title:(null==(t=chrome.i18n)?void 0:t.getMessage)&&(null==(n=chrome.i18n)?void 0:n.getMessage("context_title"))||"使用迅雷下载",contexts:["link","audio","video"],enabled:e};chrome.contextMenus.create(i,(function(){chrome.runtime.lastError?k.error("Error creating child menu thunderMenu:",chrome.runtime.lastError):k.info("thunderMenu created successfully")}));const s={id:"ThunderContextMenu_MultiDownload",type:"normal",title:(null==(o=chrome.i18n)?void 0:o.getMessage)&&chrome.i18n.getMessage("multi_context_title")||"进入多选下载模式 (Shift+D)",contexts:["page"],enabled:e};chrome.contextMenus.create(s,(function(){chrome.runtime.lastError?k.error("Error creating multiDownloadMenu:",chrome.runtime.lastError):k.info("multiDownloadMenu created successfully")})),chrome.contextMenus.onClicked.addListener((e,t)=>{if("ThunderContextMenu"===e.menuItemId)Z(e);else if("ThunderContextMenu_MultiDownload"===e.menuItemId){const e=t.id;chrome.tabs.sendMessage(e,{name:"EnterMultiSelect",tabId:e})}})},onGetBlackListWebsites:function(e,t,n){if(e&&t[0].retVal){let e=!1;this.blackListWebsiteArray=t[1].blackList;for(const t in this.blackListWebsiteArray)this.isMonitorDomain(this.blackListWebsiteArray[t])&&this.addMonitorDomain(this.blackListWebsiteArray[t])&&(e=!0);e&&this.notifyThunderMonitorSites()}},onGetBlackListPages:function(e,t,n){e&&(t[0].retVal?this.blackListPageArray=t[1].blackList:this.blackListPageArray=[])},onGetIsMonitorProtocol:function(e,t,n){e&&t[0].retVal&&("MonitorEmule"===n[0]?this.monitorEmule=t[1].value:"MonitorMagnet"===n[0]?this.monitorMagnet=t[1].value:"MonitorTradition"===n[0]?this.monitorTradition=t[1].value:"MonitorIE"===n[0]&&(this.monitorIE=t[1].value))},onGetFiters:function(e,t,n){e&&t[0].retVal&&("MonitorDemain"===n[0]?(this.monitorDomains=t[1].value,this.onBrowserConfigMonitorDomainChange(this.monitorDomains),m.postMessage("GetBlackListWebsites",[],this,this.onGetBlackListWebsites)):"FilterDemain"===n[0]?this.filterDomains=t[1].value:"MonitorFileExt"===n[0]&&(this.monitorFileExts=t[1].value,this.onBrowserConfigMonitorExtendNamesChange(this.monitorFileExts)))},onBrowserConfigMonitorDomainChange:function(e){if(e){this.monitorDomains=e;const t=this;chrome.windows.getAll({populate:!0},(function(e){for(const n in e)for(const o in e[n].tabs)e[n].tabs[o].id>=0&&chrome.tabs.sendMessage(e[n].tabs[o].id,{name:"UpdateMonitorDomains",monitorDomains:t.monitorDomains})})),this.enumTabSetEnabled(this.pluginEnabled)}},onBrowserConfigMonitorExtendNamesChange:function(e){this.monitorFileExts=e;const t=chrome.runtime.getURL("options.html");chrome.windows.getAll({populate:!0},n=>{for(const o in n)for(const i in n[o].tabs){const s=n[o].tabs[i];s.id>=0&&s.url&&0===s.url.toLowerCase().indexOf(t.toLowerCase())&&chrome.tabs.sendMessage(s.id,{name:"UpdateMonitorFileExts",value:e})}})},onGetPluginEnabled:function(e,t,n){do{if(!e)break;this.pluginEnabled=t[0].retVal,k.warn(`${this.pluginEnabled?"启用":"禁用"}插件，当前异常: ${this.exception}`),this.notifyAllTabs({name:"UpdatePluginEnabled",enable:this.pluginEnabled,exception:this.exception}),this.updateContextMenu(this.pluginEnabled),this.pluginEnabled?this.setToolBarStatus(B.ENABLE):this.setToolBarStatus(B.DISABLE),X.init(),m.postMessage("GetBlackListPages",[],this,this.onGetBlackListPages),m.postMessage("GetFiters",["MonitorDemain"],this,this.onGetFiters),m.postMessage("GetFiters",["FilterDemain"],this,this.onGetFiters),m.postMessage("GetFiters",["MonitorFileExt"],this,this.onGetFiters),m.postMessage("GetIsMonitorProtocol",["MonitorEmule"],this,this.onGetIsMonitorProtocol),m.postMessage("GetIsMonitorProtocol",["MonitorMagnet"],this,this.onGetIsMonitorProtocol),m.postMessage("GetIsMonitorProtocol",["MonitorTradition"],this,this.onGetIsMonitorProtocol),m.postMessage("GetIsMonitorProtocol",["MonitorIE"],this,this.onGetIsMonitorProtocol),F(1022,916,"value1="+(this.pluginEnabled?"1":"0"))}while(0)},trackEvent:function(e,t,n){F(e,t,n)},feedback:function(){m.postMessage("GetThunderInfo",[],void 0,(function(e,t,n){let o="",i="";e&&(o=t[0].peerId,i=t[0].thunderVersion);const s="http://misc-xl9-ssl.xunlei.com/client/view/dist/1.0/feedback.html?version="+i+"&pid="+o;chrome.tabs.create({url:s},(function(){}))}))},getVideoConfigs:function(e){b({url:"http://static-xl.a.88cdn.com/json/xl_chrome_ext_config.json",type:"GET",success:function(t){e(0,t)},error:function(t){e(-1)}})},pollCheckNativeMessageConnected:function(){do{if(this.exceptionTimerId)break;this.exceptionTimerId=setInterval(async()=>{const e=m.connect(),t=await T();e&&t&&(clearInterval(this.exceptionTimerId),this.exceptionTimerId=void 0,this.exception=!1,m.postMessage("GetPluginEnabled",[],this,this.onGetPluginEnabled),C.call(this))},5e3)}while(0)},onDisconnect:function(e){e||(this.pluginEnabled=!1,this.exception=!0,this.enumTabSetEnabled(this.pluginEnabled),this.notifyAllTabs({name:"UpdatePluginEnabled",enable:this.pluginEnabled,exception:this.exception}),this.pollCheckNativeMessageConnected())},init:async function(){m.attachDisconnectEvent(this,this.onDisconnect);const e=m.connect(),t=await T();k.warn(""+(t?`迅雷版本：${t} - 插件版本 ${chrome.runtime.getManifest().version}`:"未安装迅雷客户端"));const n=this;this.createContextMenu(!1),e&&t?m.postMessage("GetPluginEnabled",[],this,this.onGetPluginEnabled)&&C.call(this):(k.error("插件不可用！！！"),this.exception=!0,this.pluginEnabled=!1,this.setToolBarStatus(B.EXCEPTION),F(1022,919)),n.webPeerId=await U("Q");const o=await K(n.webPeerId);2===o.activity.status&&2===o.game_depend.status&&(n.isShowRecallInfo=!0),this.registerEventListener(),this.registerRuntimeMessageListener(),this.getVideoConfigs((function(e,t){n.videoConfigGetted=!0,t&&t.black_list&&t.black_list instanceof Array&&(n.alwaysIgnoreList=t.black_list),n.videoConfigs=t})),F(1022,920)}};(new s).init()};D?j():ee();
