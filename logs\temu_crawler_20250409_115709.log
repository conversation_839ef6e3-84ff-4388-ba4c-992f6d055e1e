2025-04-09 11:57:09,674 [INFO] 日志系统初始化完成，日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250409_115709.log
2025-04-09 11:57:09,674 [INFO] ==================================================
2025-04-09 11:57:09,675 [INFO] 开始运行Temu数据抓取
2025-04-09 11:57:09,703 [INFO] 时间: 2025-04-09 11:57:09
2025-04-09 11:57:09,703 [INFO] 日志文件: C:\Users\<USER>\Documents\个人文档\dev\temupc\logs\temu_crawler_20250409_115709.log
2025-04-09 11:57:09,704 [INFO] ==================================================
2025-04-09 11:57:09,704 [INFO] 
获取商店ID列表...
2025-04-09 11:57:09,704 [INFO] 请求商店ID列表: http://172.25.165.28:8055/items/shop_data
2025-04-09 11:57:09,791 [INFO] 获取商店ID状态码: 200
2025-04-09 11:57:09,791 [INFO] 获取到的商店数据: {
  "data": [
    {
      "mall_id": "634418219240900"
    },
    {
      "mall_id": "634418210147170"
    },
    {
      "mall_id": "634418219240900"
    },
    {
      "mall_id": "634418210147170"
    }
  ]
}...
2025-04-09 11:57:09,792 [INFO] 获取到的全部mall_id数量: 4
2025-04-09 11:57:09,792 [INFO] 去重后的mall_id数量: 2
2025-04-09 11:57:09,793 [INFO] 去除了 2 个重复的mall_id
2025-04-09 11:57:09,793 [INFO] 以下mall_id有重复记录: {"634418219240900": 2, "634418210147170": 2}
2025-04-09 11:57:09,793 [INFO] 最终使用的商店ID列表: ['634418210147170', '634418219240900']
2025-04-09 11:57:09,793 [INFO] 
==================================================
2025-04-09 11:57:09,794 [INFO] 正在处理商店 1/2: mall_id 634418210147170
2025-04-09 11:57:09,794 [INFO] ==================================================
2025-04-09 11:57:09,794 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418210147170
2025-04-09 11:57:09,794 [INFO] 请求payload: {"mallId": "634418210147170", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418210147170", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-04-09 11:57:09,794 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-04-09 11:57:09,796 [INFO] 成功加载cookie，包含 19 个条目
2025-04-09 11:57:09,796 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-04-09 11:57:11,988 [INFO] Temu API状态码: 200
2025-04-09 11:57:11,989 [INFO] Temu返回数据结构: ['success', 'error_code', 'errorCode', 'result']
2025-04-09 11:57:11,990 [INFO] 成功获取到8件商品，尽管page_size设置为1
2025-04-09 11:57:11,990 [INFO] 找到'result'键，包含店铺数据
2025-04-09 11:57:11,990 [INFO] 成功获取到8件商品
2025-04-09 11:57:11,992 [INFO] 处理商店数据...
2025-04-09 11:57:11,992 [INFO] 处理后的商店数据: {"update_time": "2025-04-09 11:57:11", "mall_id": "634418210147170", "mall_name": "LN JEWELRY", "mall_logo": "https://img.kwcdn.com/supplier-public-tag/1e2331480c/cf3510fe-8e79-4443-bb8e-1b1cd13802cc_300x300.jpeg", "goods_sales_num": 300000, "goods_num": 80, "review_num": 8556}
2025-04-09 11:57:11,992 [INFO] 保存/更新商店数据: mall_id=634418210147170
2025-04-09 11:57:12,179 [INFO] 成功更新商店数据: 634418210147170
2025-04-09 11:57:12,288 [INFO] 成功记录商店变更日志: 634418210147170
2025-04-09 11:57:12,289 [INFO] 处理产品数据...
2025-04-09 11:57:12,290 [INFO] 找到8个产品
2025-04-09 11:57:12,291 [INFO] 价格转换: 430美分 -> 4.3美元
2025-04-09 11:57:12,291 [INFO] 处理后的产品数据 1: {"update_time": "2025-04-09 11:57:12", "mall_id": "634418210147170", "goods_id": "601099537002370", "title": "女士优雅珍珠项链和耳环套装 - 复古风格，人造珍珠多串设计，适合派对和日常佩戴，珍珠耳环", "image_url": "https://img.kwcdn.com/product/Fancyalgo/VirtualModelMatting/f4560af77c7bc254867c0b40e0978258.jpg", "sales_num": 9, "price": 4.3, "comment": 172}
2025-04-09 11:57:12,292 [INFO] 价格转换: 285美分 -> 2.85美元
2025-04-09 11:57:12,292 [INFO] 处理后的产品数据 2: {"update_time": "2025-04-09 11:57:12", "mall_id": "634418210147170", "goods_id": "601099800680066", "title": "两件式简约开口字母手链戒指套装，时尚优雅女士", "image_url": "https://img.kwcdn.com/product/fancy/2bd94dbe-9e53-42d6-87b8-dfadf3c0134e.jpg", "sales_num": 3, "price": 2.85, "comment": 59}
2025-04-09 11:57:12,293 [INFO] 价格转换: 685美分 -> 6.85美元
2025-04-09 11:57:12,293 [INFO] 价格转换: 558美分 -> 5.58美元
2025-04-09 11:57:12,293 [INFO] 价格转换: 488美分 -> 4.88美元
2025-04-09 11:57:12,294 [INFO] 价格转换: 221美分 -> 2.21美元
2025-04-09 11:57:12,294 [INFO] 价格转换: 271美分 -> 2.71美元
2025-04-09 11:57:12,294 [INFO] 价格转换: 166美分 -> 1.66美元
2025-04-09 11:57:12,294 [INFO] 为mall_id 634418210147170找到8个产品
2025-04-09 11:57:12,294 [INFO] 正在保存产品 1/8 (goods_id: 601099537002370)
2025-04-09 11:57:12,295 [INFO] 保存/更新产品数据: goods_id=601099537002370
2025-04-09 11:57:12,450 [INFO] 成功更新产品数据: 601099537002370
2025-04-09 11:57:12,525 [INFO] 成功记录产品变更日志: 601099537002370
2025-04-09 11:57:12,726 [INFO] 正在保存产品 2/8 (goods_id: 601099800680066)
2025-04-09 11:57:12,726 [INFO] 保存/更新产品数据: goods_id=601099800680066
2025-04-09 11:57:12,871 [INFO] 成功更新产品数据: 601099800680066
2025-04-09 11:57:12,933 [INFO] 成功记录产品变更日志: 601099800680066
2025-04-09 11:57:13,134 [INFO] 正在保存产品 3/8 (goods_id: 601099930381969)
2025-04-09 11:57:13,134 [INFO] 保存/更新产品数据: goods_id=601099930381969
2025-04-09 11:57:13,261 [INFO] 成功更新产品数据: 601099930381969
2025-04-09 11:57:13,343 [INFO] 成功记录产品变更日志: 601099930381969
2025-04-09 11:57:13,544 [INFO] 正在保存产品 4/8 (goods_id: 601099583493626)
2025-04-09 11:57:13,544 [INFO] 保存/更新产品数据: goods_id=601099583493626
2025-04-09 11:57:13,707 [INFO] 成功更新产品数据: 601099583493626
2025-04-09 11:57:13,767 [INFO] 成功记录产品变更日志: 601099583493626
2025-04-09 11:57:13,969 [INFO] 正在保存产品 5/8 (goods_id: 601099582902407)
2025-04-09 11:57:13,969 [INFO] 保存/更新产品数据: goods_id=601099582902407
2025-04-09 11:57:14,110 [INFO] 成功更新产品数据: 601099582902407
2025-04-09 11:57:14,178 [INFO] 成功记录产品变更日志: 601099582902407
2025-04-09 11:57:14,380 [INFO] 正在保存产品 6/8 (goods_id: 601099533654950)
2025-04-09 11:57:14,380 [INFO] 保存/更新产品数据: goods_id=601099533654950
2025-04-09 11:57:14,522 [INFO] 成功更新产品数据: 601099533654950
2025-04-09 11:57:14,591 [INFO] 成功记录产品变更日志: 601099533654950
2025-04-09 11:57:14,793 [INFO] 正在保存产品 7/8 (goods_id: 601099728000818)
2025-04-09 11:57:14,793 [INFO] 保存/更新产品数据: goods_id=601099728000818
2025-04-09 11:57:14,928 [INFO] 成功更新产品数据: 601099728000818
2025-04-09 11:57:15,002 [INFO] 成功记录产品变更日志: 601099728000818
2025-04-09 11:57:15,203 [INFO] 正在保存产品 8/8 (goods_id: 601099520116564)
2025-04-09 11:57:15,204 [INFO] 保存/更新产品数据: goods_id=601099520116564
2025-04-09 11:57:15,383 [INFO] 成功更新产品数据: 601099520116564
2025-04-09 11:57:15,457 [INFO] 成功记录产品变更日志: 601099520116564
2025-04-09 11:57:15,660 [INFO] 商店 634418210147170 处理完成: 成功保存 8/8 个产品
2025-04-09 11:57:16,661 [INFO] 
==================================================
2025-04-09 11:57:16,661 [INFO] 正在处理商店 2/2: mall_id 634418219240900
2025-04-09 11:57:16,661 [INFO] ==================================================
2025-04-09 11:57:16,661 [INFO] 请求Temu数据: https://www.temu.com/us-zh-Hans/api/bg/circle/c/mall/mallInfoWithGoodsList, mall_id: 634418219240900
2025-04-09 11:57:16,662 [INFO] 请求payload: {"mallId": "634418219240900", "mainGoodsIds": ["1"], "source_page_sn": "10013", "mall_id": "634418219240900", "main_goods_ids": ["1"], "filter_items": "", "page_number": 1, "page_size": 8, "list_id": "r7oe7gyw0vd5xo2z2qja2", "scene_code": "mall_rule", "page_sn": 10040, "page_el_sn": 201265, "source": 10018, "anti_content": "1"}
2025-04-09 11:57:16,662 [INFO] 尝试从 C:\Users\<USER>\Documents\个人文档\dev\temupc\cookie.json 加载cookie
2025-04-09 11:57:16,662 [INFO] 成功加载cookie，包含 19 个条目
2025-04-09 11:57:16,663 [INFO] 生成Temu请求头: {"content-type": "application/json;charset=UTF-8", "Cookie": "region=37; timezone=Asia%2FHong_Kong; ...
2025-04-09 11:57:17,821 [INFO] Temu API状态码: 200
2025-04-09 11:57:17,821 [INFO] Temu返回数据结构: ['success', 'error_code', 'errorCode', 'result']
2025-04-09 11:57:17,821 [INFO] 成功获取到8件商品，尽管page_size设置为1
2025-04-09 11:57:17,823 [INFO] 找到'result'键，包含店铺数据
2025-04-09 11:57:17,823 [INFO] 成功获取到8件商品
2025-04-09 11:57:17,824 [INFO] 处理商店数据...
2025-04-09 11:57:17,824 [INFO] 处理后的商店数据: {"update_time": "2025-04-09 11:57:17", "mall_id": "634418219240900", "mall_name": "HongYi TieYi", "mall_logo": "https://img.kwcdn.com/supplier-public-tag/1fad185920/66ed21e1-6994-4e43-84be-664e35e7d67b_300x300.jpeg", "goods_sales_num": 27000, "goods_num": 287, "review_num": 210}
2025-04-09 11:57:17,825 [INFO] 保存/更新商店数据: mall_id=634418219240900
2025-04-09 11:57:17,952 [INFO] 成功更新商店数据: 634418219240900
2025-04-09 11:57:18,030 [INFO] 成功记录商店变更日志: 634418219240900
2025-04-09 11:57:18,030 [INFO] 处理产品数据...
2025-04-09 11:57:18,031 [INFO] 找到8个产品
2025-04-09 11:57:18,031 [INFO] 价格转换: 651美分 -> 6.51美元
2025-04-09 11:57:18,031 [INFO] 处理后的产品数据 1: {"update_time": "2025-04-09 11:57:18", "mall_id": "634418219240900", "goods_id": "601099777440944", "title": "房间装饰 1 件优雅铁金属标志「把它交给上帝然后睡觉」- 家庭、办公室、卧室、露台、厨房、花园、商店、咖啡馆、舞厅、酒吧的励志墙艺术 - 乔迁、节日、生日、古董收藏家、不给糖就捣蛋、万圣节、圣诞节、可拆卸、非常适合房间装饰", "image_url": "https://img.kwcdn.com/product/fancy/c845fb99-968d-4517-967c-c3fba449b8bb.jpg", "sales_num": 7, "price": 6.51, "comment": 13}
2025-04-09 11:57:18,031 [INFO] 价格转换: 907美分 -> 9.07美元
2025-04-09 11:57:18,032 [INFO] 处理后的产品数据 2: {"update_time": "2025-04-09 11:57:18", "mall_id": "634418219240900", "goods_id": "601099779040677", "title": "1件优雅金属墙艺术“欢迎来到湖边” - 抽象万圣节与圣诞节设计，适用于办公室、家庭客厅、卧室、露台、厨房、花园、商店大厅、咖啡馆、舞厅、酒吧、乡村农舍装饰、乔迁礼物、节日、生日礼物、古董收藏家，重复使用，完美适用于房间装饰", "image_url": "https://img.kwcdn.com/product/fancy/8251355b-1b34-4d9b-bac4-154600ca9173.jpg", "sales_num": 947, "price": 9.07, "comment": 19}
2025-04-09 11:57:18,032 [INFO] 价格转换: 989美分 -> 9.89美元
2025-04-09 11:57:18,032 [INFO] 价格转换: 1304美分 -> 13.04美元
2025-04-09 11:57:18,032 [INFO] 价格转换: 1171美分 -> 11.71美元
2025-04-09 11:57:18,032 [INFO] 价格转换: 1018美分 -> 10.18美元
2025-04-09 11:57:18,032 [INFO] 价格转换: 799美分 -> 7.99美元
2025-04-09 11:57:18,033 [INFO] 价格转换: 1081美分 -> 10.81美元
2025-04-09 11:57:18,033 [INFO] 为mall_id 634418219240900找到8个产品
2025-04-09 11:57:18,033 [INFO] 正在保存产品 1/8 (goods_id: 601099777440944)
2025-04-09 11:57:18,033 [INFO] 保存/更新产品数据: goods_id=601099777440944
2025-04-09 11:57:18,332 [INFO] 成功更新产品数据: 601099777440944
2025-04-09 11:57:18,442 [INFO] 成功记录产品变更日志: 601099777440944
2025-04-09 11:57:18,644 [INFO] 正在保存产品 2/8 (goods_id: 601099779040677)
2025-04-09 11:57:18,644 [INFO] 保存/更新产品数据: goods_id=601099779040677
2025-04-09 11:57:18,840 [INFO] 成功更新产品数据: 601099779040677
2025-04-09 11:57:18,930 [INFO] 成功记录产品变更日志: 601099779040677
2025-04-09 11:57:19,132 [INFO] 正在保存产品 3/8 (goods_id: 601100365216996)
2025-04-09 11:57:19,133 [INFO] 保存/更新产品数据: goods_id=601100365216996
2025-04-09 11:57:19,259 [INFO] 成功更新产品数据: 601100365216996
2025-04-09 11:57:19,331 [INFO] 成功记录产品变更日志: 601100365216996
2025-04-09 11:57:19,533 [INFO] 正在保存产品 4/8 (goods_id: 601100572181244)
2025-04-09 11:57:19,533 [INFO] 保存/更新产品数据: goods_id=601100572181244
2025-04-09 11:57:19,690 [INFO] 成功更新产品数据: 601100572181244
2025-04-09 11:57:19,759 [INFO] 成功记录产品变更日志: 601100572181244
2025-04-09 11:57:19,960 [INFO] 正在保存产品 5/8 (goods_id: 601100565803631)
2025-04-09 11:57:19,960 [INFO] 保存/更新产品数据: goods_id=601100565803631
2025-04-09 11:57:20,101 [INFO] 成功更新产品数据: 601100565803631
2025-04-09 11:57:20,168 [INFO] 成功记录产品变更日志: 601100565803631
2025-04-09 11:57:20,369 [INFO] 正在保存产品 6/8 (goods_id: 601100570132226)
2025-04-09 11:57:20,369 [INFO] 保存/更新产品数据: goods_id=601100570132226
2025-04-09 11:57:20,551 [INFO] 成功创建新产品数据: 601100570132226
2025-04-09 11:57:20,630 [INFO] 成功记录产品变更日志: 601100570132226
2025-04-09 11:57:20,832 [INFO] 正在保存产品 7/8 (goods_id: 601099959057567)
2025-04-09 11:57:20,832 [INFO] 保存/更新产品数据: goods_id=601099959057567
2025-04-09 11:57:20,972 [INFO] 成功更新产品数据: 601099959057567
2025-04-09 11:57:21,044 [INFO] 成功记录产品变更日志: 601099959057567
2025-04-09 11:57:21,245 [INFO] 正在保存产品 8/8 (goods_id: 601100576981145)
2025-04-09 11:57:21,245 [INFO] 保存/更新产品数据: goods_id=601100576981145
2025-04-09 11:57:21,367 [INFO] 成功更新产品数据: 601100576981145
2025-04-09 11:57:21,445 [INFO] 成功记录产品变更日志: 601100576981145
2025-04-09 11:57:21,646 [INFO] 商店 634418219240900 处理完成: 成功保存 8/8 个产品
2025-04-09 11:57:22,647 [INFO] 
==================================================
2025-04-09 11:57:22,647 [INFO] Temu数据抓取汇总
2025-04-09 11:57:22,648 [INFO] 处理时间: 2025-04-09 11:57:22
2025-04-09 11:57:22,648 [INFO] 处理商店总数: 2
2025-04-09 11:57:22,649 [INFO] 成功保存商店数: 2
2025-04-09 11:57:22,649 [INFO] 处理产品总数: 16
2025-04-09 11:57:22,649 [INFO] 成功保存产品数: 16
2025-04-09 11:57:22,650 [INFO] ==================================================
